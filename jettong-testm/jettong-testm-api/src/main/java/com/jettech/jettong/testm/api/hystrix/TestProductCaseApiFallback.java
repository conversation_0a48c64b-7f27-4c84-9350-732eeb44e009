package com.jettech.jettong.testm.api.hystrix;

import com.jettech.jettong.testm.api.TestProductCaseApi;
import com.jettech.jettong.testm.api.TestReportApi;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.entity.TestProductCase;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 测试报告相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试报告相关API熔断
 * @package com.jettech.jettong.cmdb.hystrix
 * @className ProductApplicationFallback
 * @date 2022/08/08 09:54
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class TestProductCaseApiFallback implements TestProductCaseApi
{

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return Collections.emptyMap();
    }

    @Override
    public TestProductCase queryByTestCaseId(Long testCaseId) {
        return null;
    }

    @Override
    public List<TestProductCase> queryByFunctionIds(TestProductCasePageQuery data) {
        return Collections.emptyList();
    }

    @Override
    public List<TestProductCase> queryProductCaseByIds(List<Long> testCaseIds) {
        return Collections.emptyList();
    }

    @Override
    public List<TestProductCase> queryProductCaseByTaskId(List<Long> testCaseIds) {
        return Collections.emptyList();
    }

    @Override
    public List<TestProductCase> getProductCaseByFunctionIdAndTaskId(Long moduleFunctionId, Long taskId) {
        return Collections.emptyList();
    }

}
