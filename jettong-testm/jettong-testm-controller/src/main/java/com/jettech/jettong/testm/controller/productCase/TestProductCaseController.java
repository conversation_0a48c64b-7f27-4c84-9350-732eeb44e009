package com.jettech.jettong.testm.controller.productCase;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.vo.BugTrendComponentResult;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.enumeration.DateGranularityEnum;
import com.jettech.jettong.common.enumeration.DateTypeEnum;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestProductCaseDTO;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.dto.TestProductCaseSaveDTO;
import com.jettech.jettong.testm.dto.TestProductCaseUpdateDTO;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.service.impl.ProductCaseTreeServiceImpl;
import com.jettech.jettong.testm.service.impl.TestProductCaseServiceImpl;
import com.jettech.jettong.testm.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * 控制器
 * <AUTHOR>
 * @version 1.0
 * @description 控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestProductCaseController
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 *  productcase:
 *         title: 产品用例信息
 *         base-package: com.jettech.jettong.testm.controller.productcase
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testProductCase")
@Api(value = "TestProductCase", tags = "产品用例信息")
@PreAuth(replace = "testm:testProductCase:")
@RequiredArgsConstructor
public class TestProductCaseController extends SuperController<TestProductCaseService, Long, TestProductCase, TestProductCasePageQuery, TestProductCaseSaveDTO, TestProductCaseUpdateDTO> {

    private final EchoService echoService;

    private final TestProductCaseServiceImpl testProductCaseService;

    private final TestPlanCaseService testPlanCaseService;

    private final TestTaskCaseService testTaskCaseService;

    private final TestTabService testTabService;

    private final TestTabCaseService testTabCaseService;

    private final TestProductCaseHistoryService testProductCaseHistoryService;

    private final ProductCaselibraryConnectService productCaselibraryConnectService;

    private final ProductInfoApi productInfoApi;

    private final ProductCaseLibraryService productCaseLibraryService;

    private final UserApi userApi;

    private final TestPlanCaseResultService testPlanCaseResultService;

    private final TestTaskCaseResultService testTaskCaseResultService;

    private final FileApi fileApi;

    private final RequirementApi requirementApi;

    private final TestreqApi testreqApi;

    private final ProductCaseTreeService productCaseTreeService;

    private final TestProductCaseArchiveService testProductCaseArchiveService;

    private final TestOverviewService testOverviewService;

    private final ProductCaseTreeController productCaseTreeController;
    private final PersonalizedTableViewApi tableViewApi;
    @Autowired
    private ProductCaseTreeServiceImpl productCaseTreeServiceImpl;

    private final TestProductCaseTestModeService testProductCaseTestModeService;
    private final DictionaryApi dictionaryApi;
    private final  TestRequirementFunctionPointsService testRequirementFunctionPointsService;
    private final ProductModuleFunctionApi productModuleFunctionApi;


    @Override
    @Transactional
    public R<TestProductCase> handlerSave(@Validated TestProductCaseSaveDTO model)
    {
//        if(productCaseTreeService.getById(model.getTreeId()).getParentId() == 0){
//            return R.fail("根节点下禁止关联用例");
//        }
        TestProductCase testCase = BeanUtil.toBean(model, TestProductCase.class);

        baseService.saveTestCase(testCase);
        testTabCaseService.save(testCase.getId(), model.getTabInfo(), "product");
        TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                .caseId(testCase.getId())
                .caseKey(testCase.getCaseKey())
                .productId(testCase.getProductId())
                .createdBy(testCase.getCreatedBy())
                .name(testCase.getName())
                .intent(testCase.getIntent())
                .testStep(testCase.getTestStep())
                .expectedResult(testCase.getExpectedResult())
                .createTime(testCase.getCreateTime())
                .leadingBy(testCase.getLeadingBy())
                .libraryId(testCase.getLibraryId())
                .prerequisite(testCase.getPrerequisite())
                .priority(testCase.getPriority())
                .requirementId(testCase.getRequirementId())
                .stateId(testCase.getStateId())
                .stepType(testCase.getStepType())
                .treeId(testCase.getTreeId())
                .createdBy(testCase.getCreatedBy())
                .updatedBy(testCase.getCreatedBy())
                .updateTime(testCase.getUpdateTime())
                .version(testCase.getVersion())
                .execTime(testCase.getExecTime())
                .moduleFunctionId(testCase.getModuleFunctionId()).build();
        testProductCaseHistoryService.save(testProductCaseHistory);
        baseService.saveOverView(testCase);
//        if(StringUtil.isNotEmpty(model.getRequirementId())){
//
//            List<String> caseIds = new ArrayList<>();
//            caseIds.add(testCase.getId(). toString());
//            Map<String, List<String>> map = new HashMap<>();
//            map.put("productCaseIds", caseIds);
//            List<String> issueId = new ArrayList<>();
//            issueId.addAll(Arrays.asList(model.getRequirementId().split(",")));
//            map.put("requirementIds", issueId);
//            requirementApi.updateRequirementConnectCaseId(map);
//        }
        if(model.getFunctionPointsId()!=null){
            testRequirementFunctionPointsService.addCaseCount(model.getFunctionPointsId());
        }
        //添加项目和测试方式的关联
        List<Long> testModeIds = model.getTestMode();
        if (CollUtil.isNotEmpty(testModeIds))
        {
            List<TestProductCaseTestMode> caseTestModes = new ArrayList<>();
            for(Long mId : testModeIds){
                TestProductCaseTestMode testMode =  new TestProductCaseTestMode();
                testMode.setTestProductCaseId(testCase.getId());
                testMode.setTestmodeId(mId);
                caseTestModes.add(testMode);
            }
            testProductCaseTestModeService.saveBatch(caseTestModes);
        }
        return success(testCase);
    }

    @ApiOperation(value = "新增用例1", notes = "新增用例1")
    @PostMapping("/saveTestcase")
    @SysLog(value = "新增用例1", request = false)
    public R<TestProductCase> saveTestCase(@RequestBody TestProductCaseSaveDTO model)
    {
        if(productCaseTreeService.getById(model.getTreeId()).getParentId() == 0){
            return R.fail("根节点下禁止关联用例");
        }
        TestProductCase testCase = BeanUtil.toBean(model, TestProductCase.class);
        baseService.saveTestCase(testCase);
        testTabCaseService.save(testCase.getId(), model.getTabInfo(), "product");
        TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                .caseId(testCase.getId())
                .caseKey(testCase.getCaseKey())
                .productId(testCase.getProductId())
                .createdBy(testCase.getCreatedBy())
                .name(testCase.getName())
                .intent(testCase.getIntent())
                .testStep(testCase.getTestStep())
                .expectedResult(testCase.getExpectedResult())
                .createTime(testCase.getCreateTime())
                .leadingBy(testCase.getLeadingBy())
                .libraryId(testCase.getLibraryId())
                .prerequisite(testCase.getPrerequisite())
                .priority(testCase.getPriority())
                .requirementId(testCase.getRequirementId())
                .stateId(testCase.getStateId())
                .stepType(testCase.getStepType())
                .treeId(testCase.getTreeId())
                .createdBy(testCase.getCreatedBy())
                .updatedBy(testCase.getCreatedBy())
                .updateTime(testCase.getUpdateTime())
                .version(testCase.getVersion())
                .execTime(testCase.getExecTime()).build();
        testProductCaseHistoryService.save(testProductCaseHistory);
        baseService.saveOverView(testCase);
        return success(testCase);
    }

    @ApiOperation(value = "新增用例2", notes = "新增用例2")
    @PostMapping("/saveTest")
    @SysLog(value = "新增用例2", request = false)
    public R<TestProductCase> saveTest(@RequestBody TestProductCaseSaveDTO model)
    {
        if(productCaseTreeService.getById(model.getTreeId()).getParentId() == 0){
            return R.fail("根节点下禁止关联用例");
        }
        TestProductCase testCase = BeanUtil.toBean(model, TestProductCase.class);
        baseService.saveTestCase(testCase);
//        testTabCaseService.save(testCase.getId(), model.getTabInfo(), "product");
        TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                .caseId(testCase.getId())
                .caseKey(testCase.getCaseKey())
                .productId(testCase.getProductId())
                .createdBy(testCase.getCreatedBy())
                .name(testCase.getName())
                .intent(testCase.getIntent())
                .testStep(testCase.getTestStep())
                .expectedResult(testCase.getExpectedResult())
                .createTime(testCase.getCreateTime())
                .leadingBy(testCase.getLeadingBy())
                .libraryId(testCase.getLibraryId())
                .prerequisite(testCase.getPrerequisite())
                .priority(testCase.getPriority())
                .requirementId(testCase.getRequirementId())
                .stateId(testCase.getStateId())
                .stepType(testCase.getStepType())
                .treeId(testCase.getTreeId())
                .createdBy(testCase.getCreatedBy())
                .updatedBy(testCase.getCreatedBy())
                .updateTime(testCase.getUpdateTime())
                .version(testCase.getVersion())
                .execTime(testCase.getExecTime()).build();
        testProductCaseHistoryService.save(testProductCaseHistory);
        baseService.saveOverView(testCase);
        return success(testCase);
    }

    @ApiOperation(value = "查询未规划用例", notes = "查询未规划用例")
    @PutMapping("/finddelCaseById/{libraryId}")
    @SysLog(value = "查询未规划用例", request = false)
    public R<List<TestProductCase>> finddelCaseById(@PathVariable Long libraryId)
    {
        List<TestProductCase> list =
                baseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, libraryId).isNull(TestProductCase::getTreeId));
        return R.success(list);
    }


    @Override
    public IPage<TestProductCase> query(
            @RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(params.getModel().getTabName()!=null&&params.getModel().getTabName().length > 0){
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return page;
                }
            }
        }

        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

        wrapper.eq(TestProductCase::getProductId, model.getProductId())
                .like(TestProductCase::getName, model.getName())
                .eq(TestProductCase::getLeadingBy, model.getLeadingBy())
                .eq(TestProductCase::getLibraryId,model.getLibraryId())
                .eq(TestProductCase::getPriority,model.getPriority())
                .like(TestProductCase::getCaseKey,model.getCaseKey())
                .eq(TestProductCase::getState,model.getState())
                .eq(TestProductCase::getDraft,model.getDraft())
                .eq(TestProductCase::getTaskId,model.getTaskId())
                .eq(TestProductCase::getRequirementId,model.getRequirementId())
                .eq(TestProductCase::getProjectId,model.getProjectId())
                .eq(TestProductCase::getTestreqId,model.getTestreqId());
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        if(model.getFunctionIds().size() > 0){
            wrapper.in(TestProductCase::getModuleFunctionId,model.getFunctionIds());
        }
//        wrapper.eq(TestProductCase::getTreeId,model.getTreeId());
//        wrapper.orderByDesc(TestProductCase::getCreateTime);
        baseService.page(page, wrapper);
//        if (params.getModel().getPlanId() != null) {
//            List<TestPlanCase> testPlanCases = new ArrayList<>();
//            List<Long> caseIds = page.getRecords().stream().map(TestProductCase::getId).collect(Collectors.toList());
//            if (caseIds.size() != 0) {
//                testPlanCases = testPlanCaseService.list(Wraps.<TestPlanCase>lbQ()
//                        .eq(TestPlanCase::getPlanId, params.getModel().getPlanId())
//                        .in(TestPlanCase::getTestcaseId, caseIds));
//            }
//            for (TestProductCase item : page.getRecords()) {
//
//                for(TestPlanCase testPlanCase : testPlanCases){
//
//                    if (Objects.equals(item.getId(), testPlanCase.getTestcaseId()) && testPlanCase.getExecBy() != null){
//
//                        item.getEchoMap().put("execBy", userApi.findUserById(testPlanCase.getExecBy()));
//                        item.setExecBy(testPlanCase.getExecBy());
//                    }
//                }
//            }
//        }

        if (params.getModel().getTaskId() != null) {
            List<TestTaskCase> testTaskCaseList = new ArrayList<>();
            List<Long> caseIds = page.getRecords().stream().map(TestProductCase::getId).collect(Collectors.toList());
            if (caseIds.size() != 0) {
                testTaskCaseList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ()
                        .eq(TestTaskCase::getTaskId, params.getModel().getTaskId())
                        .in(TestTaskCase::getTestcaseId, caseIds));
            }
            for (TestProductCase item : page.getRecords()) {

                for(TestTaskCase testTaskCase : testTaskCaseList){

                    if (Objects.equals(item.getId(), testTaskCase.getTestcaseId()) && testTaskCase.getExecBy() != null){

                        item.getEchoMap().put("execBy", userApi.findUserById(testTaskCase.getExecBy()));
                        item.setExecBy(testTaskCase.getExecBy());
                    }
                }
            }
        }
        page.getRecords().forEach(item->
        {
            Long leadingBy = item.getLeadingBy();
            User userById = userApi.findUserById(leadingBy);
            item.getEchoMap().put("leadingBy",userById);

            //字典集合数据回显
            List<TestProductCaseTestMode> testModes = testProductCaseTestModeService.list(Wraps.<TestProductCaseTestMode>lbQ().eq(TestProductCaseTestMode::getTestProductCaseId, item.getId()));
            if(testModes != null && testModes.size()>0){
                List<Dictionary> dicList = new ArrayList<>();
                List<Long> mIds =  new ArrayList<>();
                testModes.forEach(testMode -> {
                    Long id =  testMode.getTestmodeId();
                    mIds.add(id);
                    Dictionary dictionary = dictionaryApi.findDictionaryById(id);
                    if (dictionary != null)
                    {
                        dicList.add(dictionary);
                    }
                });
                item.setTestMode(mIds);
                item.getEchoMap().put("testMode", dicList);
            }
        });
        echoService.action(page);
        return page;
    }

    @ApiOperation(value = "获取全部规划用例信息", notes = "获取全部规划用例信息")
    @PostMapping("/getAllConnect/{libraryId}")
    @SysLog(value = "获取全部规划用例信息", request = false)
    public R<List<Long>> getAllConnect(@PathVariable Long libraryId)
    {
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getLibraryId,libraryId)
                .isNotNull(TestProductCase::getTreeId)
                .ne(TestProductCase::getTreeId,0L);
        List<Long> collect =
                baseService.list(wrapper).stream().map(TestProductCase::getId).collect(Collectors.toList());
        return R.success(collect);
    }

    @ApiOperation(value = "根据用例库id获取全部终稿用例信息", notes = "根据用例库id获取全部终稿用例信息")
    @PostMapping("/getAllConnectByLibraryId/{libraryId}")
    @SysLog(value = "根据用例库id获取全部终稿用例信息", request = false)
    public R<List<Long>> getAllConnectByLibraryId(@PathVariable Long libraryId)
    {
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getLibraryId,libraryId)
                .isNotNull(TestProductCase::getTreeId)
                .ne(TestProductCase::getTreeId,0L)
                .eq(TestProductCase::getDraft,false)
                .eq(TestProductCase::getState,false);
        List<Long> collect =
                baseService.list(wrapper).stream().map(TestProductCase::getId).collect(Collectors.toList());
        return R.success(collect);
    }


    @ApiOperation(value = "复制用例树下用例", notes = "复制用例树下用例")
    @PutMapping("/copyTreeById")
    @SysLog(value = "复制用例树下用例", request = false)
    @Transactional
    public R<Boolean> copyTreeById(@RequestBody TestProductCaseVO param)
    {

        if(param.getCaseId().size()==0 || param.getCaseId().get(0)==null){
            throw BizException.validFail("请勾选测试用例");
        }
        Long treeId = param.getTreeId();
        List<Long> caseId = param.getCaseId();
        List<TestProductCase> testProductCases = new ArrayList<>();
        caseId.forEach(item->
        {
            TestProductCase testCase = baseService.copyCaseById(item, treeId);
            TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                    .caseId(testCase.getId())
                    .caseKey(testCase.getCaseKey())
                    .productId(testCase.getProductId())
                    .createdBy(testCase.getCreatedBy())
                    .name(testCase.getName())
                    .intent(testCase.getIntent())
                    .testStep(testCase.getTestStep())
                    .expectedResult(testCase.getExpectedResult())
                    .createTime(testCase.getCreateTime())
                    .leadingBy(testCase.getLeadingBy())
                    .libraryId(testCase.getLibraryId())
                    .prerequisite(testCase.getPrerequisite())
                    .priority(testCase.getPriority())
                    .requirementId(testCase.getRequirementId())
                    .stateId(testCase.getStateId())
                    .stepType(testCase.getStepType())
                    .treeId(testCase.getTreeId())
                    .createdBy(testCase.getCreatedBy())
                    .updatedBy(testCase.getCreatedBy())
                    .updateTime(testCase.getUpdateTime())
                    .version(testCase.getVersion())
                    .execTime(testCase.getExecTime()).build();
            testProductCaseHistoryService.save(testProductCaseHistory);
            List<TestTabCase> list = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getProdId, item));
            if(list.size()!=0){
                list.forEach(tab->
                {
                    tab.setProdId(testCase.getId());
                    tab.setId(UidGeneratorUtil.getId());
                    tab.setCreatedBy(ContextUtil.getUserId());
                    tab.setCreateTime(LocalDateTime.now());
                    tab.setUpdatedBy(ContextUtil.getUserId());
                    tab.setUpdateTime(LocalDateTime.now());
                });
                testTabCaseService.saveBatch(list);
            }
            testProductCases.add(testCase);
        });
        Map<Integer, Long> map = testProductCases.stream().collect(Collectors.groupingBy(TestProductCase::getPriority, Collectors.counting()));
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        long caseHighest = testOverview.getCaseHighest()==null? 0:testOverview.getCaseHighest() + (map.get(5)==null ? 0:map.get(5));
        long caseHigher = testOverview.getCaseHigher()==null? 0:testOverview.getCaseHigher() + (map.get(4)==null ? 0:map.get(4));
        long caseOrdinary = testOverview.getCaseOrdinary()==null? 0:testOverview.getCaseOrdinary() +(map.get(3)==null ? 0:map.get(3));
        long caseLower = testOverview.getCaseLower()==null? 0:testOverview.getCaseLower() + (map.get(2)==null ? 0:map.get(2));
        long caseMinimum = testOverview.getCaseMinimum()==null? 0:testOverview.getCaseMinimum() + (map.get(1)==null ? 0:map.get(1));
        testOverview.setCaseHighest(caseHighest).setCaseHigher(caseHigher).setCaseOrdinary(caseOrdinary)
                .setCaseLower(caseLower).setCaseMinimum(caseMinimum);
        testOverviewService.updateById(testOverview);
        return R.success();
    }

    @ApiOperation(value = "用例升级", notes = "用例升级")
    @PutMapping("/caseUpgrade")
    @SysLog(value = "用例升级", request = false)
    public R<Boolean> caseUpgrade(@Validated @RequestBody TestProductCaseUpdateDTO testProductCaseUpdateDTO)
    {
        TestProductCase testProductCase = BeanUtil.toBean(testProductCaseUpdateDTO, TestProductCase.class);
        if(testProductCase.getDraft() == true){
            return R.validFail("草稿用例不能升级");
        }
        baseService.caseUpgrade(testProductCase);
        return R.success();
    }

    @ApiOperation(value = "判断当前用例版本是否为最新版本", notes = "判断当前用例版本是否为最新版本")
    @PostMapping("/newVersion")
    @SysLog(value = "判断当前用例版本是否为最新版本", request = false)
    public R<Boolean> newVersion(@RequestBody CaseVersion caseVersion)
    {
        Long id = caseVersion.getId();
        String version = caseVersion.getVersion();
        TestProductCase byId = baseService.getById(id);
        if(byId == null){
            TestProductCaseArchive testProductCaseArchive = testProductCaseArchiveService.getById(id);
            if (testProductCaseArchive.getVersion().equals(version)){
                return R.success();
            }
            return R.fail("当前用例非产品用例");
        }
        if(byId.getVersion().equals(version)){
            return R.success();
        }
        return R.fail("当前用例版本不是最新版本");
    }


    @ApiOperation(value = "(包含总数)查询产品用例趋势组件", notes = "(包含总数)查询项目用例趋势组件")
    @GetMapping("/findAllTrendByLibraryId/{libraryId}")
    @SysLog("(包含总数)查询项目用例趋势组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<BugTrendComponentResult> findAllTrendByProjectId(@PathVariable("libraryId") Long libraryId,
            @RequestParam(value = "granularity", defaultValue = "DAY")
                    DateGranularityEnum granularity,
            @RequestParam(value = "dateType", defaultValue = "LAST_WEEK") DateTypeEnum dateType)
    {
        return R.success(testProductCaseArchiveService.findAllTrendByProjectId(libraryId, granularity, dateType));
    }

    @ApiOperation(value = "用例数量人员分布", notes = "用例数量人员分布")
    @GetMapping("/findCaseNumberUser/{libraryId}")
    @SysLog(value = "用例数量人员分布", request = false)
    public R<Map<String,Object>> findCaseNumberUser(@PathVariable Long libraryId)
    {
        Map<String,Object> map = testProductCaseArchiveService.findCaseNumberUser(libraryId);
        return R.success(map);
    }


    @ApiOperation(value = "查看测试用例列表", notes = "查看测试用例列表")
    @PostMapping("/getPlanList")
    @SysLog(value = "查看测试用例列表", request = false)
    public IPage<TestProductCase> getPlanList(
            @RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getLeadingBy,model.getLeadingBy())
                .eq(TestProductCase::getStateId,model.getStateId());
        baseService.page(page, wrapper);
        echoService.action(page);
        return page;
    }

    @Override
    public R<TestProductCase> handlerUpdate(TestProductCaseUpdateDTO model) {
        TestProductCase testProductCase = BeanPlusUtil.toBean(model, TestProductCase.class);
        TestProductCase byId = baseService.getById(model.getId());
        baseService.setOverView(testProductCase);
        baseService.updateById(testProductCase);
        testTabCaseService.update(model.getId(), model.getTabInfo(), "product");

        List<TestTaskCaseResult> caseResultList = testTaskCaseResultService.list(Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTestcaseId, model.getId()));
        //用例已加入执行范围，若还未执行，则将修改内容同步到执行范围；若用例已经有执行结果，则不同步。
        if(caseResultList.size() == 0){
            testTaskCaseService.update(new UpdateWrapper<TestTaskCase>().eq("testcase_id", model.getId())
                    .set("name",model.getName())
                    .set("version",model.getVersion())
                    .set("case_key",model.getCaseKey())
                    .set("priority",model.getPriority()));
        }
        //对测试分析点新增删除修改用例数
        Long oldFunctionPointsId = byId.getFunctionPointsId();
        Long newFunctionPointsId = model.getFunctionPointsId();
        // 仅当ID变化时操作计数
        if (oldFunctionPointsId != null) {
            // 减少旧ID计数
            testRequirementFunctionPointsService.redCaseCount(oldFunctionPointsId);
        }
        if (newFunctionPointsId != null) {
            // 增加新ID计数
            testRequirementFunctionPointsService.addCaseCount(newFunctionPointsId);
        }
        // 更新附件文件
        Long caseId = model.getId();
        List<File> newFiles = model.getFiles();

        // 注意：这里判空，不能判集合是否有元素。集合无元素代表，删除原来的关联关系
        if (newFiles != null) {
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            newFiles.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        //更新用例意图附件
        if (model.getIntentFiles() != null){
            List<File> files = model.getIntentFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }

        //更新用例前置条件附件
        if (model.getPrerequisiteFiles() != null){
            List<File> files = model.getPrerequisiteFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }

        //更新用例测试步骤附件
        if (model.getTestStepFiles() != null){
            List<File> files = model.getTestStepFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }
        //更新历史表数据
        TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                .caseId(testProductCase.getId())
                .caseKey(testProductCase.getCaseKey())
                .productId(testProductCase.getProductId())
                .createdBy(testProductCase.getCreatedBy())
                .name(testProductCase.getName())
                .intent(testProductCase.getIntent())
                .testStep(testProductCase.getTestStep())
                .expectedResult(testProductCase.getExpectedResult())
                .createTime(testProductCase.getCreateTime())
                .leadingBy(testProductCase.getLeadingBy())
                .libraryId(testProductCase.getLibraryId())
                .prerequisite(testProductCase.getPrerequisite())
                .priority(testProductCase.getPriority())
                .requirementId(testProductCase.getRequirementId())
                .stateId(testProductCase.getStateId())
                .stepType(testProductCase.getStepType())
                .treeId(testProductCase.getTreeId())
                .createdBy(testProductCase.getCreatedBy())
                .updatedBy(testProductCase.getCreatedBy())
                .updateTime(testProductCase.getUpdateTime())
                .version(testProductCase.getVersion())
                .execTime(testProductCase.getExecTime()).build();
        TestProductCaseHistory one = testProductCaseHistoryService.getOne(Wraps.<TestProductCaseHistory>lbQ()
                .eq(TestProductCaseHistory::getCaseId, testProductCase.getId())
                .orderByDesc(TestProductCaseHistory::getCreateTime).last(" limit 1"));
        testProductCaseHistory.setId(one.getId());
        testProductCaseHistoryService.updateById(testProductCaseHistory);
//        if(StringUtil.isNotEmpty(model.getRequirementId())){
//
//            List<String> caseIds = new ArrayList<>();
//            caseIds.add(model.getId().toString());
//            Map<String, List<String>> map = new HashMap<>();
//            map.put("productCaseIds", caseIds);
//            List<String> issueId = new ArrayList<>();
//            issueId.addAll(Arrays.asList(model.getRequirementId().split(",")));
//            map.put("requirementIds", issueId);
//            requirementApi.updateRequirementConnectCaseId(map);
//        }
        //删除项目和测试方式关联关系
        testProductCaseTestModeService.remove(
                Wraps.<TestProductCaseTestMode>lbQ().eq(TestProductCaseTestMode::getTestProductCaseId, testProductCase.getId()));
        //添加项目和测试方式的关联
        List<Long> testModeIds = model.getTestMode();
        if (CollUtil.isNotEmpty(testModeIds))
        {
            List<TestProductCaseTestMode> projectTestModes = new ArrayList<>();
            for(Long mId : testModeIds){
                TestProductCaseTestMode testMode =  new TestProductCaseTestMode();
                testMode.setTestProductCaseId(testProductCase.getId());
                testMode.setTestmodeId(mId);
                projectTestModes.add(testMode);
            }
            testProductCaseTestModeService.saveBatch(projectTestModes);
        }
        return R.success(testProductCase);
    }

    @ApiOperation(value = "批量打标签", notes = "批量打标签")
    @GetMapping("/tabBatch")
    @SysLog(value = "批量打标签", request = false)
    public R tabBatch(@RequestParam List<Long> caseIds, @RequestParam String[] tabInfo)
    {
        for (Long t : caseIds) {
            testTabCaseService.update(t, tabInfo, "product");
        }
        return success();
    }

    @Override
    public R<TestProductCase> get(Long id) {
        TestProductCase byId = baseService.getById(id);
        if (byId == null){
            byId = BeanUtil.toBean(testProductCaseArchiveService.getById(id), TestProductCase.class);
        }
        List<File> caseFiles =
                fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD, byId.getId());
        byId.setFiles(caseFiles);
        byId.setIntentFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD, byId.getId()));
        byId.setPrerequisiteFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD, byId.getId()));
        byId.setTestStepFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD, byId.getId()));

        List<ProductCaselibraryConnect> list = productCaselibraryConnectService
                .list(Wraps.<ProductCaselibraryConnect>lbQ().eq(ProductCaselibraryConnect::getCaselibraryId, id));
        ArrayList<ProductInfo> products = new ArrayList<>();
        list.forEach(item->
        {
            ProductInfo productInfo = productInfoApi.selectProductInfoById(item.getProductId());
            products.add(productInfo);
        });
        byId.getEchoMap().put("product",products);
        if(!Objects.equals(byId.getRequirementId(), null) && !Objects.equals(byId.getRequirementId(), "")) {
            String[] requirementIdStrList = byId.getRequirementId().split(",");
            List<Long> requirementIdList = Arrays.asList(requirementIdStrList).stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
            List<Requirement> requirements = new ArrayList<>();
            List<Testreq> testreqs = new ArrayList<>();
//            requirementIdList.forEach(requirementId -> {
//
//                Requirement requirement = requirementApi.getRequirement(requirementId);
//                requirements.add(requirement);
//            });
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);

            requirementIdList.forEach(requirementId -> {
                if (atomicBoolean.get() || Objects.isNull(requirementApi.getRequirement(requirementId))) {
                    atomicBoolean.set(true);
                    Testreq testreq = testreqApi.getTestreq(requirementId);
                    testreqs.add(testreq);
                } else {
                    Requirement requirement = requirementApi.getRequirement(requirementId);
                    requirements.add(requirement);
                }
            });
            byId.getEchoMap().put("requirement", requirements);
            byId.getEchoMap().put("testreq", testreqs);
        }
        byId.getEchoMap().put("treeId", productCaseTreeService.getById(byId.getTreeId()));
        byId.getEchoMap().put("leadingBy",userApi.findUserById(byId.getLeadingBy()));
        byId.setTabs(testTabCaseService.getTabsOfCase(byId.getId(), "product"));
        List<TestProductCaseTestMode> testModes = testProductCaseTestModeService.list(Wraps.<TestProductCaseTestMode>lbQ().eq(TestProductCaseTestMode::getTestProductCaseId, byId.getId()));
        List<Long> testModeList = testModes.stream().map(TestProductCaseTestMode::getTestmodeId).collect(Collectors.toList());
        byId.setTestMode(testModeList);
        ProductModuleFunction moduleFunction = productModuleFunctionApi.findProductModuleFunctionById(byId.getModuleFunctionId());
        byId.setModuleFunctionName(moduleFunction.getName());
        return success(byId);
    }

    @ApiOperation(value = "拖拽用例到树下", notes = "拖拽用例到树下")
    @PostMapping("/updateTreeIdTestProductCase/{treeId}")
    @SysLog(value = "拖拽用例到树下", request = false)
    public R updateTreeIdTestProductCase(@PathVariable Long treeId,@RequestBody List<Long> caseIds)
    {
        caseIds.forEach(item->
        {
            TestProductCase testProductCase = baseService.getById(item);
            testProductCase.setTreeId(treeId);
            baseService.updateById(testProductCase);
        });
        return R.success();
    }

    @ApiOperation(value = "修改执行步骤", notes = "修改执行步骤")
    @PutMapping("/updateTestStep")
    @SysLog(value = "修改执行步骤", request = false)
    public R<Boolean> updateTestStep(@RequestBody TestProductCaseUpdateDTO dto)
    {
        TestProductCase testProductCase = baseService.getById(dto.getId());
        testProductCase.setTestStep(dto.getTestStep());

        Boolean update = baseService.updateById(testProductCase);

        return R.success(update);
    }

    @ApiOperation(value = "修改用例优先级", notes = "修改用例优先级")
    @GetMapping("/updatePriority")
    @SysLog(value = "修改用例优先级", request = false)
    public R<TestProductCase> updatePriority(@RequestParam Long planId, @RequestParam Long caseId, @RequestParam Integer priority )
    {
        TestProductCase testProductCase = baseService.getById(caseId);
        if (testProductCase == null){
            TestProductCaseArchive byId = testProductCaseArchiveService.getById(caseId);
            testProductCase = BeanUtil.toBean(byId,TestProductCase.class);
        }
        Long libraryId = testProductCase.getLibraryId();
        //处理优先级表格数据
        if (!productCaseLibraryService.getById(libraryId).getType().equals("project")){
            TestProductCase testProductCase1 = baseService.getById(testProductCase.getId());
            if (testProductCase1 == null){
                TestProductCaseArchive byId = testProductCaseArchiveService.getById(testProductCase.getId());
                testProductCase1 = BeanUtil.toBean(byId,TestProductCase.class);
            }
            TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
            if (!testProductCase1.getPriority().equals(testProductCase.getPriority()))
            {
                switch (testProductCase1.getPriority())
                {
                    case 1:
                        testOverview.setCaseMinimum(testOverview.getCaseMinimum() - 1);
                        break;
                    case 2:
                        testOverview.setCaseLower(testOverview.getCaseLower() - 1);
                        break;
                    case 3:
                        testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() - 1);
                        break;
                    case 4:
                        testOverview.setCaseHigher(testOverview.getCaseHigher() - 1);
                        break;
                    case 5:
                        testOverview.setCaseHighest(testOverview.getCaseHighest() - 1);
                        break;
                }
                switch (testProductCase.getPriority())
                {
                    case 1:
                        testOverview.setCaseMinimum(testOverview.getCaseMinimum()==null? 0:testOverview.getCaseMinimum() + 1);
                        break;
                    case 2:
                        testOverview.setCaseLower(testOverview.getCaseLower()==null? 0:testOverview.getCaseLower() + 1);
                        break;
                    case 3:
                        testOverview.setCaseOrdinary(testOverview.getCaseOrdinary()==null? 0:testOverview.getCaseOrdinary() + 1);
                        break;
                    case 4:
                        testOverview.setCaseHigher(testOverview.getCaseHigher()==null? 0:testOverview.getCaseHigher() + 1);
                        break;
                    case 5:
                        testOverview.setCaseHighest(testOverview.getCaseHighest()==null? 0:testOverview.getCaseHighest() + 1);
                        break;
                }
                testOverviewService.updateById(testOverview);
            }
        }
        //修改用例表或用例归档表数据
        if (testProductCase != null){
            baseService.update(new UpdateWrapper<TestProductCase>().eq("id", caseId)
                    .set("priority", priority));
        }else {
            testProductCaseArchiveService.update(new UpdateWrapper<TestProductCaseArchive>().eq("id", caseId)
                    .set("priority", priority));
        }
        testPlanCaseService.update(new UpdateWrapper<TestPlanCase>().eq("plan_id", planId)
                .eq("testcase_id", caseId)
                .set("priority", priority));
        return R.success(baseService.getById(caseId));
    }


    @ApiOperation(value = "概览-用例总数", notes = "productIds非必填，不关联产品时不用填写")
    @GetMapping("/getCaseNumOnProductId")
    @SysLog(value = "概览-用例总数", request = false)
    public R<TestSummaryComponent> getCaseNumOnProductId(@RequestParam(required = false) List<Long> productIds)
    {
        List<TestProductCase> casesOnProductId = new ArrayList<>();
        if(null != productIds && productIds.size() > 0) {
            for (Long t : productIds) {
                casesOnProductId.addAll(productCaseLibraryService.getCasesOnProductId(t));
            }
        } else {
            casesOnProductId.addAll(testProductCaseService.list());
        }
        // 优先级为null统一赋值
        for (TestProductCase t : casesOnProductId) {
            if (null == t.getPriority()) {
                t.setPriority(3);
            }
        }
        Map<Integer, Long> map = casesOnProductId.stream().collect(Collectors.groupingBy(TestProductCase::getPriority, Collectors.counting()));
        return success(baseService.dataFormat(map, casesOnProductId.size()));
    }

    @ApiOperation(value = "概览-用例总数(更具用例库id查询)", notes = "LibraryIds非必填，不关联产品时不用填写")
    @GetMapping("/getCaseNumOnLibraryIds")
    @SysLog(value = "概览-用例总数", request = false)
    public R<TestSummaryComponent> getCaseNumOnLibraryId(@RequestParam(required = false) List<Long> libraryIds)
    {
     /*   List<TestProductCase> casesOnProductId = new ArrayList<>();

        if(null != libraryIds && libraryIds.size() > 0) {
            for (Long t : libraryIds) {
                casesOnProductId.addAll(testProductCaseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, t)));
            }
        } else {
            casesOnProductId.addAll(testProductCaseService.list());
        }
        // 优先级为null统一赋值
        for (TestProductCase t : casesOnProductId) {
            if (null == t.getPriority()) {
                t.setPriority(3);
            }
        }
        Map<Integer, Long> map = casesOnProductId.stream().collect(Collectors.groupingBy(TestProductCase::getPriority, Collectors.counting()));
        return success(baseService.dataFormat(map, casesOnProductId.size()));*/
        List<TestProductCase> casesOnProductId = new ArrayList<>();

        if(null != libraryIds && libraryIds.size() > 0) {
            for (Long t : libraryIds) {
                casesOnProductId.addAll(testProductCaseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, t)
                        .eq(TestProductCase::getState,false)));
                List<TestProductCaseArchive> list = testProductCaseArchiveService
                        .list(Wraps.<TestProductCaseArchive>lbQ().eq(TestProductCaseArchive::getLibraryId, t));
                list.forEach(item ->
                {
                    TestProductCase testProductCase = BeanUtil.toBean(item, TestProductCase.class);
                    casesOnProductId.add(testProductCase);
                });
            }
        } else {
            casesOnProductId.addAll(testProductCaseService.list(Wraps.<TestProductCase>lbQ().isNotNull(TestProductCase::getPriority)));
        }
        Map<Integer, Long> map = casesOnProductId.stream().collect(Collectors.groupingBy(TestProductCase::getPriority, Collectors.counting()));
        return success(baseService.dataFormat(map, casesOnProductId.size()));
    }
    /**
     * 点击删除时，通过用例id将用例移入回收站内
     * @param caseIds
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/4/8 9:54
     * @update xmy 2022/4/8 9:54
     * @since 1.0
     */
    @ApiOperation(value = "用例移入回收站", notes = "用例移入回收站")
    @PostMapping("/trashCase")
    @SysLog(value = "用例移入回收站", request = false)
    public R trashCase(@RequestBody List<Long> caseIds)
    {
        ArrayList<TestProductCase> testProductCases = new ArrayList<>();
        testProductCases.forEach(item ->
        {
            baseService.deleteOverView(item);
        });
        if(testPlanCaseService.list(Wraps.<TestPlanCase>lbQ().isNotNull(TestPlanCase::getPlanId).in(
                TestPlanCase::getTestcaseId,caseIds)).size()>0){
            return R.validFail("用例内包含被测试计划引用的用例，不可删除");
        }
        caseIds.forEach(item->
        {
            TestProductCase testProductCase = baseService.getById(item);
            testProductCase.setState(true);
            testProductCases.add(testProductCase);
        });
        baseService.updateBatchById(testProductCases);
        return R.success();
    }


    /**
     * 批量删除用例
     * @param caseIds
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/4/8 9:54
     * @update xmy 2022/4/8 9:54
     * @since 1.0
     */
    @ApiOperation(value = "批量删除用例", notes = "批量删除用例")
    @PostMapping("/batchDeleteCase")
    @SysLog(value = "批量删除用例", request = false)
    public R batchDeleteCase(@RequestBody List<Long> caseIds)
    {
        List<TestProductCase> testProductCases =
                testProductCaseService.list(Wraps.<TestProductCase>lbQ().in(TestProductCase::getId, caseIds));
        //删除计数
        testProductCases.forEach(item ->
                {
                    if (item.getFunctionPointsId() != null)
                    {
                        testRequirementFunctionPointsService.redCaseCount(item.getFunctionPointsId());
                    }
                }
        );
        testTabCaseService.remove(Wraps.<TestTabCase>lbQ().in(TestTabCase::getProdId,caseIds));
        baseService.removeAllTestProductCase(testProductCases);
        testProductCaseHistoryService.remove(Wraps.<TestProductCaseHistory>lbQ().in(TestProductCaseHistory::getCaseId,caseIds));
        //删除测试方式关联关系
        testProductCaseTestModeService.remove(Wraps.<TestProductCaseTestMode>lbQ().in(TestProductCaseTestMode::getTestProductCaseId, caseIds));
        testTaskCaseService.remove(Wraps.<TestTaskCase>lbQ().in(TestTaskCase::getTestcaseId, caseIds));
        baseService.removeByIds(caseIds);
        return R.success();
    }


    /**
     * 点击删除时，查询已关联用例列表
     * @param caseIds
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/4/8 9:54
     * @update xmy 2022/4/8 9:54
     * @since 1.0
     */
    @ApiOperation(value = "查询已关联用例列表", notes = "查询已关联用例列表")
    @PostMapping("/queryUsedProductCase")
    @SysLog(value = "查询已关联用例列表", request = false)
    public R<List<TestProductCase>> queryUsedProductCase(@RequestBody List<Long> caseIds)
    {
        List<TestProductCase> testProductCases = new ArrayList<>();
        List<TestTaskCase> productCaseList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().in(TestTaskCase::getTestcaseId,caseIds));
        List<Long> productCaseIds = productCaseList.stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
        if(productCaseIds.size() > 0){
            testProductCases = baseService.list(Wraps.<TestProductCase>lbQ().in(TestProductCase::getId,productCaseIds));
        }
        return R.success(testProductCases);
    }

    @ApiOperation(value = "查询回收站内用例", notes = "查询回收站内用例")
    @PostMapping("/getTrashCase")
    @SysLog(value = "查询回收站内用例", request = false)
    public IPage<TestProductCase> getTrashCase(@RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(params.getModel().getTabName().length > 0){
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return page;
                }
            }
        }
        List<Long> prodCaseIDs = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getState,true)
                .eq(TestProductCase::getLibraryId,model.getLibraryId())
                .eq(TestProductCase::getPriority,model.getPriority())
                .like(TestProductCase::getName,model.getName())
                .like(TestProductCase::getCaseKey,model.getCaseKey());
        if(prodCaseIDs.size() > 0) {
            wrapper.in(TestProductCase::getId, prodCaseIDs);
        }
        baseService.page(page, wrapper);
        echoService.action(page);
        return page;
    }

    @ApiOperation(value = "回收站还原", notes = "回收站还原")
    @PostMapping("/callBack")
    @SysLog(value = "回收站还原", request = false)
    public R callBack(@RequestBody List<Long> caseIds)
    {
        ArrayList<TestProductCase> testProductCases = new ArrayList<>();
        testProductCases.forEach(item ->{
            baseService.saveOverView(item);
        });
        caseIds.forEach(item->
        {
            TestProductCase testProductCase = baseService.getById(item);
            testProductCase.setState(false);
//            //判断用例树是否被删除，删除则还原到未分组
//            if (productCaseTreeService.getById(testProductCase.getTreeId()) == null){

                testProductCase.setTreeId(0L);

//            }
            testProductCases.add(testProductCase);
        });
        baseService.updateBatchById(testProductCases);
        return R.success();
    }

    @ApiOperation(value = "测试用例批量修改前置条件等", notes = "测试用例批量修改前置条件等")
    @PostMapping("/updateAllParam")
    @SysLog(value = "测试用例批量修改前置条件等", request = false)
    public R updateAllParam(@RequestBody TestProductCaseUpdateDTO model) {
        List<Long> ids = model.getIds();
        if(ids.size()==0){
            throw BizException.validFail("请勾选测试用例");
        }
        ids.forEach(item->
        {
            TestProductCase testProductCase = baseService.getById(item);
            if(model.getPriority() != null){
                baseService.setOverView(testProductCase.setPriority(model.getPriority()));
            }
            testProductCase.setPrerequisite(model.getPrerequisite()==null|| "".equals(model.getPrerequisite()) ? testProductCase.getPrerequisite() : model.getPrerequisite());
            testProductCase.setPriority(model.getPriority()==null||model.getPriority()==0 ? testProductCase.getPriority() : model.getPriority());
            testProductCase.setTreeId(model.getTreeId()==null||model.getTreeId()==0 ? testProductCase.getTreeId() : model.getTreeId());
            if(model.getTabInfo()!=null && model.getTabInfo().length!=0){
                testTabCaseService.save(item, model.getTabInfo(), "product");
            }
            testProductCase.setIntent(model.getIntent()==null||"".equals(model.getIntent()) ? testProductCase.getIntent() : model.getIntent());
            testProductCase.setRequirementId(model.getRequirementId()==null||Objects.equals(model.getRequirementId(),"0") ? testProductCase.getRequirementId() : model.getRequirementId());
            baseService.updateById(testProductCase);
        });
        return R.success();
    }

    @ApiOperation(value = "修改全部的前置条件等", notes = "修改全部的前置条件等")
    @PostMapping("/updateAllByLibraryId")
    @SysLog(value = "修改全部的前置条件等", request = false)
    public R updateAllByLibraryId(@RequestBody TestProductCaseUpdateDTO model) {

        //查询出用例库下全部用例Id
        List<TestProductCase> allCaselist = baseService.list(Wraps.<TestProductCase>lbQ()
                .select(TestProductCase::getId, TestProductCase::getPriority)
                .eq(TestProductCase::getLibraryId, model.getLibraryId()));
        //若需要批量修改标签，保存批量修改的标签，并查询出其最子级标签ID和标签名称
        if(model.getTabInfo()!=null && model.getTabInfo().length!=0){
            Map<String, String> minIdOfTab = testTabCaseService.save(null, model.getTabInfo(), "product");
            List<TestTabCase> testTabCaseList = new ArrayList<>();
            //test_tab_case批量匹配配置数据
            minIdOfTab.forEach((key,value) -> {

                allCaselist.forEach(l -> {

                    TestTabCase testTabCase = new TestTabCase(UidGeneratorUtil.getId(), null, Long.valueOf(key), l.getId(), value);
                    testTabCase.setTabName(value);
                    testTabCaseList.add(testTabCase);
                });
            });
            //test_tab_case批量插入
            //每2000条提交一次
            int tabSize = testTabCaseList.size();
            int tabIndex = 2000;
            for(int i = 0; i < testTabCaseList.size(); i += 2000){
                if (i + 2000 > tabSize)
                {
                    tabIndex = tabSize - i;
                }
                List<TestTabCase> newList = testTabCaseList.subList(i, i + tabIndex);
                testTabCaseService.saveTabCaseList(newList);
            }
        }
        //若需要批量修改优先级，将原优先级状态分布做减法，然后将新更新的优先级数量加上去
        if(!Objects.equals(model.getPriority(), null) && !Objects.equals(model.getPriority(), "")){
            testProductCaseService.deleteAllOverView(allCaselist);
            TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
            switch (model.getPriority())
            {
                case 1:
                    testOverview.setCaseMinimum(testOverview.getCaseMinimum() + allCaselist.size());
                    break;
                case 2:
                    testOverview.setCaseLower(testOverview.getCaseLower() + allCaselist.size());
                    break;
                case 3:
                    testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() + allCaselist.size());
                    break;
                case 4:
                    testOverview.setCaseHigher(testOverview.getCaseHigher() + allCaselist.size());
                    break;
                case 5:
                    testOverview.setCaseHighest(testOverview.getCaseHighest() + allCaselist.size());
                    break;
            }
            testOverviewService.updateById(testOverview);
        }
        //若需要批量修改用例的其他属性,筛选条件用例库Id，直接用update set进去
        baseService.updateBatchByLibraryId(model, model.getLibraryId());


        //原功能代码暂不删除
//        Long id = model.getLibraryId();
//        List<TestProductCase> list =
//                baseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, id));
//        list.forEach(testProductCase->
//        {
//            if(model.getPriority() !=null){
//                baseService.setOverView(testProductCase.setPriority(model.getPriority()));
//            }
//            testProductCase.setPrerequisite(model.getPrerequisite()==null|| "".equals(model.getPrerequisite()) ? testProductCase.getPrerequisite() : model.getPrerequisite());
//            testProductCase.setPriority(model.getPriority()==null||model.getPriority()==0 ? testProductCase.getPriority() : model.getPriority());
//            testProductCase.setTreeId(model.getTreeId()==null||model.getTreeId()==0 ? testProductCase.getTreeId() : model.getTreeId());
//            if(model.getTabInfo()!=null && model.getTabInfo().length!=0){
//                testTabCaseService.save(testProductCase.getId(), model.getTabInfo(), "product");
//            }
//            testProductCase.setIntent(model.getIntent()==null||"".equals(model.getIntent()) ? testProductCase.getIntent() : model.getIntent());
//            testProductCase.setRequirementId(model.getRequirementId()==null||model.getRequirementId()==0 ? testProductCase.getRequirementId() : model.getRequirementId());
//            baseService.updateById(testProductCase);
//
//        });
        return R.success();
    }

    @Override
    @Transactional
    public R<Boolean> delete(@RequestBody List<Long> ids)
    {
        if (Objects.equals(ids.size(),0)||Objects.equals(ids,null)){
            return fail("参数传递异常！");
        }
        if (testPlanCaseService.count(Wraps.<TestPlanCase>lbQ().in(TestPlanCase::getTestcaseId, ids)) > 0)
        {
            return R.validFail("当前用例在测试计划中使用，不可删除");
        }
        testTabCaseService.remove(Wraps.<TestTabCase>lbQ().in(TestTabCase::getProdId,ids));
        List<TestProductCase> testProductCases =
                testProductCaseService.list(Wraps.<TestProductCase>lbQ().in(TestProductCase::getId, ids));
        baseService.removeAllTestProductCase(testProductCases);
        if (ids.size() != 0){
            testPlanCaseService.remove(Wraps.<TestPlanCase>lbQ().in(TestPlanCase::getTestcaseId, ids));
        }
        testProductCaseHistoryService.remove(Wraps.<TestProductCaseHistory>lbQ().in(TestProductCaseHistory::getCaseId,ids));
        if (ids.size() != 0) {
            testPlanCaseResultService.remove(Wraps.<TestPlanCaseResult>lbQ().in(TestPlanCaseResult::getTestcaseId, ids));
        }
        baseService.removeByIds(ids);
        //删除测试方式关联关系
        testProductCaseTestModeService.remove(Wraps.<TestProductCaseTestMode>lbQ().in(TestProductCaseTestMode::getTestProductCaseId, ids));
        return success();
    }


    @ApiOperation(value = "根据用例库查询草稿箱用例", notes = "根据用例库查询草稿箱用例")
    @PostMapping("/getDraftByLibraryId")
    @SysLog(value = "根据用例库查询草稿箱用例", request = false)
    public IPage<TestProductCase> getDraftByLibraryId(@RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getLibraryId, model.getLibraryId())
                .eq(TestProductCase::getDraft,true);

        baseService.page(page, wrapper);
        page.getRecords().forEach(item ->
        {
            Long leadingBy = item.getLeadingBy();
            User userById = userApi.findUserById(leadingBy);
            item.getEchoMap().put("leadingBy",userById);
        });
        echoService.action(page);
        return page;
    }

    @ApiOperation(value = "查询用例库用户", notes = "查询用例库用户")
    @PostMapping("/getUserById/{id}")
    @SysLog(value = "查询用例库用户", request = false)
    public R<User> getUserById(@PathVariable Long id)
    {
        User userById = userApi.findUserById(id);
        return R.success(userById);
    }



    @ApiOperation(value = "跨用例库复制用例", notes = "跨用例库复制用例")
    @PostMapping("/copyCaseToProject")
    @SysLog(value = "跨用例库复制用例", request = false)
    @Transactional
    public R copyCaseToProject(@RequestBody TestProductCaseUpdateDTO model)
    {
        model.getIds().forEach(item->
        {
            TestProductCase byId = baseService.getById(item);
            byId.setLibraryId(model.getLibraryId());
            byId.setTreeId(model.getTreeId());
            byId.setVersion("v1.0");
            baseService.saveTestCase(byId);
        });
        return R.success();
    }

    @ApiOperation(value = "分页查看未分组用例", notes = "分页查看未分组用例")
    @PostMapping("/getNoGroupCase")
    @SysLog(value = "分页查看未分组用例", request = false)
    public IPage<TestProductCase> getNoGroupCase(
            @RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(params.getModel().getTabName().length > 0){
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return page;
                }
            }
        }
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestProductCase::getLeadingBy,model.getLeadingBy())
                .eq(TestProductCase::getStateId,model.getStateId())
                .eq(TestProductCase::getLibraryId,model.getLibraryId())
                .eq(TestProductCase::getState,false)
                .eq(TestProductCase::getCaseKey,model.getCaseKey())
                .like(TestProductCase::getName,model.getName())
                .eq(TestProductCase::getDraft,model.getDraft())
                .eq(TestProductCase::getTreeId,0L);
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        baseService.page(page, wrapper);
        echoService.action(page);
        return page;
    }

    @ApiOperation(value = "全局筛选用例和树级", notes = "全局筛选用例和树级")
    @PostMapping("/getTreeAndCase")
    @SysLog(value = "全局筛选用例和树级", request = false)
    public Map getTreeAndCase(@RequestBody @Validated PageParams<TestProductCasePageQuery> params)
    {
        //分页参数配置
        IPage<TestProductCase> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        Map map = new HashMap();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        //当筛选条件包含标签名称时
        if(params.getModel().getTabName().length > 0){
            //单个标签正常查询，多个标签取交集
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    //循环从上一次结果取交集
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    map.put("page",null);
                    map.put("tree",null);
                    return map;
                }
            }
        }
        //条件筛选数据
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

        wrapper.eq(TestProductCase::getProductId, model.getProductId())
                .like(TestProductCase::getName, model.getName())
                .eq(TestProductCase::getLeadingBy, model.getLeadingBy())
                .eq(TestProductCase::getLibraryId,model.getLibraryId())
                .eq(TestProductCase::getPriority,model.getPriority())
                .like(TestProductCase::getCaseKey,model.getCaseKey())
                .eq(TestProductCase::getState,model.getState())
                .eq(TestProductCase::getDraft,model.getDraft());
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        wrapper.eq(TestProductCase::getTreeId,model.getTreeId());
        baseService.page(page, wrapper);
        echoService.action(page);

//        List<TestProductCase> records = page.getRecords();
//        Boolean flag = model.getName()==null&&model.getCaseKey()==null&&model.getTabName().length==0&&model.getPriority()==null;
//        List<ProductCaseTree> productCaseTrees = new ArrayList<>();
//        //当无条件帅选判断时，整理出全部树级
//        if (flag){
//            productCaseTrees = productCaseTreeService.list(Wraps.<ProductCaseTree>lbQ().eq(ProductCaseTree::getLibraryId,model.getLibraryId()));
//        //当筛选出的结果没有值时，只展示根树级
//        }else if(records.size()==0){
//            productCaseTrees = productCaseTreeService.list(Wraps.<ProductCaseTree>lbQ().eq(ProductCaseTree::getLibraryId,model.getLibraryId())
//            .eq(ProductCaseTree::getParentId,0));
//        }
//        //当具有筛选条件且有满足结果时，展示满足结果的树级
//        else {
//            productCaseTrees = productCaseTreeService.getAllParentTree(records);
//        }

//        List<Long> ids = records.stream().map(TestProductCase::getId).collect(Collectors.toList());
//        List<TestProductCaseNumberOfTreeVo> testProductCaseNumberOfTreeVos = testProductCaseService.getQueryCaseNumberOfTree(BeanUtil.toBean(model,TestProductCase.class),ids);
//        Map<Long, List<TestProductCaseNumberOfTreeVo>> testProductCaseNumberOfTreeMap = testProductCaseNumberOfTreeVos.stream()
//                .collect(Collectors.groupingBy(TestProductCaseNumberOfTreeVo::getTreeId));
//        List<Long> parentTreeList = productCaseTrees.stream().map(ProductCaseTree::getParentId).collect(Collectors.toList());
//        List<ProductCaseTree> trees = new ArrayList<>();
//        trees.addAll(productCaseTrees);
//        List<ProductCaseTree> primaryTreeList = new ArrayList<>();
//
//        productCaseTrees.forEach(tree -> {
//
//            if (!parentTreeList.contains(tree.getId())){
//
//                primaryTreeList.add(tree);
//            }
//        });
//        productCaseTreeController.operationCaseNumberOfTree(primaryTreeList, productCaseTrees, testProductCaseNumberOfTreeMap);
//        testProductCaseNumberOfTreeMap.remove(0);


        map.put("page",page);
//        map.put("tree",trees);
//        map.put("count",testProductCaseNumberOfTreeMap);

        // 保存最后一次查询的数据
        tableViewApi.saveLastSearch(getUserId(), "testm-case-table", params);

        return map;
    }

    @ApiOperation(value = "根据项目Id/用例库id模糊查询项目用例库下/产品用例库下用例", notes = "根据项目Id/用例库id模糊查询项目用例库下/产品用例库下用例")
    @GetMapping("/getCaseByProjectIdOrLibraryId/{type}/{id}")
    @SysLog(value = "根据项目Id/用例库id模糊查询项目用例库下/产品用例库下用例", request = false)
    public R<List<TestProductCase>> getCaseByProjectIdOrLibraryId(@PathVariable("type") String type,
                                                                  @PathVariable("id") Long id,
                                                                  @RequestParam(value = "name") String name) {
        if (Objects.equals(type, "product")){

            List<TestProductCase> TestProductCaseList = baseService.list(Wraps.<TestProductCase>lbQ()
                    .eq(TestProductCase::getLibraryId, id)
                    .like(TestProductCase::getName, name));
            return R.success(TestProductCaseList, "查询用例信息成功！");
        }else {

            ProductCaseLibrary productCaseLibrary = productCaseLibraryService
                    .getOne(Wraps.<ProductCaseLibrary>lbQ()
                    .eq(ProductCaseLibrary::getProjectId, id));
            List<TestProductCase> TestProductCaseList = baseService.list(Wraps.<TestProductCase>lbQ()
                    .eq(TestProductCase::getLibraryId, productCaseLibrary.getId())
                    .like(TestProductCase::getName, name));
            return R.success(TestProductCaseList, "查询用例信息成功！");
        }

    }

    @ApiOperation(value = "查询用例树级下条件删选后的总树级与用例", notes = "查询用例树级下条件删选后的总树级与用例")
    @PutMapping("/queryTestCaseLibrary")
    @SysLog(value = "查询用例树级下条件删选后的总树级与用例", request = false)
    public R<List<PlanTreeAndPlanCaseResultVo>> querySubordinateOfPlanTree(@RequestBody TestProductCase testProductCase) {
        ///1.先获取范围树级
        List<ProductCaseTree> resultTrees = new ArrayList<>();

        if(Objects.equals(testProductCase.getTreeId(), null) && Objects.equals(testProductCase.getTreeId(), "")){

            return R.fail("请选择树级！");

        }

        ProductCaseTree currentTree = productCaseTreeService.getById(testProductCase.getTreeId());

        resultTrees.add(currentTree);

        List<Long> treeIds = new ArrayList<>();

        treeIds.add(testProductCase.getTreeId());

        getChildTree(treeIds, resultTrees);
        //父级的信息不需要查出，故提前到父级树添加前查询
        List<Long> treeIdList = resultTrees.stream().map(ProductCaseTree::getId).collect(Collectors.toList());

        getParentTree(currentTree.getParentId(), resultTrees);

        ///2.获取符合筛选条件的用例
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(testProductCase.getTabInfo()!=null&&testProductCase.getTabInfo().length > 0){
            for (String tabId : testProductCase.getTabInfo()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return success(null);
                }
            }
        }
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

        wrapper.like(TestProductCase::getName, testProductCase.getName())
                .eq(TestProductCase::getLibraryId,testProductCase.getLibraryId())
                .eq(TestProductCase::getPriority,testProductCase.getPriority())
                .like(TestProductCase::getCaseKey,testProductCase.getCaseKey())
                .eq(TestProductCase::getState,0)
                .eq(TestProductCase::getDraft,0)
                .in(TestProductCase::getTreeId, treeIdList)
                .select(TestProductCase::getId,
                        TestProductCase::getName,
                        TestProductCase::getTreeId,
                        TestProductCase::getVersion,
                        TestProductCase::getPriority,
                        TestProductCase::getCaseKey);

        
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        List<TestProductCase> testProductCaselist = baseService.list(wrapper);
        ///3.获取树级计数
        Map<Long, List<TestProductCase>> caseOfTreeMap = testProductCaselist
                .stream()
                .collect(Collectors.groupingBy(TestProductCase::getTreeId));
        Map<Long, List<TestProductCaseNumberOfTreeVo>> testProductCaseNumberOfTreeMap = new HashMap<>();
        caseOfTreeMap.forEach((key, value) -> {
            List<TestProductCaseNumberOfTreeVo> testProductCaseNumberOfTreeVoList = new ArrayList<>();
            TestProductCaseNumberOfTreeVo testProductCaseNumberOfTreeVo = new TestProductCaseNumberOfTreeVo();
            testProductCaseNumberOfTreeVo.setTreeId(key);
            testProductCaseNumberOfTreeVo.setCount(Long.valueOf(value.size()));
            testProductCaseNumberOfTreeVoList.add(testProductCaseNumberOfTreeVo);
            testProductCaseNumberOfTreeMap.put(key, testProductCaseNumberOfTreeVoList);
        });
        testProductCaseNumberOfTreeMap.remove(null);



        List<Long> parentTreeList = resultTrees.stream().map(ProductCaseTree::getParentId).collect(Collectors.toList());
        List<ProductCaseTree> primaryTreeList = new ArrayList<>();
        List<ProductCaseTree> trees = new ArrayList<>();
        trees.addAll(resultTrees.stream().distinct().collect(Collectors.toList()));
        resultTrees.forEach(tree -> {

            if (!parentTreeList.contains(tree.getId())){

                primaryTreeList.add(tree);
            }
        });
        productCaseTreeServiceImpl.operationCaseNumberOfTree(primaryTreeList, resultTrees, testProductCaseNumberOfTreeMap);
        testProductCaseNumberOfTreeMap.remove(0);

        ///4.剔除树级计数为0时的树级数据
        Map<Long, TestProductCaseNumberOfTreeVo> testCasesCountOfTreeResult = new HashMap<>();
        List<Long> resultTreeId = new ArrayList<>();
        testProductCaseNumberOfTreeMap.forEach((key, value) -> {

            if (value.get(0).getCount() != 0L) {
                testCasesCountOfTreeResult.put(key, value.get(0));
                resultTreeId.add(key);
            }
        });
        List<PlanTreeAndPlanCaseResultVo> planTreeAndPlanCaseResults = new ArrayList<>();
        ///5.循环处理allTrees, testPlanCaseVos数据
        trees.forEach(tree -> {
            if (resultTreeId.contains(tree.getId())) {
                PlanTreeAndPlanCaseResultVo planTreeAndPlanCaseResult = new PlanTreeAndPlanCaseResultVo();
                planTreeAndPlanCaseResult.setId(tree.getId());
                planTreeAndPlanCaseResult.setName(tree.getName());
                planTreeAndPlanCaseResult.setParentId(tree.getParentId());
                planTreeAndPlanCaseResult.setType("tree");
                planTreeAndPlanCaseResult.setCount(testCasesCountOfTreeResult.get(tree.getId()).getCount());
                planTreeAndPlanCaseResults.add(planTreeAndPlanCaseResult);
            }
        });
        testProductCaselist.forEach(testCase -> {
            PlanTreeAndPlanCaseResultVo planTreeAndPlanCaseResult = new PlanTreeAndPlanCaseResultVo();
            planTreeAndPlanCaseResult.setId(testCase.getId());
            planTreeAndPlanCaseResult.setName(testCase.getName());
            planTreeAndPlanCaseResult.setParentId(testCase.getTreeId());
            planTreeAndPlanCaseResult.setVersion(testCase.getVersion());
            planTreeAndPlanCaseResult.setType("case");
            planTreeAndPlanCaseResult.setPriority(testCase.getPriority());
            planTreeAndPlanCaseResult.setCaseKey(testCase.getCaseKey());
            planTreeAndPlanCaseResults.add(planTreeAndPlanCaseResult);
        });
        return R.success(TreeUtil.buildTree(planTreeAndPlanCaseResults));
//        return R.success(planTreeAndPlanCaseResults);
    }

    public void getChildTree(List<Long> treeIds, List<ProductCaseTree> resultTrees){

        QueryWrapper<ProductCaseTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "name", "parent_id").in("parent_id", treeIds);
        List<ProductCaseTree> list = productCaseTreeService.list(queryWrapper);
        if (list.size()!=0) {
            resultTrees.addAll(list);
            treeIds = list.stream().map(ProductCaseTree::getId).collect(Collectors.toList());
            getChildTree(treeIds, resultTrees);
        }
    }

    public void getParentTree(Long parentTreeId, List<ProductCaseTree> resultTrees){

        if (parentTreeId != 0){
            QueryWrapper<ProductCaseTree> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("id", "name", "parent_id").eq("id", parentTreeId);
            ProductCaseTree parentTree = productCaseTreeService.getOne(queryWrapper);
            resultTrees.add(parentTree);
            getParentTree(parentTree.getParentId(), resultTrees);
        }
    }


    @ApiOperation(value = "通过筛选用例过滤树级", notes = "通过筛选用例过滤树级")
    @PutMapping("/queryTestCaseTreeByCase")
    @SysLog(value = "通过筛选用例过滤树级", request = false)
    public R<List<PlanTreeAndPlanCaseResultVo>> queryTestCaseTreeByCase(@RequestBody TestProductCase testProductCase) {
        ///1.先获取条件筛选的整体用例
        List<ProductCaseTree> resultTrees = new ArrayList<>();
        ///1.1若筛选条件包含标签则获取交集条件下的用例
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(testProductCase.getTabInfo()!=null&&testProductCase.getTabInfo().length > 0){
            for (String tabId : testProductCase.getTabInfo()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return success(null);
                }
            }
        }
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

        wrapper.like(TestProductCase::getName, testProductCase.getName())
                .eq(TestProductCase::getLibraryId,testProductCase.getLibraryId())
                .eq(TestProductCase::getPriority,testProductCase.getPriority())
                .like(TestProductCase::getCaseKey,testProductCase.getCaseKey())
                .eq(TestProductCase::getState,0)
                .eq(TestProductCase::getDraft,0)
                .select(TestProductCase::getId,
                        TestProductCase::getName,
                        TestProductCase::getTreeId,
                        TestProductCase::getVersion,
                        TestProductCase::getPriority);

        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        List<TestProductCase> testProductCaselist = baseService.list(wrapper);
        ///1.2获取符合条件下每个树级有多少个用例
        Map<Long, List<TestProductCase>> caseOfTreeMap = testProductCaselist
                .stream()
                .collect(Collectors.groupingBy(TestProductCase::getTreeId));
        Map<Long, List<TestProductCaseNumberOfTreeVo>> testProductCaseNumberOfTreeMap = new HashMap<>();
        caseOfTreeMap.forEach((key, value) -> {
            List<TestProductCaseNumberOfTreeVo> testProductCaseNumberOfTreeVoList = new ArrayList<>();
            TestProductCaseNumberOfTreeVo testProductCaseNumberOfTreeVo = new TestProductCaseNumberOfTreeVo();
            testProductCaseNumberOfTreeVo.setTreeId(key);
            testProductCaseNumberOfTreeVo.setCount(Long.valueOf(value.size()));
            testProductCaseNumberOfTreeVoList.add(testProductCaseNumberOfTreeVo);
            testProductCaseNumberOfTreeMap.put(key, testProductCaseNumberOfTreeVoList);
        });
        testProductCaseNumberOfTreeMap.remove(null);
        ///1.3取出满足条件的用例树级Id，递归获取树级到根目录
        List<Long> leafTreeList = testProductCaselist.stream().map(TestProductCase::getTreeId).distinct().collect(Collectors.toList());
//        QueryWrapper<ProductCaseTree> queryWrapper = new QueryWrapper<>();
//        queryWrapper.select("id", "name", "parent_id").in("id", leafTreeList);
//        List<ProductCaseTree> list = productCaseTreeService.list(queryWrapper);
//        resultTrees.addAll(list);
        if(leafTreeList.size() != 0) {
            getParentTreeList(leafTreeList, resultTrees);
        }
        ///2.组装数据获取每层树级数据的递加值
        List<Long> parentTreeList = resultTrees.stream().map(ProductCaseTree::getParentId).collect(Collectors.toList());
        List<ProductCaseTree> primaryTreeList = new ArrayList<>();

        resultTrees.forEach(tree -> {

            if (!parentTreeList.contains(tree.getId())){

                primaryTreeList.add(tree);
            }
        });

        List<ProductCaseTree> trees = new ArrayList<>();
        trees.addAll(resultTrees.stream().distinct().collect(Collectors.toList()));

        productCaseTreeServiceImpl.operationCaseNumberOfTree(primaryTreeList, resultTrees, testProductCaseNumberOfTreeMap);
        testProductCaseNumberOfTreeMap.remove(0);


        Map<Long, TestProductCaseNumberOfTreeVo> testCasesCountOfTreeResult = new HashMap<>();
        List<Long> resultTreeId = new ArrayList<>();
        testProductCaseNumberOfTreeMap.forEach((key, value) -> {

            if (value.get(0).getCount() != 0L) {
                testCasesCountOfTreeResult.put(key, value.get(0));
                resultTreeId.add(key);
            }
        });
        List<PlanTreeAndPlanCaseResultVo> planTreeAndPlanCaseResults = new ArrayList<>();


        ///3.循环处理allTrees, testPlanCaseVos数据
        trees.forEach(tree -> {
            if (resultTreeId.contains(tree.getId())) {
                PlanTreeAndPlanCaseResultVo planTreeAndPlanCaseResult = new PlanTreeAndPlanCaseResultVo();
                planTreeAndPlanCaseResult.setId(tree.getId());
                planTreeAndPlanCaseResult.setName(tree.getName());
                planTreeAndPlanCaseResult.setParentId(tree.getParentId());
                planTreeAndPlanCaseResult.setType("tree");
                planTreeAndPlanCaseResult.setCount(testCasesCountOfTreeResult.get(tree.getId()).getCount());
                planTreeAndPlanCaseResults.add(planTreeAndPlanCaseResult);
            }
        });
        return R.success(planTreeAndPlanCaseResults);
//        return R.success(planTreeAndPlanCaseResults);
    }

    public void getParentTreeList(List<Long> parentTreeIds, List<ProductCaseTree> resultTrees){

        QueryWrapper<ProductCaseTree> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "name", "parent_id").in("id", parentTreeIds);
        List<ProductCaseTree> list = productCaseTreeService.list(queryWrapper);
        resultTrees.addAll(list);

        List<Long> parentIdList = list.stream().map(ProductCaseTree::getParentId).collect(Collectors.toList());
        List<Long> resultParentIds = parentIdList.stream().filter(a -> !a.equals(0)).collect(Collectors.toList());
        if(resultParentIds.size() != 0){

            getParentTreeList(resultParentIds, resultTrees);
        }
    }


    @ApiOperation(value = "查询用例执行下任务关联用例", notes = "查询用例执行下任务关联用例")
    @PostMapping("/queryProductCaseByFunctionIdsAndTaskId")
    @SysLog(value = "查询用例执行下任务关联用例", request = false)
    public R<List<TestTaskCase>> queryProductCaseByFunctionIdsAndTaskId(@RequestBody TestProductCasePageQuery data)
    {
        List<Long> caseIdList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId,data.getTaskId())).stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
        LbqWrapper<TestProductCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.in(TestProductCase::getModuleFunctionId, data.getFunctionIds());
        List<TestProductCase> productCases = baseService.list(queryWrapLbuWrapper);
        Map<Long, TestProductCase> testProductCaseMap = productCases.stream().filter(a -> caseIdList.contains(a.getId()))
                .collect(Collectors.toMap(TestProductCase::getId, v -> v));
        if(CollUtil.isEmpty(testProductCaseMap)){
            return success(Collections.EMPTY_LIST);
        }
        //使用TestProductCase的name
        List<TestTaskCase> testTaskCaseList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, data.getTaskId())
                        .in(TestTaskCase::getTestcaseId, testProductCaseMap.keySet()));
        testTaskCaseList.stream().filter(testTaskCase -> testTaskCase.getStatus()==null).forEach(testTaskCase -> testTaskCase.setName(testProductCaseMap.get(testTaskCase.getTestcaseId()).getName()));
        return R.success(testTaskCaseList, "查询用例执行下任务关联用例成功！");
    }

    @ApiOperation(value = "查询用例执行下任务未关联用例成功", notes = "查询用例执行下任务未关联用例成功")
    @PostMapping("/queryByFunctionIdUnassociatedTask")
    @SysLog(value = "查询用例执行下任务未关联用例成功", request = false)
    public R<List<TestProductCase>> queryByFunctionIdUnassociatedTask(@RequestBody TestProductCasePageQuery data)
    {
        List<Long> caseIdList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId,data.getTaskId())).stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
        LbqWrapper<TestProductCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.in(TestProductCase::getModuleFunctionId, data.getFunctionIds());
        if(data.getTestreqId()!=null){
            queryWrapLbuWrapper.eq(TestProductCase::getTestreqId,data.getTestreqId());
        }else{
            queryWrapLbuWrapper.eq(TestProductCase::getProjectId,data.getProjectId());
        }
        queryWrapLbuWrapper.like(TestProductCase::getCaseKey,data.getCaseKey());
        queryWrapLbuWrapper.like(TestProductCase::getName,data.getName());
        queryWrapLbuWrapper.eq(TestProductCase::getPriority,data.getPriority());

        List<TestProductCase> productCases = baseService.list(queryWrapLbuWrapper);
        List<TestProductCase> filterList = productCases.stream().filter(a -> !caseIdList.contains(a.getId())).sorted(Comparator.comparing(TestProductCase::getCreateTime).reversed()).collect(Collectors.toList());
        for(TestProductCase testProductCase:filterList){
            List<TestTaskCase> testTaskCaseList = testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTestcaseId,testProductCase.getId()));
            if(testTaskCaseList.size()>0){
                testProductCase.setIsCited(true);
            }else{
                testProductCase.setIsCited(false);
            }
        }
        List<TestProductCase> resultList = new ArrayList<>();
        if(data.getIsCited() != null){
            resultList = filterList.stream().filter(item->data.getIsCited().equals(item.getIsCited())).collect(Collectors.toList());
        }else{
            resultList = filterList;
        }

        return R.success(resultList, "查询用例执行下任务未关联用例成功！");
    }

    @GetMapping("/queryProductCaseById/{id}/{taskId}")
    @SysLog(value = "根据用例id查已执行用例", request = false)
    public R<TestProductCase> queryProductCaseById(@PathVariable("id") Long id,@PathVariable("taskId") Long taskId) {
        TestProductCase byId = baseService.getById(id);

        TestTaskCase taskCase = testTaskCaseService.getOne(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTestcaseId, id).eq(TestTaskCase::getTaskId,taskId));
        if(taskCase != null){
            byId.setName(taskCase.getName());
            byId.setCaseKey(taskCase.getCaseKey());
            byId.setVersion(taskCase.getVersion());
            byId.setPriority(taskCase.getPriority());
            byId.setExecBy(taskCase.getExecBy());
        }

        List<File> caseFiles =
                fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD, byId.getId());
        byId.setFiles(caseFiles);
        byId.setIntentFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD, byId.getId()));
        byId.setPrerequisiteFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD, byId.getId()));
        byId.setTestStepFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD, byId.getId()));

        List<ProductCaselibraryConnect> list = productCaselibraryConnectService
                .list(Wraps.<ProductCaselibraryConnect>lbQ().eq(ProductCaselibraryConnect::getCaselibraryId, id));
        ArrayList<ProductInfo> products = new ArrayList<>();
        list.forEach(item->
        {
            ProductInfo productInfo = productInfoApi.selectProductInfoById(item.getProductId());
            products.add(productInfo);
        });
        byId.getEchoMap().put("product",products);
        if(!Objects.equals(byId.getRequirementId(), null) && !Objects.equals(byId.getRequirementId(), "")) {
            String[] requirementIdStrList = byId.getRequirementId().split(",");
            List<Long> requirementIdList = Arrays.asList(requirementIdStrList).stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
            List<Requirement> requirements = new ArrayList<>();
            List<Testreq> testreqs = new ArrayList<>();
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);

            requirementIdList.forEach(requirementId -> {
                if (atomicBoolean.get() || Objects.isNull(requirementApi.getRequirement(requirementId))) {
                    atomicBoolean.set(true);
                    Testreq testreq = testreqApi.getTestreq(requirementId);
                    testreqs.add(testreq);
                } else {
                    Requirement requirement = requirementApi.getRequirement(requirementId);
                    requirements.add(requirement);
                }
            });
            byId.getEchoMap().put("requirement", requirements);
            byId.getEchoMap().put("testreq", testreqs);
        }
        byId.getEchoMap().put("treeId", productCaseTreeService.getById(byId.getTreeId()));
        byId.getEchoMap().put("leadingBy",userApi.findUserById(byId.getLeadingBy()));
        byId.setTabs(testTabCaseService.getTabsOfCase(byId.getId(), "product"));
        List<TestProductCaseTestMode> testModes = testProductCaseTestModeService.list(Wraps.<TestProductCaseTestMode>lbQ().eq(TestProductCaseTestMode::getTestProductCaseId, byId.getId()));
        List<Long> testModeList = testModes.stream().map(TestProductCaseTestMode::getTestmodeId).collect(Collectors.toList());
        byId.setTestMode(testModeList);
        ProductModuleFunction moduleFunction = productModuleFunctionApi.findProductModuleFunctionById(byId.getModuleFunctionId());
        byId.setModuleFunctionName(moduleFunction.getName());
        return success(byId);
    }

    @ApiOperation(value = "批量修改", notes = "批量修改")
    @PostMapping("/updateByIds")
    @SysLog(value = "批量修改", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> updateBatch(@Validated @RequestBody TestProductCaseDTO models)
    {
        baseService.updateBatch(models);
        return success(true);
    }
}
