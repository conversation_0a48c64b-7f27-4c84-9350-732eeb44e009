package com.jettech.jettong.testm.controller.testTask;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.BugApi;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.testm.dao.TestCaseMapper;
import com.jettech.jettong.testm.dto.*;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.CaseAndTreeReturnVo;
import com.jettech.jettong.testm.vo.GroupForUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 测试任务用例表控制器
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestTaskCaseController
 * @date 2025-08-12
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testTaskCase")
@Api(value = "TestTaskCase", tags = "测试任务用例表")
@PreAuth(replace = "testm:testTaskCase:")
@RequiredArgsConstructor
public class TestTaskCaseController extends SuperController<TestTaskCaseService, Long, TestTaskCase, TestTaskCasePageQuery, TestTaskCaseSaveDTO, TestTaskCaseUpdateDTO>
{

    private final TestProductCaseService testProductCaseService;
    private final TestTaskCaseTreeService testTaskCaseTreeService;
    private final TestTaskCaseResultService testTaskCaseResultService;
    private final UserApi userApi;
    private final TestProductCaseArchiveService testProductCaseArchiveService;
    private final TestProductCaseHistoryService testProductCaseHistoryService;
    private final TaskApi taskApi;
    private final EchoService echoService;
    private final ProductInfoApi productInfoApi;
    private final BugApi bugApi;


    @ApiOperation(value = "查询测试任务下用例", notes = "查询测试任务下用例")
    @PostMapping("/queryTestTaskCase")
    @SysLog(value = "查询测试计划下用例", request = false)
    public R<List<TestTaskCase>> queryTestTaskCase(@RequestBody TestTaskCase data)
    {
        LbqWrapper<TestTaskCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestTaskCase::getTaskId, data.getTaskId());
        List<TestTaskCase> testTaskCaseLists = baseService.list(queryWrapLbuWrapper);
        return R.success(testTaskCaseLists, "查询测试任务下用例成功！");
    }

    @ApiOperation(value = "根据测试任务id查询测试用例", notes = "根据测试任务id查询测试用例")
    @GetMapping("/getTestProductCaseById/{id}")
    @SysLog(value = "根据测试任务id查询测试用例", request = false)
    public R<List<TestProductCase>> getTestProductCaseById(@PathVariable("id") Long id)
    {
        List<Long> collect =
                baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, id)).stream()
                        .map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
        if (collect.size()!=0){
            List<TestProductCase> list =
                    testProductCaseService.list(Wraps.<TestProductCase>lbQ().in(TestProductCase::getId, collect));
            return R.success(list);
        }
        return R.success(null);
    }

    @Override
    public IPage<TestTaskCase> query(
            @RequestBody @Validated PageParams<TestTaskCasePageQuery> params) {

        IPage<TestTaskCase> page = params.buildPage(TestTaskCase.class);
        TestTaskCasePageQuery model = params.getModel();

        List<Long> functionIds = new ArrayList<>();
        if(model.getFunctionAndModuleId() != null && model.getFunctionAndModuleId() != 0L ){
            functionIds = productInfoApi.findByFunctionId(model.getFunctionAndModuleId());

            if(functionIds.isEmpty()){
                return  page;
            }
        }

        LbqWrapper<TestTaskCase> wrapper = Wraps.lbQ();

        wrapper.eq(TestTaskCase::getStatus, model.getStatus())
                .like(TestTaskCase::getName, model.getName())
                .eq(TestTaskCase::getExecBy, model.getExecBy())
                .eq(TestTaskCase::getPriority,model.getPriority())
                .like(TestTaskCase::getCaseKey,model.getCaseKey())
                .eq(TestTaskCase::getTaskId,model.getTaskId())
                .eq(TestTaskCase::getTestcaseId,model.getTestcaseId())
                .eq(TestTaskCase::getModuleFunctionId,model.getModuleFunctionId())
                .eq(TestTaskCase::getProjectId,model.getProjectId())
                .eq(TestTaskCase::getTestreqId,model.getTestreqId())
                .in(TestTaskCase::getModuleFunctionId,functionIds);


        baseService.page(page, wrapper);
        page.getRecords().forEach(item->{
            List<Bug> bugList = bugApi.queryByTestTaskId(item.getId());

            item.getEchoMap().put("bugList", bugList);
        });
        echoService.action(page);
        return page;
    }

    @ApiOperation(value = "保存测试任务关联用例", notes = "保存测试任务关联用例")
    @PostMapping("/saveTestTaskCase")
    @SysLog(value = "保存测试任务关联用例", request = false)
    @Transactional
    public R saveCaseOFTestTaskGroup(@RequestBody TestTaskCaseSaveDTO model)
    {

        String message = baseService.isTaskCaseAddedRepeat(model);
        if(message.length() > 0) {
            return fail(message);
        }
        if (model.getTestcaseIds()!=null && model.getTestcaseIds().size()!=0)
        {
            List<TestProductCase> testProductCaseList = testProductCaseService.list(
                    Wraps.<TestProductCase>lbQ()
                            .in(TestProductCase::getId, model.getTestcaseIds()));
            List<TestTaskCase> testTaskCaseList = new ArrayList<>();
            testProductCaseList.forEach(t ->
            {
                TestTaskCase testTaskCase = new TestTaskCase();
                testTaskCase.setId(UidGeneratorUtil.getId());
                testTaskCase.setTaskId(model.getTaskId());
                testTaskCase.setTreeId(t.getTreeId());
                testTaskCase.setTestcaseId(t.getId());
                testTaskCase.setName(t.getName());
                testTaskCase.setVersion(t.getVersion());
                testTaskCase.setExecBy(model.getExecBy());
                testTaskCase.setPriority(t.getPriority());
                testTaskCase.setCaseKey(t.getCaseKey());
                testTaskCaseList.add(testTaskCase);
            });
            baseService.saveBatch(testTaskCaseList);
        }
        Long taskId = model.getTaskId();
        List<CaseAndTreeReturnVo> caseAndTreeReturnVo = model.getCaseAndTreeReturnVo();
        ArrayList<TestTaskCaseTree> testTaskCaseTrees = new ArrayList<>();
        //0920在对比是否传入的树级Id已被重复关联时，就不在保存其树级结构
        //接口传参需格式要修改，保存数据格式需要修改
        //TestPlanCaseTree实体类格式需要修改
        //TestPlanCaseTreeXml格式需要修改，补充字段
        List<Long> collect =
                testTaskCaseTreeService.list(Wraps.<TestTaskCaseTree>lbQ().eq(TestTaskCaseTree::getTaskId, taskId))
                        .stream()
                        .map(TestTaskCaseTree::getTreeId).collect(Collectors.toList());
        caseAndTreeReturnVo.forEach(item ->
        {
            if (!collect.contains(Long.valueOf(item.getTreeId()))){
                TestTaskCaseTree testTaskCaseTree = TestTaskCaseTree.builder().id(UidGeneratorUtil.getId())
                        .taskId(taskId)
                        .parentId(Long.valueOf(item.getParentId()))
                        .name(item.getTreeName())
                        .treeId(Long.valueOf(item.getTreeId()))
                        .createdBy(ContextUtil.getUserId()).build();
                testTaskCaseTrees.add(testTaskCaseTree);
            }
        });
        testTaskCaseTreeService.saveBatch(testTaskCaseTrees);
        return success();
    }

    @ApiOperation(value = "删除测试任务下用例", notes = "删除测试任务下用例")
    @DeleteMapping("/deleteTestTaskCase")
    @SysLog(value = "删除测试任务下用例", request = false)
    @Transactional
    public R<Boolean> deleteTestTaskCase(@RequestBody TestTaskCaseDelDTO testTaskCaseDelDTO)
    {
        //根据testTaskCaseDelDTO中的planId和testcaseIds查询出所有的testTaskCase
        List<TestTaskCase> testTaskCaseListBefore = baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, testTaskCaseDelDTO.getTaskId())
                .in(TestTaskCase::getTestcaseId,testTaskCaseDelDTO.getTestCaseIds()));
        baseService.remove(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, testTaskCaseDelDTO.getTaskId())
                .in(TestTaskCase::getTestcaseId,testTaskCaseDelDTO.getTestCaseIds()));
        if(!Objects.equals(testTaskCaseDelDTO.getTestCaseIds().size(),0) && !Objects.equals(testTaskCaseDelDTO.getTestCaseIds().size(),null)){
            testTaskCaseResultService.remove(Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTaskId, testTaskCaseDelDTO.getTaskId())
                    .in(TestTaskCaseResult::getTestcaseId,testTaskCaseDelDTO.getTestCaseIds()));
        }
        if(testTaskCaseListBefore!=null && testTaskCaseListBefore.size()>0){
            Long treeId = testTaskCaseListBefore.get(0).getTreeId();
            List<Long> treeIds = new ArrayList<>();
            //删除后查看该计划下是否还有该树级下的用例，如果没有记录带删除中
            List<TestTaskCase> testTaskCaseListAfter = baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, testTaskCaseDelDTO.getTaskId())
                    .eq(TestTaskCase::getTreeId, treeId));
            if(testTaskCaseListAfter==null || testTaskCaseListAfter.size()==0){
                boolean flag = true;
                List<TestTaskCaseTree> list1 = testTaskCaseTreeService.list(Wraps.<TestTaskCaseTree>lbQ().eq(TestTaskCaseTree::getParentId, treeId).eq(TestTaskCaseTree::getTaskId, testTaskCaseDelDTO.getTaskId()));
                //若有子目录存在，则不删除，给flag
                if(list1.size()==0){
                    treeIds.add(treeId);
                }else {
                    flag = false;
                }
                while (flag) {
                    TestTaskCaseTree byId = testTaskCaseTreeService.getOne(Wraps.<TestTaskCaseTree>lbQ().eq(TestTaskCaseTree::getTreeId, treeId).eq(TestTaskCaseTree::getTaskId, testTaskCaseDelDTO.getTaskId()));
                    treeId = byId.getParentId();
                    if(byId.getParentId()!=0) {
                        List<TestTaskCase> testTaskCaseList = baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, testTaskCaseDelDTO.getTaskId())
                                .eq(TestTaskCase::getTreeId, treeId));
                        if(testTaskCaseList==null||testTaskCaseList.size()==0){
                            List<TestTaskCaseTree> list = testTaskCaseTreeService.list(Wraps.<TestTaskCaseTree>lbQ().eq(TestTaskCaseTree::getTreeId, treeId).eq(TestTaskCaseTree::getTaskId, testTaskCaseDelDTO.getTaskId()));
                            //若有子目录存在，则不删除，给flag //顶级目录不删除
                            if(list.size()==1&&list.get(0).getParentId()!=0){
                                treeIds.add(treeId);
                            }else {
                                flag = false;
                            }
                        }else {
                            flag = false;
                        }
                    }else {
                        flag = false;
                    }
                }
            }
            if(treeIds.size()>0){
                testTaskCaseTreeService.remove(Wraps.<TestTaskCaseTree>lbQ().eq(TestTaskCaseTree::getTaskId, testTaskCaseDelDTO.getTaskId())
                        .in(TestTaskCaseTree::getTreeId,treeIds));
            }
        }
        return R.success(null, "取消关联用例成功！");
    }

    @ApiOperation(value = "分组指派", notes = "分组指派")
    @PostMapping("/groupForUser")
    @SysLog(value = "分组指派", request = false)
    public R groupForUser(@RequestBody @Validated GroupForUser groupForUser)
    {
        Long user = groupForUser.getUser();
        Long taskId = groupForUser.getTaskId();
        List<Long> caseId = groupForUser.getCaseId();
//        ArrayList<TestPlanCase> testPlanCases = new ArrayList<>();
        UpdateWrapper<TestTaskCase> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("testcase_id", caseId);
        updateWrapper.eq("task_id", taskId);
        updateWrapper.set("exec_by", user);
        baseService.update(updateWrapper);

        return R.success();
    }


    @ApiOperation(value = "执行用例结果", notes = "执行用例结果")
    @GetMapping("/executeTaskCaseResult")
    @SysLog(value = "执行用例结果", request = false)
    public R<TestTaskCaseExecDTO> executeTaskCaseResult(@RequestParam Long taskId,@RequestParam Long testcaseId) {
        TestTaskCaseResult testTaskCaseResult = testTaskCaseResultService.getOne(
                Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTaskId, taskId)
                        .eq(TestTaskCaseResult::getTestcaseId, testcaseId)
                        .orderByDesc(SuperEntity::getCreateTime).last("limit 1"));
        // 用例未执行，没有用例执行结果
        if(testTaskCaseResult==null) {
            return success(null);
        }

        TestTaskCaseExecDTO testTaskCaseExecDTO = new TestTaskCaseExecDTO();
        testTaskCaseExecDTO.setTaskId(testTaskCaseResult.getTaskId());
        testTaskCaseExecDTO.setCaseId(testTaskCaseResult.getTestcaseId());
        testTaskCaseExecDTO.setResult(testTaskCaseResult.getResult());
        testTaskCaseExecDTO.setStatus(testTaskCaseResult.getStatus());
        testTaskCaseExecDTO.setLatestTime(testTaskCaseResult.getCreateTime());
        testTaskCaseExecDTO.setTruexecTime(testTaskCaseResult.getTruexecTime());
        User userById = userApi.findUserById(testTaskCaseResult.getExecBy());
        testTaskCaseExecDTO.setExecAvatarPath(userById.getAvatarPath());
        testTaskCaseExecDTO.setExecName(userById.getName());
        // 文本/步骤 字段赋值
        TestProductCase testProductCase = testProductCaseService.getById(testTaskCaseResult.getTestcaseId());
        if (testProductCase == null){
            testProductCase = BeanUtil.toBean(testProductCaseArchiveService.getById(testcaseId), TestProductCase.class);
        }
        testTaskCaseExecDTO.setStepType(testProductCase.getStepType());
        return success(testTaskCaseExecDTO);
    }

    @ApiOperation(value = "执行用例", notes = "执行用例")
    @PostMapping("/executeTaskCase")
    @SysLog(value = "执行用例", request = false)
    public R executeTaskCase(@RequestBody TestTaskCaseExecDTO testTaskCaseExecDTO) {
        Long taskId = Long.valueOf(testTaskCaseExecDTO.getTaskIdString());
        Long caseId = testTaskCaseExecDTO.getCaseId();
        String status;
        if (Objects.equals(testTaskCaseExecDTO.getStatus(), "undo")){
            status = null;
        }else{
            status = testTaskCaseExecDTO.getStatus();
        }
        String result = testTaskCaseExecDTO.getResult();
        if (caseId == null) {
            return R.fail("请选择需要执行得用例！");
        }
        TestProductCaseHistory testProductCaseHistory = testProductCaseHistoryService
                .getOne(Wraps.<TestProductCaseHistory>lbQ().eq(TestProductCaseHistory::getCaseId, caseId)
                        .eq(TestProductCaseHistory::getVersion, testTaskCaseExecDTO.getVersion()));

        Task byId = taskApi.getById(taskId);

        TestTaskCase one = baseService.getOne(new QueryWrapper<TestTaskCase>().eq("task_id", taskId)
                .eq("testcase_id", caseId));

        baseService.update(new UpdateWrapper<TestTaskCase>().eq("id", one.getId())
                .set("status", status)
                .set("exec_by", getUserId())
                .set("exec_etime", LocalDateTime.now()));

        TestTaskCaseResult testTaskCaseResult = new TestTaskCaseResult();
        testTaskCaseResult.setTaskId(taskId);
        testTaskCaseResult.setStatus(status);
        testTaskCaseResult.setTestcaseId(Long.valueOf(caseId));
        testTaskCaseResult.setName(byId.getName());
        if(testProductCaseHistory != null){
            testTaskCaseResult.setHistoryId(testProductCaseHistory.getId());
        }
        testTaskCaseResult.setExecBy(getUserId());
        testTaskCaseResult.setExecTime(LocalDateTime.now());
        testTaskCaseResult.setCreateTime(LocalDateTime.now());
        testTaskCaseResult.setCreatedBy(getUserId());
        testTaskCaseResult.setUpdateTime(LocalDateTime.now());
        testTaskCaseResult.setUpdatedBy(getUserId());
        testTaskCaseResult.setResult(result);
        testTaskCaseResult.setVersion(testTaskCaseExecDTO.getVersion());
        testTaskCaseResult.setTruexecTime(testTaskCaseExecDTO.getTruexecTime());
        testTaskCaseResultService.saveTestTaskCaseResult(testTaskCaseResult);
        return success("执行用例成功！");
    }

    @ApiOperation(value = "查询当前任务下执行用例历史执行结果", notes = "查询当前任务下执行用例历史执行结果")
    @GetMapping("/getHistoryExecuteTaskCaseResults")
    @SysLog(value = "查询当前任务下执行用例历史执行结果", request = false)
    public R<List<TestTaskCaseResult>> getHistoryExecuteTaskCaseResults(@RequestParam(value = "taskId") Long taskId,@RequestParam(value = "testcaseId") Long testcaseId) {

        List<TestTaskCaseResult> testTaskCaseResults = testTaskCaseResultService.list(
                Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTaskId, taskId)
                .eq(TestTaskCaseResult::getTestcaseId, testcaseId)
                .orderByDesc(SuperEntity::getCreateTime) );
        echoService.action(testTaskCaseResults);
        return success(testTaskCaseResults);
    }


    @ApiOperation(value = "根据测试任务id查询新增执行用例个数", notes = "根据测试任务id查询新增执行用例个数")
    @GetMapping("/findTrendByTaskId/{taskId}")
    @SysLog("根据测试任务id查询新增执行用例个数")
    public R<Map> findTrendByTaskId(@PathVariable("taskId") Long taskId)
    {
        return R.success(baseService.findTrendByProductId(taskId));
    }

    @ApiOperation(value = "根据测试任务id查询新增缺陷个数", notes = "根据测试计划id查询新增缺陷个数")
    @GetMapping("/findBugTrendByTaskId/{taskId}")
    @SysLog("根据测试计划id查询新增缺陷个数")
    public R<Map> findBugTrendByTaskId(@PathVariable("taskId") Long taskId)
    {
        return R.success(baseService.findBugTrendByProductId(taskId));
    }

    @ApiOperation(value = "根据测试任务id查询人员分配用例个数", notes = "根据测试任务id查询人员分配用例个数")
    @GetMapping("/findUserTrendByTaskId/{taskId}")
    @SysLog("根据测试任务id查询人员分配用例个数")
    public R<Map> findUserTrendByTaskId(@PathVariable("taskId") Long taskId)
    {
        return R.success(baseService.findUserTrendByProductId(taskId));
    }

    @ApiOperation(value = "概览-根据任务id查询基础数据", notes = "概览-根据任务id查询基础数据")
    @GetMapping("/getOverview/{id}")
    @SysLog(value = "概览-根据任务id查询基础数据", request = false)
    public R<Map> getOverview(@PathVariable("id") Long id) {
        //测试用例总数
        int sum = baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, id)).size();
        //未分配
        int  noExec= baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, id).isNull(TestTaskCase::getExecBy)).size();
        //测试人员
        List<TestTaskCase> list1 = baseService
                .list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, id).isNotNull(TestTaskCase::getExecBy));
        List<Long> collect1 = list1.stream().map(TestTaskCase::getExecBy).distinct().collect(Collectors.toList());
        int user = collect1.size();
        List<Long> collect =
                baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, id)).stream()
                        .map(TestTaskCase::getTestcaseId).distinct().collect(Collectors.toList());
        //已执行用例数
        List<TestTaskCaseResult> list = testTaskCaseResultService
                .list(Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTaskId, id)
                        .in(TestTaskCaseResult::getTestcaseId,collect));
        List<Long> collect2 =
                list.stream().map(TestTaskCaseResult::getTestcaseId).distinct().collect(Collectors.toList());
        Map map = new HashMap();
        map.put("noExec",noExec);
        map.put("sum",sum);
        map.put("user",user);
        map.put("execed", collect2.size());
        //执行率
        if(sum == 0){
            map.put("percent",0);
            return R.success(map);
        }
        int size = collect2.size();
        double percent =(double)(Math.round( size*100 / sum)/100.0);
        map.put("percent",percent);
        return R.success(map);
    }


//    @ApiOperation(value = "查询项目用例分布状态统计", notes = "查询项目用例分布状态统计")
//    @GetMapping("/status")
//    @SysLog("查询项目用例分布状态统计")
//    public R<Map<String, Object>> status(@RequestParam(value = "taskIds", required = false) List<Long> taskIds)
//    {
//        return R.success(baseService.status(taskIds));
//    }

    /**
     * 保存任务圈选用例
     *
     * @param data 项目计划DTO
     * @return 数据
     */
    @ApiOperation(value = "保存任务圈选用例", notes = "保存任务圈选用例")
    @PostMapping("/saveChoseTestCase")
    @SysLog(value = "保存任务圈选用例", request = false)
    @Transactional
    public R saveChoseTestCase(@RequestBody TestTaskCaseSaveDTO data)
    {

//        LbqWrapper<TestTaskCase> queryWrapLbuWrapper = Wraps.lbQ();
//        queryWrapLbuWrapper.eq(TestTaskCase::getTaskId, data.getTaskId());
//        List<TestTaskCase> list = baseService.list(queryWrapLbuWrapper);
//        List<Long> testCaseIds = list.stream().filter(a -> data.getTestcaseIds().contains(a.getTestcaseId()))
//                .map(TestTaskCase::getTestcaseId).collect(Collectors.toList());

//        if (testCaseIds.size() != 0)
//        {
//            List<TestCase> testCases = testCaseMapper.selectBatchIds(testCaseIds);
//            List<String> testCaseName = testCases.stream().map(TestCase::getName).collect(Collectors.toList());
//            String testCaseNameStr = testCaseName.stream().map(String::valueOf).collect(Collectors.joining(","));
//            return R.fail(testCaseNameStr + "用例重复添加！");
//        }
//        // 查询现有关联记录
//        List<Long> existingIds = baseService.listObjs(
//                Wraps.<TestTaskCase>lbQ()
//                        .select(TestTaskCase::getTestcaseId)
//                        .eq(TestTaskCase::getTaskId, data.getTaskId()),
//                Object::toString
//        ).stream().map(Long::valueOf).collect(Collectors.toList());
//
//
//        List<Long> newIds = data.getTestcaseIds();
//        // 计算需删除的旧记录
//        List<Long> idsToRemove = existingIds.stream()
//                .filter(id -> !newIds.contains(id))
//                .collect(Collectors.toList());
//        if (!idsToRemove.isEmpty()) {
//            baseService.remove(Wraps.<TestTaskCase>lbQ()
//                    .in(TestTaskCase::getTestcaseId, idsToRemove)
//                    .eq(TestTaskCase::getTaskId, data.getTaskId()));
//        }
//        List<Long> itemsToInsertIds = newIds.stream()
//                .filter(id -> !existingIds.contains(id))
//                .collect(Collectors.toList());

        List<TestTaskCase> testTaskCaseList = new ArrayList<>();
        for (Long testCaseId : data.getTestcaseIds())
        {
            TestProductCase byId = testProductCaseService.getById(testCaseId);
            TestTaskCase testTaskCase = new TestTaskCase();
            testTaskCase.setTestcaseId(testCaseId);
            testTaskCase.setTaskId(data.getTaskId());
            testTaskCase.setCreatedBy(data.getCreatedBy());
            testTaskCase.setId(UidGeneratorUtil.getId());
            testTaskCase.setName(byId.getName());
            testTaskCase.setPriority(byId.getPriority());
            testTaskCase.setCreateTime(LocalDateTime.now());
            testTaskCase.setVersion(byId.getVersion());
            testTaskCase.setCaseKey(byId.getCaseKey());
            testTaskCase.setTreeId(byId.getTreeId());
            testTaskCase.setTestreqId(byId.getTestreqId());
            testTaskCase.setProjectId(byId.getProjectId());
            testTaskCase.setModuleFunctionId(byId.getModuleFunctionId());
            testTaskCaseList.add(testTaskCase);
        }
        baseService.saveBatch(testTaskCaseList);

        return success();
    }

    @GetMapping("/getChoseTestCaseByTaskId/{taskId}")
    @ApiOperation(value = "根据任务id获取用例信息")
    public List<TestProductCase> getChoseTestCaseByTaskId(@PathVariable("taskId")Long taskId){
        List<Long> testCaseIds =
                baseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, taskId))
                        .stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
        List<TestProductCase> list = testProductCaseService.list(
                Wraps.<TestProductCase>lbQ().in(TestProductCase::getId, testCaseIds));
        return list;
    }


    @ApiOperation(value = "取消关联任务下用例", notes = "取消关联任务下用例")
    @DeleteMapping("/disassociateTestTaskCase")
    @SysLog(value = "取消关联任务下用例", request = false)
    @Transactional
    public R<Boolean> disassociateTestTaskCase(@RequestBody TestTaskCaseDelDTO testTaskCaseDelDTO)
    {
        baseService.remove(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTaskId, testTaskCaseDelDTO.getTaskId())
                .in(TestTaskCase::getTestcaseId,testTaskCaseDelDTO.getTestCaseIds()));
        if(!Objects.equals(testTaskCaseDelDTO.getTestCaseIds().size(),0) && !Objects.equals(testTaskCaseDelDTO.getTestCaseIds().size(),null)){
            testTaskCaseResultService.remove(Wraps.<TestTaskCaseResult>lbQ().eq(TestTaskCaseResult::getTaskId, testTaskCaseDelDTO.getTaskId())
                    .in(TestTaskCaseResult::getTestcaseId,testTaskCaseDelDTO.getTestCaseIds()));
        }
        return R.success(null, "取消关联用例成功！");
    }


    @ApiOperation(value = "修改测试任务下用例优先级", notes = "修改测试任务下用例优先级")
    @PostMapping("/updatePriority")
    @SysLog(value = "修改测试任务下用例优先级", request = false)
    public R<TestTaskCase> updatePriority(@RequestBody TestTaskCase data)
    {
        LbqWrapper<TestTaskCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestTaskCase::getTestcaseId,data.getTestcaseId())
                .eq(TestTaskCase::getTaskId, data.getTaskId());
        TestTaskCase testTaskCase = baseService.getOne(queryWrapLbuWrapper);
        testTaskCase.setPriority(data.getPriority());
        baseService.updateById(testTaskCase);

//        TestProductCase testCase = testProductCaseService.getById(data.getTestcaseId());
//        if (testCase!=null){
//            testCase.setPriority(data.getPriority());
//            testProductCaseService.updateById(testCase);
//        }else{
//            TestProductCaseArchive byId = testProductCaseArchiveService.getById(data.getTestcaseId());
//            byId.setPriority(data.getPriority());
//            testProductCaseArchiveService.updateById(byId);
//        }
        return R.success(testTaskCase, "修改测试计划下用例优先级成功！");
    }

    /**
     * 点击删除时，查询已关联缺陷用例列表
     * @param caseIds
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/4/8 9:54
     * @update xmy 2022/4/8 9:54
     * @since 1.0
     */
    @ApiOperation(value = "查询已关联缺陷用例列表", notes = "查询已关联缺陷用例列表")
    @PostMapping("/queryAssociatedTestTaskCase")
    @SysLog(value = "查询已关联缺陷用例列表", request = false)
    public R<List<TestTaskCase>> queryAssociatedTestTaskCase(@RequestBody List<Long> caseIds)
    {
        List<TestTaskCase> testTaskCaseList = new ArrayList<>();
        List<Bug> bugList = bugApi.queryByTestTaskIds(caseIds);
        List<Long> testTaskIds = bugList.stream().map(Bug::getTestTaskId).collect(Collectors.toList());
        if(testTaskIds.size() > 0){
            testTaskCaseList = baseService.list(Wraps.<TestTaskCase>lbQ().in(TestTaskCase::getId,testTaskIds));
        }
        return R.success(testTaskCaseList);
    }


}
