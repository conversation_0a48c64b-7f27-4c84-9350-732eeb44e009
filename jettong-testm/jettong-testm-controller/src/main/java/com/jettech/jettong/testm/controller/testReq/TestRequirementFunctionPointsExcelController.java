
package com.jettech.jettong.testm.controller.testReq;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsUpdateDTO;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.utils.xmind.SqlParseUtil;
import com.jettech.jettong.testm.vo.TestRequirementFunctionPointsEcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 通信DTC条目信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 通信DTC条目信息控制器
 * @projectName vone
 * @package com.cb.vone.alm.controller.issue
 * @className CommunicateDtcEntryController
 * @date 2023-08-28
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/testRequirementFunctionPoints/excel")
@Api(value = "TestRequirementFunctionPoints", tags = "通信DTC条目信息")
public class TestRequirementFunctionPointsExcelController extends
        SuperController<TestRequirementFunctionPointsService, Long, TestRequirementFunctionPoints, TestRequirementFunctionPointsPageQuery,
                        TestRequirementFunctionPointsSaveDTO, TestRequirementFunctionPointsUpdateDTO>
{

    private final TestRequirementFunctionPointsService functionPointsService;

    @InitBinder
    public void initBinder(WebDataBinder binder)
    {
        binder.setDisallowedFields(new String[]{"export"});
    }

    @Override
    public R<TestRequirementFunctionPoints> save( TestRequirementFunctionPointsSaveDTO saveDTO)
    {

        TestRequirementFunctionPoints model = BeanUtil.toBean(saveDTO, getEntityClass());
        getBaseService().save(model);
        return R.success(model);
    }

    @Override
    public R<TestRequirementFunctionPoints> update(TestRequirementFunctionPointsUpdateDTO updateDTO)
    {
        TestRequirementFunctionPoints model = BeanUtil.toBean(updateDTO, getEntityClass());
        getBaseService().updateById(model);
        return R.success(model);
    }

    @Override
    public QueryWrap<TestRequirementFunctionPoints> handlerWrapper(TestRequirementFunctionPoints model,
            PageParams<TestRequirementFunctionPointsPageQuery> params)
    {
        QueryWrap<TestRequirementFunctionPoints> queryWrap = Wraps.q(model, params.getExtra(), getEntityClass());

//        if (model.getVersionId() == null)
//        {
//            queryWrap.lambda().isNull(CommunicateDtcEntry::getVersionId);
//        }
        //自定义筛选
        Map<String, Object> extra = params.getExtra();
        if (!extra.isEmpty())
        {
            String sql = SqlParseUtil.parseToString(new TestRequirementFunctionPoints(), extra);
            queryWrap.last(sql);
        }
        return queryWrap;
    }

    @GetMapping(value = "/template/{projectId}/{taskId}", produces = "application/octet-stream")
    @ApiOperation(value = "下载导入模板")
    @SysLog(value = "下载导入模板", optType = OptLogTypeEnum.DOWNLOAD)
    public void template(HttpServletResponse response,@PathVariable(value = "projectId") Long projectId,@PathVariable(value = "code", required = false) String code)
    {
        ExportParams exportParams = new ExportParams(null,
                "测试分析");
        Requirement requirement =new Requirement();
        requirement.setProjectId(projectId);
        List<Requirement> requirements = Collections.emptyList();
        if("TEST".equals(code)){
            List<Testreq> testreqs = requirementApi.selectTestReqByCondition(new Testreq().setProjectId(projectId));
            requirements = BeanUtil.copyToList(testreqs, Requirement.class);
        }else{
            requirements = requirementApi.selectRequirementByCondition(requirement);
        }
        List<ProductModuleFunction> productModuleFunctions =
                productInfoApi.selectProductModuleFunctionByProjectId(projectId);
        String[] s1 = requirements.stream()
                .map(r -> "(" + r.getCode() + ")" + r.getName())
                .toArray(String[]::new);
        String[] s2 = productModuleFunctions.stream()
                .map(r -> "(" + r.getCode() + ")" + r.getName())
                .toArray(String[]::new);
        if(s1.length==0){
            //抛出异常
            throw new BizException("下载导入模板失败：请先创建需求!");
        }
        if(s2.length==0){
            throw new BizException("下载导入模板失败：请先创建功能交易!");
        }
        //s3 是，否
        String[] s3 = {"是","否"};
        TestRequirementFunctionPointsEcelVo testRequirementFunctionPointsEcelVo = new TestRequirementFunctionPointsEcelVo();
        List list = new ArrayList<>();
        list.add(testRequirementFunctionPointsEcelVo);

        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                TestRequirementFunctionPointsEcelVo.class, list);
        Map<Integer, Map<Integer, String[]>> dropDownMap = Maps.newHashMapWithExpectedSize(1);
        Map<Integer, String[]> columnMap = Maps.newHashMapWithExpectedSize(1);
        dropDownMap.put(0, columnMap);
        columnMap.put(0, s1);
        columnMap.put(1, s2);
        columnMap.put(5, s3);
        columnMap.put(6, s3);
        ExcelDownLoadUtil.makeDropDownMap(workbook, dropDownMap,1);
        // 下载导入模板
        try
        {
            ExcelDownLoadUtil.export(response, workbook, "测试分析导入模板.xls");
        }
        catch (IOException e)
        {
            log.error("下载测试分析导入模板失败，原因:{}", e.getMessage(), e);
        }
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出")
    @SysLog(value = "导出", optType = OptLogTypeEnum.EXPORT)
    public void export(HttpServletResponse response, @RequestBody PageParams<TestRequirementFunctionPointsPageQuery> params)
    {
        params.setSize(9999l);
        IPage<TestRequirementFunctionPoints> page = params.buildPage(TestRequirementFunctionPoints.class);
        TestRequirementFunctionPointsPageQuery model = params.getModel();
        List<Long> functionIds = baseService.getFunctionIdsByProjectAndModule(model.getProjectId(), model.getFunctionAndModuleId());
        if(functionIds.isEmpty()){
            return  ;
        }
        LbqWrapper<TestRequirementFunctionPoints> wrapper = Wraps.lbQ();
        wrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, model.getIssueTestReqId())
                .eq(TestRequirementFunctionPoints::getFunctionId, model.getFunctionId())
                .like(TestRequirementFunctionPoints::getFunctionPoint, model.getFunctionPoint())
                .like(TestRequirementFunctionPoints::getTestPoints, model.getTestPoints())
                .eq(TestRequirementFunctionPoints::getRuleType, model.getRuleType())
                .eq(TestRequirementFunctionPoints::getInvolveAccount, model.getInvolveAccount())
                .eq(TestRequirementFunctionPoints::getInvolveBatch, model.getInvolveBatch())
                .eq(TestRequirementFunctionPoints::getPriority, model.getPriority())
                .in(TestRequirementFunctionPoints::getFunctionId, functionIds);

        baseService.page(page, wrapper);

        List<TestRequirementFunctionPointsEcelVo> entryList = BeanUtil.copyToList(page.getRecords(), TestRequirementFunctionPointsEcelVo.class);
        ExportParams exportParams = new ExportParams(null, "测试分析");
        Requirement requirement =new Requirement();
        requirement.setProjectId(model.getProjectId());
        List<Requirement> requirements = requirementApi.selectRequirementByCondition(requirement);
        List<Testreq> testReqs =
                requirementApi.selectTestReqByCondition(new Testreq().setProjectId(model.getProjectId()));
        //testreqs转为requirement
        List<Requirement> testReq = BeanUtil.copyToList(testReqs, Requirement.class);
        requirements.addAll(testReq);
        List<ProductModuleFunction> productModuleFunctions =
                productInfoApi.selectProductModuleFunctionByProjectId(model.getProjectId());
        Map<Long, Requirement> requirementMap =
                requirements.stream().collect(Collectors.toMap(Requirement::getId, Function.identity()));
        Map<Long, ProductModuleFunction> productModuleFunctionMap = productModuleFunctions.stream()
                .collect(Collectors.toMap(ProductModuleFunction::getId, Function.identity()));
        entryList.stream().forEach(e -> {
            e.setAccount(e.getInvolveAccount().toString());
            e.setBatch(e.getInvolveBatch().toString());
            e.setIssueTestReq("("+requirementMap.get(e.getIssueTestReqId()).getCode()+")"+requirementMap.get(e.getIssueTestReqId()).getName());
            e.setFunction("("+productModuleFunctionMap.get(e.getFunctionId()).getCode()+")"+productModuleFunctionMap.get(e.getFunctionId()).getName());
        });
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestRequirementFunctionPointsEcelVo.class, entryList);
        try
        {
            ExcelDownLoadUtil.export(response, workbook, "测试分析信息" + System.currentTimeMillis() + ".xls");
        }
        catch (IOException e)
        {
            log.error("导出失败", e);
        }
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入")
    @SysLog(value = "导入", optType = OptLogTypeEnum.IMPORT)
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> importExcel(@RequestParam(value = "file")MultipartFile file, @RequestParam Long projectId, @RequestParam(required = false) Long versionId)
    {
        ImportParams dataTypeParams = new ImportParams();
        dataTypeParams.setTitleRows(0);
        dataTypeParams.setHeadRows(1);
        dataTypeParams.setNeedVerify(false);
        //获取表头下的数据
        ExcelImportResult<TestRequirementFunctionPointsEcelVo> entryList = null;
        try (InputStream is = file.getInputStream())
        {
            entryList = ExcelImportUtil.importExcelMore(is, TestRequirementFunctionPointsEcelVo.class, dataTypeParams);
        }
        catch (Exception e)
        {
            log.error("导入失败", e);
            return R.fail("导入失败");
        }
        List<TestRequirementFunctionPointsEcelVo> list = entryList.getList();
        if (CollUtil.isEmpty(list))
        {
            return R.fail("导入数据为空");
        }
        list = list.stream().filter(n -> BeanUtil.isNotEmpty(n)).collect(Collectors.toList());
        Requirement requirement =new Requirement();
        requirement.setProjectId(projectId);
        List<Requirement> requirements = requirementApi.selectRequirementByCondition(requirement);
        List<Testreq> testreqs =
                requirementApi.selectTestReqByCondition(new Testreq().setProjectId(projectId));
        //testreqs转为requirement
        List<Requirement> testReq = BeanUtil.copyToList(testreqs, Requirement.class);
        requirements.addAll(testReq);
        List<ProductModuleFunction> productModuleFunctions =
                productInfoApi.selectProductModuleFunctionByProjectId(projectId);
        Map<String, Requirement> requirementMap =
                requirements.stream().collect(Collectors.toMap(Requirement::getCode, Function.identity()));
        Map<String, ProductModuleFunction> productModuleFunctionMap = productModuleFunctions.stream()
                .collect(Collectors.toMap(ProductModuleFunction::getCode, Function.identity()));
        list.forEach(
                n -> {
                    String req = n.getIssueTestReq().split("\\(")[1].split("\\)")[0];
                    n.setIssueTestReqId(requirementMap.get(req).getId());
                    String function = n.getFunction().split("\\(")[1].split("\\)")[0];
                    n.setFunctionId(productModuleFunctionMap.get(function).getId());
                    n.setInvolveAccount("是".equals(n.getAccount()));
                    n.setInvolveBatch("是".equals(n.getBatch()));
                }
        );
        List<TestRequirementFunctionPoints> dtcEntryList = BeanUtil.copyToList(list, TestRequirementFunctionPoints.class);
        baseService.saveBatch(dtcEntryList);
        return R.success(true,"导入成功"+dtcEntryList.size()+"条，失败0条");
    }


}
