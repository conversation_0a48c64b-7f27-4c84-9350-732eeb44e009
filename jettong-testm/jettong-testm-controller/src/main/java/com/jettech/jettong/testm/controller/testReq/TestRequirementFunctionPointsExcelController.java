
package com.jettech.jettong.testm.controller.testReq;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsUpdateDTO;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.service.TestRequirementAnalysisConfigService;
import com.jettech.jettong.testm.vo.TestRequirementFunctionPointsExcelVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFDataValidation;
import org.apache.poi.xssf.usermodel.XSSFDataValidationConstraint;
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 测试分析功能要点Excel操作控制器
 *
 * 主要功能：
 * 1. 基于fieldconfig配置的模板导出 - 根据数据库中存储的字段配置动态生成Excel模板
 * 2. 数据导入 - 支持字段约束校验，包括系统、交易及其他字段的取值验证
 * 3. 筛选数据导出 - 基于查询条件的数据导出功能
 *
 * <AUTHOR>
 * @version 2.0
 * @description 测试分析功能要点Excel操作控制器，支持基于fieldconfig的动态配置
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.controller.testReq
 * @className TestRequirementFunctionPointsExcelController
 * @date 2025-09-03
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/testRequirementFunctionPoints/excel")
@Api(value = "TestRequirementFunctionPointsExcel", tags = "测试分析功能要点Excel操作")
public class TestRequirementFunctionPointsExcelController extends
        SuperController<TestRequirementFunctionPointsService, Long, TestRequirementFunctionPoints, TestRequirementFunctionPointsPageQuery,
                        TestRequirementFunctionPointsSaveDTO, TestRequirementFunctionPointsUpdateDTO>
{
    // 常量定义
    private static final String EXCEL_SHEET_NAME = "测试分析";
    private static final String TEMPLATE_FILE_NAME = "测试分析导入模板.xls";
    private static final String EXPORT_FILE_PREFIX = "测试分析信息";
    private static final int SYSTEM_COLUMN_INDEX = 0;
    private static final int TRANSACTION_COLUMN_INDEX = 1;
    private static final int FIELD_CONFIG_START_COLUMN_INDEX = 2;

    // 节点类型常量：nodeType=0为系统，nodeType=1为模块，nodeType=2为交易
    private static final int NODE_TYPE_SYSTEM = 0;
    private static final int NODE_TYPE_MODULE = 1;
    private static final int NODE_TYPE_TRANSACTION = 2;

    private final TestRequirementFunctionPointsService functionPointsService;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final TestRequirementAnalysisConfigService configService;
    private final RequirementApi requirementApi;
    private final ProductInfoApi productInfoApi;

    @InitBinder
    public void initBinder(WebDataBinder binder)
    {
        binder.setDisallowedFields(new String[]{"export"});
    }

    // 移除了原有的save、update和handlerWrapper方法，因为这些方法应该在主Controller中处理，而不是在Excel专用Controller中

    @GetMapping(value = "/template/{projectId}/{taskId}", produces = "application/octet-stream")
    @ApiOperation(value = "下载导入模板")
    @SysLog(value = "下载导入模板", optType = OptLogTypeEnum.DOWNLOAD)
    public void template(HttpServletResponse response,
                        @PathVariable(value = "projectId") Long projectId,
                        @PathVariable(value = "taskId") Long taskId,
                        @RequestParam(value = "reqId", required = false) Long reqId)
    {
        try {
            // 获取启用的fieldconfig配置
            TestRequirementAnalysisConfig config = getEnabledConfig();
            if (config == null || config.getFieldConfig() == null) {
                throw new BizException("下载导入模板失败：未找到启用的字段配置!");
            }

            // 解析fieldconfig
            JSONArray fieldConfigs = JSON.parseArray(config.getFieldConfig());

            // 获取任务关联的系统和交易
            SystemAndTransactionData systemAndTransactionData = getSystemAndTransactionByTask(projectId, taskId, reqId);

            // 构建Excel模板
            Workbook workbook = buildTemplateWorkbook(fieldConfigs, systemAndTransactionData);

            // 下载导入模板
            ExcelDownLoadUtil.export(response, workbook, TEMPLATE_FILE_NAME);
        }
        catch (IOException e)
        {
            log.error("下载测试分析导入模板失败，原因:{}", e.getMessage(), e);
            throw new BizException("下载导入模板失败：" + e.getMessage());
        }
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出")
    @SysLog(value = "导出", optType = OptLogTypeEnum.EXPORT)
    public void export(HttpServletResponse response, @RequestBody PageParams<TestRequirementFunctionPointsPageQuery> params)
    {
        try {
            // 获取启用的fieldconfig配置
            TestRequirementAnalysisConfig config = getEnabledConfig();
            if (config == null || config.getFieldConfig() == null) {
                throw new BizException("导出失败：未找到启用的字段配置!");
            }

            // 解析fieldconfig
            JSONArray fieldConfigs = JSON.parseArray(config.getFieldConfig());

            // 查询数据（参考TestRequirementFunctionPointsController的query方法）
            List<TestRequirementFunctionPoints> dataList = queryDataForExport(params);

            if (dataList.isEmpty()) {
                throw new BizException("没有数据可导出");
            }

            // 获取系统和交易数据用于显示
            TestRequirementFunctionPointsPageQuery model = params.getModel();
            SystemAndTransactionData systemAndTransactionData = getSystemAndTransactionByTask(model.getProjectId(), model.getTaskId(), model.getIssueTestReqId());

            // 构建导出工作簿
            Workbook workbook = buildExportWorkbook(dataList, fieldConfigs, systemAndTransactionData, model.getProjectId());

            // 导出文件
            ExcelDownLoadUtil.export(response, workbook, EXPORT_FILE_PREFIX + System.currentTimeMillis() + ".xls");

        } catch (IOException e) {
            log.error("导出失败", e);
            throw new BizException("导出失败：" + e.getMessage());
        }
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入")
    @SysLog(value = "导入", optType = OptLogTypeEnum.IMPORT)
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> importExcel(@RequestParam(value = "file") MultipartFile file,
                                  @RequestParam Long projectId,
                                  @RequestParam Long taskId,
                                  @RequestParam(value = "reqId", required = false) Long reqId)
    {
        try {
            // 获取启用的fieldconfig配置
            TestRequirementAnalysisConfig config = getEnabledConfig();
            if (config == null || config.getFieldConfig() == null) {
                return R.fail("导入失败：未找到启用的字段配置!");
            }

            // 解析fieldconfig
            JSONArray fieldConfigs = JSON.parseArray(config.getFieldConfig());

            // 导入Excel数据
            ImportParams dataTypeParams = new ImportParams();
            dataTypeParams.setTitleRows(0);
            dataTypeParams.setHeadRows(1);
            dataTypeParams.setNeedVerify(false);

            ExcelImportResult<TestRequirementFunctionPointsExcelVo> entryList = null;
            try (InputStream is = file.getInputStream()) {
                entryList = ExcelImportUtil.importExcelMore(is, TestRequirementFunctionPointsExcelVo.class, dataTypeParams);
            }

            List<TestRequirementFunctionPointsExcelVo> list = entryList.getList();
            if (CollUtil.isEmpty(list)) {
                return R.fail("导入数据为空");
            }

            list = list.stream().filter(n -> BeanUtil.isNotEmpty(n)).collect(Collectors.toList());

            // 获取系统和交易数据用于校验
            SystemAndTransactionData systemAndTransactionData = getSystemAndTransactionByTask(projectId, taskId, reqId);

            // 校验和转换数据
            List<TestRequirementFunctionPoints> validatedData = validateAndConvertImportData(
                    list, fieldConfigs, systemAndTransactionData, projectId, taskId);

            // 保存数据
            baseService.saveBatch(validatedData);

            return R.success(true, "导入成功" + validatedData.size() + "条，失败0条");

        } catch (Exception e) {
            log.error("导入失败", e);
            return R.fail("导入失败：" + e.getMessage());
        }
    }

    /**
     * 获取启用的配置
     */
    private TestRequirementAnalysisConfig getEnabledConfig() {
        List<TestRequirementAnalysisConfig> configs = configService.list(
                Wraps.<TestRequirementAnalysisConfig>lbQ().eq(TestRequirementAnalysisConfig::getEnable, true));
        return configs.isEmpty() ? null : configs.get(0);
    }

    /**
     * 根据任务获取关联的系统和交易信息
     * 调用 ProductModuleFunctionApi.findByProjectId API
     * 返回的是树形结构数据，需要递归提取系统和交易节点
     * 参考curl: 'http://jettong.dev.jettech.cn/api/product/product/productModuleFunction/findByProjectId/1962330750892113920?reqId=1962331161988431872&bizId=1962346497966080000'
     */
    private SystemAndTransactionData getSystemAndTransactionByTask(Long projectId, Long taskId, Long reqId) {
        try {
            // 调用API获取项目关联的模块交易范围，传入bizId(taskId)和reqId参数
            List<ProductModuleFunction> treeData = productModuleFunctionApi.findByProjectId(projectId, taskId, reqId, null);

            if (treeData.isEmpty()) {
                log.info("项目 {} 任务 {} 没有关联的交易，使用项目级别数据", projectId, taskId);
                return getSystemAndTransactionData(projectId, "任务关联");
            }

            // 递归提取树形结构中的系统和交易节点
            List<ProductModuleFunction> systems = new ArrayList<>();
            List<ProductModuleFunction> transactions = new ArrayList<>();
            extractSystemsAndTransactions(treeData, systems, transactions);

            // 如果没有系统或交易，则从项目级别获取
            if (systems.isEmpty() || transactions.isEmpty()) {
                log.info("项目 {} 任务 {} 关联的交易中缺少系统或交易，使用项目级别数据", projectId, taskId);
                return getSystemAndTransactionData(projectId, "任务关联");
            }

            log.info("成功获取项目 {} 任务 {} 关联的系统 {} 个，交易 {} 个", projectId, taskId, systems.size(), transactions.size());
            return new SystemAndTransactionData(systems, transactions);

        } catch (Exception e) {
            log.warn("获取任务关联的系统交易失败，使用项目级别数据作为备选: {}", e.getMessage());
            return getSystemAndTransactionData(projectId, "任务关联");
        }
    }

    /**
     * 递归提取树形结构中的系统和交易节点
     * nodeType: 0=系统, 1=模块, 2=交易
     */
    private void extractSystemsAndTransactions(List<ProductModuleFunction> nodes,
                                               List<ProductModuleFunction> systems,
                                               List<ProductModuleFunction> transactions) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }

        for (ProductModuleFunction node : nodes) {
            if (node.getNodeType() != null) {
                switch (node.getNodeType()) {
                    case NODE_TYPE_SYSTEM: // 0 - 系统
                        systems.add(node);
                        break;
                    case NODE_TYPE_TRANSACTION: // 2 - 交易
                        transactions.add(node);
                        break;
                    case NODE_TYPE_MODULE: // 1 - 模块，继续递归处理子节点
                    default:
                        // 对于模块节点或其他类型，继续递归处理子节点
                        break;
                }
            }

            // 递归处理子节点
            if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                extractSystemsAndTransactions(node.getChildren(), systems, transactions);
            }
        }
    }

    /**
     * 获取系统和交易数据的通用方法
     */
    private SystemAndTransactionData getSystemAndTransactionData(Long projectId, String context) {
        // 获取项目关联的系统和交易范围
        List<ProductModuleFunction> allFunctions = productInfoApi.selectProductModuleFunctionByProjectId(projectId);

        // 过滤出系统和交易
        List<ProductModuleFunction> systems = allFunctions.stream()
                .filter(f -> f.getNodeType() != null && f.getNodeType() == NODE_TYPE_SYSTEM)
                .collect(Collectors.toList());

        List<ProductModuleFunction> transactions = allFunctions.stream()
                .filter(f -> f.getNodeType() != null && f.getNodeType() == NODE_TYPE_TRANSACTION)
                .collect(Collectors.toList());

        if (systems.isEmpty()) {
            throw new BizException(context + "失败：请先创建系统!");
        }
        if (transactions.isEmpty()) {
            throw new BizException(context + "失败：请先创建交易!");
        }

        return new SystemAndTransactionData(systems, transactions);
    }

    /**
     * 构建模板工作簿（支持级联选择）
     */
    private Workbook buildTemplateWorkbook(JSONArray fieldConfigs, SystemAndTransactionData data) {
        // 构建下拉约束
        Map<Integer, String[]> dropDownConstraints = buildDropDownConstraints(fieldConfigs, data);

        // 使用现有的TestRequirementFunctionPointsEcelVo创建模板
        ExportParams exportParams = new ExportParams(null, EXCEL_SHEET_NAME);
        TestRequirementFunctionPointsExcelVo templateVo = new TestRequirementFunctionPointsExcelVo();
        List<TestRequirementFunctionPointsExcelVo> templateList = new ArrayList<>();
        templateList.add(templateVo);

        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestRequirementFunctionPointsExcelVo.class, templateList);

        // 应用基础下拉约束
        Map<Integer, Map<Integer, String[]>> dropDownMap = Maps.newHashMapWithExpectedSize(1);
        dropDownMap.put(0, dropDownConstraints);
        ExcelDownLoadUtil.makeDropDownMap(workbook, dropDownMap, 1);

        // 应用级联选择约束
        applyCascadingConstraints(workbook, data);

        return workbook;
    }

    /**
     * 构建下拉约束的通用方法（支持级联选择）
     */
    private Map<Integer, String[]> buildDropDownConstraints(JSONArray fieldConfigs, SystemAndTransactionData data) {
        Map<Integer, String[]> dropDownConstraints = new HashMap<>();

        // 第一列：系统
        String[] systemOptions = data.getSystems().stream()
                .map(s -> "(" + s.getCode() + ")" + s.getName())
                .toArray(String[]::new);
        dropDownConstraints.put(SYSTEM_COLUMN_INDEX, systemOptions);

        // 第二列：交易 - 这里先放所有交易，后面会通过级联约束来限制
        String[] transactionOptions = data.getTransactions().stream()
                .map(t -> "(" + t.getCode() + ")" + t.getName())
                .toArray(String[]::new);
        dropDownConstraints.put(TRANSACTION_COLUMN_INDEX, transactionOptions);

        // 根据fieldconfig添加其他列的约束
        int columnIndex = FIELD_CONFIG_START_COLUMN_INDEX;
        for (int i = 0; i < fieldConfigs.size(); i++) {
            JSONObject fieldConfig = fieldConfigs.getJSONObject(i);

            // 处理下拉选项
            JSONArray options = fieldConfig.getJSONArray("options");
            if (options != null && !options.isEmpty()) {
                String[] optionValues = new String[options.size()];
                for (int j = 0; j < options.size(); j++) {
                    JSONObject option = options.getJSONObject(j);
                    optionValues[j] = option.getString("text");
                }
                dropDownConstraints.put(columnIndex, optionValues);
            }
            columnIndex++;
        }

        return dropDownConstraints;
    }

    /**
     * 应用级联选择约束
     * 实现系统-交易的级联选择功能
     */
    private void applyCascadingConstraints(Workbook workbook, SystemAndTransactionData data) {
        try {
            Sheet sheet = workbook.getSheetAt(0);

            // 创建隐藏的数据表用于存储级联数据
            Sheet dataSheet = workbook.createSheet("CascadingData");
            workbook.setSheetHidden(workbook.getSheetIndex(dataSheet), true);

            // 构建系统-交易映射关系
            Map<String, List<ProductModuleFunction>> systemTransactionMap = buildSystemTransactionMapping(data);

            // 在隐藏表中创建级联数据
            createCascadingDataInSheet(dataSheet, systemTransactionMap);

            // 为交易列添加级联约束
            addCascadingValidationToTransactionColumn(sheet, systemTransactionMap.keySet().size());

            log.info("成功应用级联选择约束，系统数量: {}", systemTransactionMap.size());

        } catch (Exception e) {
            log.warn("应用级联选择约束失败，将使用普通下拉约束: {}", e.getMessage());
        }
    }

    /**
     * 构建系统-交易映射关系
     */
    private Map<String, List<ProductModuleFunction>> buildSystemTransactionMapping(SystemAndTransactionData data) {
        Map<String, List<ProductModuleFunction>> systemTransactionMap = new LinkedHashMap<>();

        for (ProductModuleFunction system : data.getSystems()) {
            String systemKey = "(" + system.getCode() + ")" + system.getName();

            // 找到属于该系统的交易
            List<ProductModuleFunction> systemTransactions = data.getTransactions().stream()
                    .filter(transaction -> isTransactionBelongsToSystem(transaction, system))
                    .collect(Collectors.toList());

            systemTransactionMap.put(systemKey, systemTransactions);
        }

        return systemTransactionMap;
    }

    /**
     * 判断交易是否属于指定系统
     * 这里需要根据实际的数据结构来判断层级关系
     */
    private boolean isTransactionBelongsToSystem(ProductModuleFunction transaction, ProductModuleFunction system) {
        // 方法1: 通过productId判断（如果交易和系统有相同的productId）
        if (transaction.getProductId() != null && system.getId() != null) {
            return transaction.getProductId().equals(system.getId());
        }

        // 方法2: 通过层级关系判断（需要递归查找父级）
        // 这里可以根据实际的数据结构来实现更复杂的层级判断逻辑

        // 默认情况：如果无法确定层级关系，则认为所有交易都属于所有系统
        return true;
    }

    /**
     * 在隐藏表中创建级联数据
     */
    private void createCascadingDataInSheet(Sheet dataSheet, Map<String, List<ProductModuleFunction>> systemTransactionMap) {
        int rowIndex = 0;

        // 创建系统列表
        Row systemHeaderRow = dataSheet.createRow(rowIndex++);
        systemHeaderRow.createCell(0).setCellValue("系统列表");

        for (String systemName : systemTransactionMap.keySet()) {
            Row row = dataSheet.createRow(rowIndex++);
            row.createCell(0).setCellValue(systemName);
        }

        // 为每个系统创建对应的交易列表
        int columnIndex = 1;
        for (Map.Entry<String, List<ProductModuleFunction>> entry : systemTransactionMap.entrySet()) {
            String systemName = entry.getKey();
            List<ProductModuleFunction> transactions = entry.getValue();

            // 创建交易列表标题
            Row headerRow = dataSheet.getRow(0);
            if (headerRow == null) {
                headerRow = dataSheet.createRow(0);
            }
            headerRow.createCell(columnIndex).setCellValue(systemName + "_交易");

            // 创建交易数据
            for (int i = 0; i < transactions.size(); i++) {
                ProductModuleFunction transaction = transactions.get(i);
                Row row = dataSheet.getRow(i + 1);
                if (row == null) {
                    row = dataSheet.createRow(i + 1);
                }
                String transactionValue = "(" + transaction.getCode() + ")" + transaction.getName();
                row.createCell(columnIndex).setCellValue(transactionValue);
            }

            columnIndex++;
        }
    }

    /**
     * 为交易列添加级联约束
     */
    private void addCascadingValidationToTransactionColumn(Sheet sheet, int systemCount) {
        if (!(sheet instanceof XSSFSheet)) {
            log.warn("当前Excel格式不支持级联约束，请使用.xlsx格式");
            return;
        }

        XSSFSheet xssfSheet = (XSSFSheet) sheet;
        XSSFDataValidationHelper validationHelper = new XSSFDataValidationHelper(xssfSheet);

        // 为交易列（第二列，索引为1）添加数据验证
        // 这里使用INDIRECT函数来实现级联选择
        // 公式示例: =INDIRECT(SUBSTITUTE(A2," ","_")&"_交易")
        String formula = "INDIRECT(SUBSTITUTE(A2,\" \",\"_\")&\"_交易\")";

        XSSFDataValidationConstraint constraint = (XSSFDataValidationConstraint)
                validationHelper.createFormulaListConstraint(formula);

        // 应用到交易列的所有行（从第2行开始，假设有1000行数据）
        CellRangeAddressList addressList = new CellRangeAddressList(1, 1000, 1, 1);
        XSSFDataValidation validation = (XSSFDataValidation) validationHelper.createValidation(constraint, addressList);

        validation.setShowErrorBox(true);
        validation.setErrorStyle(DataValidation.ErrorStyle.STOP);
        validation.createErrorBox("输入错误", "请从下拉列表中选择有效的交易");
        validation.setShowPromptBox(true);
        validation.createPromptBox("提示", "请先选择系统，然后选择对应的交易");

        xssfSheet.addValidationData(validation);
    }

    /**
     * 查询数据用于导出（参考TestRequirementFunctionPointsController的query方法）
     * 参考curl请求：
     * curl 'http://jettong.dev.jettech.cn/api/testm/testRequirementFunctionPoints/page'
     * --data-raw '{"size":20,"current":1,"sort":"createTime","extra":{"tableId":"1","tableSave":true,"boardField":[],"fixedView":[],"height":0},"model":{"functionAndModuleId":"1959505688296161280","functionId":"","projectId":"1962330750892113920","taskId":"1962346497966080000","issueTestReqId":"1962331161988431872"}}'
     */
    private List<TestRequirementFunctionPoints> queryDataForExport(PageParams<TestRequirementFunctionPointsPageQuery> params) {
        params.setSize(9999L); // 设置大数量以获取所有数据
        IPage<TestRequirementFunctionPoints> page = params.buildPage(TestRequirementFunctionPoints.class);
        TestRequirementFunctionPointsPageQuery model = params.getModel();

        // 获取功能ID列表（根据functionAndModuleId）
        List<Long> functionIds = new ArrayList<>();
        if (model.getFunctionAndModuleId() != null && model.getFunctionAndModuleId() != 0L) {
            functionIds = baseService.getFunctionIdsByFunctionId(model.getFunctionAndModuleId());
            if (functionIds.isEmpty()) {
                log.warn("根据functionAndModuleId {} 未找到关联的功能ID", model.getFunctionAndModuleId());
                return new ArrayList<>();
            }
        }

        // 构建查询条件（参考curl请求中的model参数）
        LbqWrapper<TestRequirementFunctionPoints> wrapper = Wraps.lbQ();
        wrapper.eq(TestRequirementFunctionPoints::getProjectId, model.getProjectId())
                .eq(TestRequirementFunctionPoints::getTaskId, model.getTaskId())
                .eq(TestRequirementFunctionPoints::getIssueTestReqId, model.getIssueTestReqId())
                .eq(TestRequirementFunctionPoints::getFunctionId, model.getFunctionId())
                .eq(TestRequirementFunctionPoints::getStateCode, model.getStateCode())
                .like(TestRequirementFunctionPoints::getFunctionPoint, model.getFunctionPoint())
                .like(TestRequirementFunctionPoints::getTestPoints, model.getTestPoints())
                .eq(TestRequirementFunctionPoints::getRuleType, model.getRuleType())
                .eq(TestRequirementFunctionPoints::getInvolveAccount, model.getInvolveAccount())
                .eq(TestRequirementFunctionPoints::getInvolveBatch, model.getInvolveBatch())
                .eq(TestRequirementFunctionPoints::getPriority, model.getPriority());

        // 如果有functionAndModuleId，则按功能ID列表过滤
        if (!functionIds.isEmpty()) {
            wrapper.in(TestRequirementFunctionPoints::getFunctionId, functionIds);
        }

        // 处理extra参数中的自定义筛选（如果有的话）
        Map<String, Object> extra = params.getExtra();
        if (extra != null && !extra.isEmpty()) {
            // 这里可以根据需要处理extra中的自定义筛选条件
            log.debug("处理extra筛选条件: {}", extra);
        }

        baseService.page(page, wrapper);
        log.info("查询到 {} 条数据用于导出", page.getRecords().size());
        return page.getRecords();
    }

    /**
     * 构建导出工作簿
     */
    private Workbook buildExportWorkbook(List<TestRequirementFunctionPoints> dataList,
                                         JSONArray fieldConfigs,
                                         SystemAndTransactionData systemAndTransactionData,
                                         Long projectId) {

        // 构建系统和交易的映射
        Map<Long, ProductModuleFunction> systemMap = systemAndTransactionData.getSystems().stream()
                .collect(Collectors.toMap(ProductModuleFunction::getId, Function.identity()));

        Map<Long, ProductModuleFunction> transactionMap = systemAndTransactionData.getTransactions().stream()
                .collect(Collectors.toMap(ProductModuleFunction::getId, Function.identity()));

        // 转换为导出VO
        List<TestRequirementFunctionPointsExcelVo> exportList = new ArrayList<>();

        for (TestRequirementFunctionPoints entity : dataList) {
            TestRequirementFunctionPointsExcelVo vo = new TestRequirementFunctionPointsExcelVo();

            // 设置系统信息（如果有系统ID字段的话）
            // 这里需要根据实际的实体结构来设置

            // 设置交易信息
            ProductModuleFunction transaction = transactionMap.get(entity.getFunctionId());
            if (transaction != null) {
                vo.setFunction("(" + transaction.getCode() + ")" + transaction.getName());
                vo.setFunctionId(transaction.getId());
            }

            // 根据fieldconfig设置其他字段
            setExportFieldsByConfig(vo, entity, fieldConfigs);

            exportList.add(vo);
        }

        // 创建工作簿
        ExportParams exportParams = new ExportParams(null, EXCEL_SHEET_NAME);
        return ExcelExportUtil.exportExcel(exportParams, TestRequirementFunctionPointsExcelVo.class, exportList);
    }

    /**
     * 根据fieldconfig设置导出字段
     */
    private void setExportFieldsByConfig(TestRequirementFunctionPointsExcelVo vo,
                                         TestRequirementFunctionPoints entity,
                                         JSONArray fieldConfigs) {
        for (int i = 0; i < fieldConfigs.size(); i++) {
            JSONObject fieldConfig = fieldConfigs.getJSONObject(i);
            String key = fieldConfig.getString("key");

            // 从实体中获取字段值
            Object fieldValue = getFieldValueFromEntity(entity, key);

            // 转换为显示值
            Object displayValue = convertToDisplayValue(fieldValue, fieldConfig);

            // 设置到VO中
            setFieldValueToVo(vo, key, displayValue);
        }
    }

    /**
     * 从实体中获取字段值
     */
    private Object getFieldValueFromEntity(TestRequirementFunctionPoints entity, String fieldKey) {
        switch (fieldKey) {
            case "functionPoint":
                return entity.getFunctionPoint();
            case "testPoints":
                return entity.getTestPoints();
            case "priority":
                return entity.getPriority();
            case "ruleType":
                return entity.getRuleType();
            case "involveAccount":
                return entity.getInvolveAccount();
            case "involveBatch":
                return entity.getInvolveBatch();
            case "ruleSource":
                return entity.getRuleSource();
            case "remark":
                return entity.getRemark();
            default:
                return null;
        }
    }

    /**
     * 转换为显示值
     */
    private Object convertToDisplayValue(Object value, JSONObject fieldConfig) {
        if (value == null) {
            return "";
        }

        String fieldType = fieldConfig.getString("field_type");

        if ("SELECT".equals(fieldType) || "MULTI-SELECT".equals(fieldType)) {
            JSONArray options = fieldConfig.getJSONArray("options");
            if (options != null) {
                for (int i = 0; i < options.size(); i++) {
                    JSONObject option = options.getJSONObject(i);
                    Object optionValue = option.get("value");
                    if (value.equals(optionValue)) {
                        return option.getString("text");
                    }
                }
            }
        }

        // 特殊处理布尔值
        if (value instanceof Boolean) {
            return (Boolean) value ? "是" : "否";
        }

        return value.toString();
    }

    /**
     * 设置字段值到VO
     */
    private void setFieldValueToVo(TestRequirementFunctionPointsExcelVo vo, String fieldKey, Object value) {
        if (value == null) {
            return;
        }

        String valueStr = value.toString();

        switch (fieldKey) {
            case "functionPoint":
                vo.setFunctionPoint(valueStr);
                break;
            case "testPoints":
                vo.setTestPoints(valueStr);
                break;
            case "priority":
                vo.setPriority(valueStr);
                break;
            case "ruleType":
                vo.setRuleType(valueStr);
                break;
            case "involveAccount":
                vo.setAccount(valueStr);
                break;
            case "involveBatch":
                vo.setBatch(valueStr);
                break;
            case "ruleSource":
                vo.setRuleSource(valueStr);
                break;
            case "remark":
                vo.setRemark(valueStr);
                break;
        }
    }

    /**
     * 校验和转换导入数据
     */
    private List<TestRequirementFunctionPoints> validateAndConvertImportData(
            List<TestRequirementFunctionPointsExcelVo> importList,
            JSONArray fieldConfigs,
            SystemAndTransactionData systemAndTransactionData,
            Long projectId,
            Long taskId) {

        // 构建系统和交易的映射
        Map<String, ProductModuleFunction> systemMap = systemAndTransactionData.getSystems().stream()
                .collect(Collectors.toMap(s -> "(" + s.getCode() + ")" + s.getName(), Function.identity()));

        Map<String, ProductModuleFunction> transactionMap = systemAndTransactionData.getTransactions().stream()
                .collect(Collectors.toMap(t -> "(" + t.getCode() + ")" + t.getName(), Function.identity()));

        List<TestRequirementFunctionPoints> result = new ArrayList<>();

        for (int i = 0; i < importList.size(); i++) {
            TestRequirementFunctionPointsExcelVo vo = importList.get(i);

            try {
                TestRequirementFunctionPoints entity = new TestRequirementFunctionPoints();

                // 设置基本信息
                entity.setProjectId(projectId);
                entity.setTaskId(taskId);

                // 校验和设置系统（从第一列获取）
                String systemValue = getSystemFromVo(vo);
                if (systemValue == null || !systemMap.containsKey(systemValue)) {
                    throw new BizException("第" + (i + 2) + "行：系统值无效 - " + systemValue);
                }
                // 这里需要根据实际的VO结构来设置系统ID，暂时跳过

                // 校验和设置交易（从第二列获取）
                String transactionValue = getTransactionFromVo(vo);
                if (transactionValue == null || !transactionMap.containsKey(transactionValue)) {
                    throw new BizException("第" + (i + 2) + "行：交易值无效 - " + transactionValue);
                }
                entity.setFunctionId(transactionMap.get(transactionValue).getId());

                // 根据fieldconfig校验和设置其他字段
                validateAndSetFieldsByConfig(entity, vo, fieldConfigs, i + 2);

                result.add(entity);

            } catch (Exception e) {
                throw new BizException("第" + (i + 2) + "行数据处理失败：" + e.getMessage());
            }
        }

        return result;
    }

    /**
     * 从VO中获取系统值（需要根据实际VO结构调整）
     */
    private String getSystemFromVo(TestRequirementFunctionPointsExcelVo vo) {
        // 这里需要根据实际的VO结构来获取系统值
        // 暂时返回null，需要根据实际情况调整
        return null;
    }

    /**
     * 从VO中获取交易值
     */
    private String getTransactionFromVo(TestRequirementFunctionPointsExcelVo vo) {
        return vo.getFunction();
    }

    /**
     * 根据fieldconfig校验和设置字段值
     */
    private void validateAndSetFieldsByConfig(TestRequirementFunctionPoints entity,
                                              TestRequirementFunctionPointsExcelVo vo,
                                              JSONArray fieldConfigs,
                                              int rowNumber) {
        for (int i = 0; i < fieldConfigs.size(); i++) {
            JSONObject fieldConfig = fieldConfigs.getJSONObject(i);
            String key = fieldConfig.getString("key");
            String displayName = fieldConfig.getString("display_name");
            boolean required = fieldConfig.getBooleanValue("required");
            String fieldType = fieldConfig.getString("field_type");

            // 从VO中获取字段值
            Object fieldValue = getFieldValueFromVo(vo, key);

            // 必填校验
            if (required && (fieldValue == null || fieldValue.toString().trim().isEmpty())) {
                throw new BizException("第" + rowNumber + "行：" + displayName + "为必填项");
            }

            // 根据字段类型进行校验和转换
            Object convertedValue = validateAndConvertFieldValue(fieldValue, fieldConfig, displayName, rowNumber);

            // 设置到实体中
            setFieldValueToEntity(entity, key, convertedValue);
        }
    }

    /**
     * 从VO中获取指定字段的值
     */
    private Object getFieldValueFromVo(TestRequirementFunctionPointsExcelVo vo, String fieldKey) {
        switch (fieldKey) {
            case "functionPoint":
                return vo.getFunctionPoint();
            case "testPoints":
                return vo.getTestPoints();
            case "priority":
                return vo.getPriority();
            case "ruleType":
                return vo.getRuleType();
            case "involveAccount":
                return vo.getAccount();
            case "involveBatch":
                return vo.getBatch();
            case "ruleSource":
                return vo.getRuleSource();
            case "remark":
                return vo.getRemark();
            default:
                return null;
        }
    }

    /**
     * 校验和转换字段值
     */
    private Object validateAndConvertFieldValue(Object value, JSONObject fieldConfig, String displayName, int rowNumber) {
        if (value == null) {
            return null;
        }

        String fieldType = fieldConfig.getString("field_type");
        String valueStr = value.toString().trim();

        switch (fieldType) {
            case "SELECT":
            case "MULTI-SELECT":
                return validateSelectValue(valueStr, fieldConfig, displayName, rowNumber);
            case "TEXT":
            case "TEXTAREA":
                return validateTextValue(valueStr, fieldConfig, displayName, rowNumber);
            default:
                return valueStr;
        }
    }

    /**
     * 校验选择类型字段
     */
    private Object validateSelectValue(String value, JSONObject fieldConfig, String displayName, int rowNumber) {
        JSONArray options = fieldConfig.getJSONArray("options");
        if (options != null) {
            for (int i = 0; i < options.size(); i++) {
                JSONObject option = options.getJSONObject(i);
                String text = option.getString("text");
                if (text.equals(value)) {
                    return option.get("value");
                }
            }
            throw new BizException("第" + rowNumber + "行：" + displayName + "的值无效 - " + value);
        }
        return value;
    }

    /**
     * 校验文本类型字段
     */
    private Object validateTextValue(String value, JSONObject fieldConfig, String displayName, int rowNumber) {
        Integer maxLength = fieldConfig.getInteger("max_length");
        if (maxLength != null && value.length() > maxLength) {
            throw new BizException("第" + rowNumber + "行：" + displayName + "长度不能超过" + maxLength + "个字符");
        }
        return value;
    }

    /**
     * 设置字段值到实体
     */
    private void setFieldValueToEntity(TestRequirementFunctionPoints entity, String fieldKey, Object value) {
        if (value == null) {
            return;
        }

        switch (fieldKey) {
            case "functionPoint":
                entity.setFunctionPoint(value.toString());
                break;
            case "testPoints":
                entity.setTestPoints(value.toString());
                break;
            case "priority":
                entity.setPriority(value.toString());
                break;
            case "ruleType":
                entity.setRuleType(value.toString());
                break;
            case "involveAccount":
                entity.setInvolveAccount("是".equals(value.toString()) || Boolean.TRUE.equals(value));
                break;
            case "involveBatch":
                entity.setInvolveBatch("是".equals(value.toString()) || Boolean.TRUE.equals(value));
                break;
            case "ruleSource":
                entity.setRuleSource(value.toString());
                break;
            case "remark":
                entity.setRemark(value.toString());
                break;
        }
    }

    /**
     * 系统和交易数据封装类
     */
    private static class SystemAndTransactionData {
        private final List<ProductModuleFunction> systems;
        private final List<ProductModuleFunction> transactions;

        public SystemAndTransactionData(List<ProductModuleFunction> systems, List<ProductModuleFunction> transactions) {
            this.systems = systems;
            this.transactions = transactions;
        }

        public List<ProductModuleFunction> getSystems() {
            return systems;
        }

        public List<ProductModuleFunction> getTransactions() {
            return transactions;
        }
    }

}
