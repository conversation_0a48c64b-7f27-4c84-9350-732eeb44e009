package com.jettech.jettong.testm.controller.testReq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TestRequirementFunctionPointsExcelController 单元测试
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试Excel操作控制器的核心功能
 * @date 2025-09-03
 */
@ExtendWith(MockitoExtension.class)
class TestRequirementFunctionPointsExcelControllerTest {

    @Test
    void testFieldConfigParsing() {
        // 测试fieldconfig配置解析
        String fieldConfigJson = """
            [
                {
                    "key": "functionPoint",
                    "display_name": "功能点",
                    "field_type": "TEXT",
                    "required": true,
                    "max_length": 100
                },
                {
                    "key": "priority",
                    "display_name": "优先级",
                    "field_type": "SELECT",
                    "required": true,
                    "options": [
                        {"text": "高", "value": "HIGH"},
                        {"text": "中", "value": "MEDIUM"},
                        {"text": "低", "value": "LOW"}
                    ]
                }
            ]
            """;
        
        JSONArray fieldConfigs = JSON.parseArray(fieldConfigJson);
        assertNotNull(fieldConfigs);
        assertEquals(2, fieldConfigs.size());
        
        JSONObject firstField = fieldConfigs.getJSONObject(0);
        assertEquals("functionPoint", firstField.getString("key"));
        assertEquals("功能点", firstField.getString("display_name"));
        assertEquals("TEXT", firstField.getString("field_type"));
        assertTrue(firstField.getBooleanValue("required"));
        
        JSONObject secondField = fieldConfigs.getJSONObject(1);
        assertEquals("priority", secondField.getString("key"));
        assertEquals("优先级", secondField.getString("display_name"));
        assertEquals("SELECT", secondField.getString("field_type"));
        
        JSONArray options = secondField.getJSONArray("options");
        assertNotNull(options);
        assertEquals(3, options.size());
    }

    @Test
    void testSystemAndTransactionDataCreation() {
        // 测试系统和交易数据封装
        ProductModuleFunction system1 = new ProductModuleFunction();
        system1.setId(1L);
        system1.setCode("SYS001");
        system1.setName("系统1");
        system1.setNodeType(1); // 系统
        
        ProductModuleFunction transaction1 = new ProductModuleFunction();
        transaction1.setId(2L);
        transaction1.setCode("TXN001");
        transaction1.setName("交易1");
        transaction1.setNodeType(2); // 交易
        
        List<ProductModuleFunction> systems = Arrays.asList(system1);
        List<ProductModuleFunction> transactions = Arrays.asList(transaction1);
        
        // 这里我们无法直接测试私有内部类，但可以验证数据结构
        assertNotNull(systems);
        assertNotNull(transactions);
        assertEquals(1, systems.size());
        assertEquals(1, transactions.size());
        
        assertEquals("SYS001", systems.get(0).getCode());
        assertEquals("TXN001", transactions.get(0).getCode());
    }

    @Test
    void testFieldValidationLogic() {
        // 测试字段验证逻辑
        JSONObject fieldConfig = new JSONObject();
        fieldConfig.put("key", "priority");
        fieldConfig.put("display_name", "优先级");
        fieldConfig.put("field_type", "SELECT");
        fieldConfig.put("required", true);
        
        JSONArray options = new JSONArray();
        JSONObject option1 = new JSONObject();
        option1.put("text", "高");
        option1.put("value", "HIGH");
        options.add(option1);
        
        JSONObject option2 = new JSONObject();
        option2.put("text", "中");
        option2.put("value", "MEDIUM");
        options.add(option2);
        
        fieldConfig.put("options", options);
        
        // 验证配置结构
        assertEquals("SELECT", fieldConfig.getString("field_type"));
        assertTrue(fieldConfig.getBooleanValue("required"));
        assertEquals(2, fieldConfig.getJSONArray("options").size());
    }

    @Test
    void testConfigurationStructure() {
        // 测试配置结构
        TestRequirementAnalysisConfig config = new TestRequirementAnalysisConfig();
        config.setEnable(true);
        config.setFieldConfig("""
            [
                {
                    "key": "testField",
                    "display_name": "测试字段",
                    "field_type": "TEXT",
                    "required": false
                }
            ]
            """);

        assertTrue(config.getEnable());
        assertNotNull(config.getFieldConfig());

        JSONArray parsedConfig = JSON.parseArray(config.getFieldConfig());
        assertEquals(1, parsedConfig.size());
    }

    @Test
    void testTreeDataStructure() {
        // 测试树形数据结构处理
        // 模拟API返回的树形数据
        ProductModuleFunction system = new ProductModuleFunction();
        system.setId(1L);
        system.setName("新建类项目系统");
        system.setNodeType(0); // 系统

        ProductModuleFunction module = new ProductModuleFunction();
        module.setId(2L);
        module.setName("模块一");
        module.setNodeType(1); // 模块
        module.setParentId(1L);

        ProductModuleFunction transaction1 = new ProductModuleFunction();
        transaction1.setId(3L);
        transaction1.setName("交易一");
        transaction1.setNodeType(2); // 交易
        transaction1.setParentId(2L);

        ProductModuleFunction transaction2 = new ProductModuleFunction();
        transaction2.setId(4L);
        transaction2.setName("交易二");
        transaction2.setNodeType(2); // 交易
        transaction2.setParentId(2L);

        // 构建树形结构
        module.setChildren(Arrays.asList(transaction1, transaction2));
        system.setChildren(Arrays.asList(module));

        List<ProductModuleFunction> treeData = Arrays.asList(system);

        // 验证树形结构
        assertNotNull(treeData);
        assertEquals(1, treeData.size());
        assertEquals(0, treeData.get(0).getNodeType()); // 系统
        assertEquals(1, treeData.get(0).getChildren().size()); // 一个模块
        assertEquals(2, treeData.get(0).getChildren().get(0).getChildren().size()); // 两个交易

        // 验证交易节点
        List<ProductModuleFunction> transactions = treeData.get(0).getChildren().get(0).getChildren();
        assertTrue(transactions.stream().allMatch(t -> t.getNodeType() == 2));
        assertEquals("交易一", transactions.get(0).getName());
        assertEquals("交易二", transactions.get(1).getName());
    }

    @Test
    void testCascadingSelectionMapping() {
        // 测试级联选择映射关系
        ProductModuleFunction system1 = new ProductModuleFunction();
        system1.setId(1L);
        system1.setName("系统A");
        system1.setCode("SYS_A");
        system1.setNodeType(0);

        ProductModuleFunction module1 = new ProductModuleFunction();
        module1.setId(2L);
        module1.setName("模块A1");
        module1.setNodeType(1);
        module1.setParentId(1L);

        ProductModuleFunction transaction1 = new ProductModuleFunction();
        transaction1.setId(3L);
        transaction1.setName("交易A1");
        transaction1.setCode("TXN_A1");
        transaction1.setNodeType(2);
        transaction1.setParentId(2L);

        ProductModuleFunction transaction2 = new ProductModuleFunction();
        transaction2.setId(4L);
        transaction2.setName("交易A2");
        transaction2.setCode("TXN_A2");
        transaction2.setNodeType(2);
        transaction2.setParentId(2L);

        // 构建树形结构
        module1.setChildren(Arrays.asList(transaction1, transaction2));
        system1.setChildren(Arrays.asList(module1));

        // 验证级联关系
        List<ProductModuleFunction> systems = Arrays.asList(system1);
        List<ProductModuleFunction> allTransactions = Arrays.asList(transaction1, transaction2);

        // 验证系统包含的交易
        assertEquals(2, system1.getChildren().get(0).getChildren().size());

        // 验证交易属于正确的系统
        List<ProductModuleFunction> systemTransactions = system1.getChildren().get(0).getChildren();
        assertTrue(systemTransactions.contains(transaction1));
        assertTrue(systemTransactions.contains(transaction2));

        // 验证级联选择的键值格式
        String systemKey = "(" + system1.getCode() + ")" + system1.getName();
        assertEquals("(SYS_A)系统A", systemKey);

        String transactionKey1 = "(" + transaction1.getCode() + ")" + transaction1.getName();
        assertEquals("(TXN_A1)交易A1", transactionKey1);
    }

    @Test
    void testValidRangeNameCreation() {
        // 测试Excel命名范围名称的创建
        String systemName1 = "(SYS_A)系统A";
        String systemName2 = "(SYS-B)系统B!@#";
        String systemName3 = "123系统C";

        // 模拟createValidRangeName方法的逻辑
        String rangeName1 = systemName1.replaceAll("[^a-zA-Z0-9\u4e00-\u9fa5]", "_");
        String rangeName2 = systemName2.replaceAll("[^a-zA-Z0-9\u4e00-\u9fa5]", "_");
        String rangeName3 = systemName3.replaceAll("[^a-zA-Z0-9\u4e00-\u9fa5]", "_");

        // 确保以字母开头
        if (!rangeName3.matches("^[a-zA-Z\u4e00-\u9fa5].*")) {
            rangeName3 = "System_" + rangeName3;
        }

        assertEquals("_SYS_A_系统A", rangeName1);
        assertEquals("_SYS_B_系统B___", rangeName2);
        assertEquals("System_123系统C", rangeName3);

        // 验证名称有效性
        assertTrue(rangeName1.length() <= 255);
        assertTrue(rangeName2.length() <= 255);
        assertTrue(rangeName3.length() <= 255);
    }
}
