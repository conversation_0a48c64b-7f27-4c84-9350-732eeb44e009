<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-testm</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>jettong-testm-entity</artifactId>
    <name>${project.artifactId}</name>
    <description>测试管理-实体模块</description>

    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-base-entity</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-common</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20171018</version>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.2.0</version>
        </dependency>
    </dependencies>

</project>
