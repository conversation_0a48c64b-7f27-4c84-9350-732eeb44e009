package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 测试计划关联用例树表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划关联用例树表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanCaseTreeUpdateDTO
 * @date 2022-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseTreeUpdateDTO", description = "测试计划关联用例树表")
public class TestPlanCaseTreeUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 测试计划Id
     */
    @ApiModelProperty(value = "测试计划Id")
    @NotNull(message = "请填写测试计划Id")
    private Long planId;
    /**
     * 树id
     */
    @ApiModelProperty(value = "树id")
    @NotNull(message = "请填写树id")
    private Long treeId;
    /**
     * 测试用例id
     */
    @ApiModelProperty(value = "测试用例id")
    private Long caseId;
    /**
     * 维护人
     */
    @ApiModelProperty(value = "维护人")
    private Long managingBy;
}
