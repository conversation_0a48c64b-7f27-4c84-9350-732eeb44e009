package com.jettech.jettong.testm.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作台需求概览组件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台用例概览组件
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.issue.vo
 * @className RequirementTypeComponentResult
 * @date 2021/12/2 20:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestReportBugComponent", description = "工作台用例概览组件")
public class TestReportBugComponent implements Serializable
{
    private static final long serialVersionUID = 1L;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityCode;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private Long num = 0L;

    /**
     * 占比 %
     */
    @ApiModelProperty(value = "占比")
    private Integer rate = 0;

}
