package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试案例树表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例树表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestCaseTree
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_case_tree")
@ApiModel(value = "TestCaseTree", description = "测试案例树表")
@AllArgsConstructor
public class TestCaseTree extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 案例库ID
     */
    @ApiModelProperty(value = "案例库ID")
    @NotNull(message = "请填写案例库ID")
    @TableField(value = "`library_id`")
    @Excel(name = "案例库ID")
    private Long libraryId;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @TableField(value = "`parent_id`")
    @Excel(name = "父ID")
    private Long parentId;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @TableField(value = "`level`")
    @Excel(name = "等级")
    private Integer level;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    protected Integer sort;


    @Builder
    public TestCaseTree(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String name, Long libraryId, Long parentId, Integer level, Long leadingBy)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.libraryId = libraryId;
        this.parentId = parentId;
        this.level = level;
        this.leadingBy = leadingBy;
    }

}
