package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 测试标签与用例关联 修改实体类
 *
 * <AUTHOR>
 * @version 1.2
 * @description 测试标签与用例关联 修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTabCaseUpdateDTO
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTabCaseUpdateDTO", description = "测试标签与用例关联表")
public class TestTabCaseUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 测试案例ID
     */
    @ApiModelProperty(value = "测试案例ID")
    private Long caseId;

    /**
     * 测试标签ID
     */
    @ApiModelProperty(value = "测试标签ID")
    private Long tabId;
}
