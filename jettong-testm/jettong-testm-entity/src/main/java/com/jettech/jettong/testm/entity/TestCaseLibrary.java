package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试案例库表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例库表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestCaseLibrary
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_case_library")
@ApiModel(value = "TestCaseLibrary", description = "测试案例库表")
@AllArgsConstructor
public class TestCaseLibrary extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "`product_id`")
    @Excel(name = "关联产品")
    private Long productId;

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    @TableField(value = "`project_id`")
    @Excel(name = "关联项目")
    private Long projectId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 案例库类型:product/project
     */
    @ApiModelProperty(value = "案例库类型:product/project")
    @Size(max = 20, message = "案例库类型:product/project长度不能超过20")
    @TableField(value = "`type`", condition = LIKE)
    @Excel(name = "案例库类型:product/project")
    private String type;


    @Builder
    public TestCaseLibrary(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String name, Long productId, Long projectId, Long leadingBy, String type)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.productId = productId;
        this.projectId = projectId;
        this.leadingBy = leadingBy;
        this.type = type;
    }

}
