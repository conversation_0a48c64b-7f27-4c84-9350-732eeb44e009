package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className ProductCaseTreeSaveDTO
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductCaseTreeSaveDTO", description = "")
public class ProductCaseTreeSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 用例库ID
     */
    @ApiModelProperty(value = "用例库ID")
    @NotNull(message = "请填写用例库ID")
    private Long libraryId;
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private Integer level;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

}
