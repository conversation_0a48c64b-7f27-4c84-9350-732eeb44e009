package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/09/20 14:53
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "CaseAndTreeReturnVo", description = "测试计划关联用例或创建用例前的树级信息列表与用例返回信息")
public class CaseAndTreeReturnVo
{

    private String treeId;

    private String treeName;

    private String parentId;

    private List<String> caseIds;

}
