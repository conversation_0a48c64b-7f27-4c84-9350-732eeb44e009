package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className ProductCaseLibrary
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_product_case_library")
@ApiModel(value = "ProductCaseLibrary", description = "")
@AllArgsConstructor
public class ProductCaseLibrary extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "product_id")
    @Excel(name = "关联产品")
    private Long productId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by",updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 描述
     */
    @ApiModelProperty(value = "类型")
    @Size(max = 20, message = "类型长度不能超过20")
    @TableField(value = "`type`")
    @Excel(name = "类型")
    private String type;
    /**
     * 描述
     */
    @ApiModelProperty(value = "树深度")
    @TableField(value = "`depth`")
    @Size(max = 5, message = "树深度长度不能超过5")
    @Excel(name = "树深度")
    private String depth;


    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    @TableField(value = "`project_id`")
    @Excel(name = "关联项目")
    private Long projectId;

    @ApiModelProperty(value = "英文标识")
    @Size(max = 20, message = "英文标识长度不能超过20")
    @TableField(value = "case_key", condition = LIKE)
    @Excel(name = "英文标识")
    private String caseKey;


    @Builder
    public ProductCaseLibrary(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    String name, Long productId, Long leadingBy ,String description,String type,String depth,Long projectId ,String caseKey)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.productId = productId;
        this.leadingBy = leadingBy;
        this.description = description;
        this.type = type;
        this.depth = depth;
        this.projectId = projectId;
        this.caseKey = caseKey;
    }

}
