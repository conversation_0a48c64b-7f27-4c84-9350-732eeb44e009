package com.jettech.jettong.testm.dto;

import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 测试任务用例执行表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例执行表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTaskCaseResult
 * @date 2025-08-13
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseResultPageQuery", description = "测试任务用例执行表")
public class TestTaskCaseResultPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    private Long taskId;
    /**
     * 用例历史id
     */
    @ApiModelProperty(value = "用例历史id")
    private Long historyId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    private Long testcaseId;
    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    private String name;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    private String status;
    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    private String result;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime execTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
    /**
     * 操作状态：create创建/update更新/execute执行/delete删除
     */
    @ApiModelProperty(value = "操作状态：create创建/update更新/execute执行/delete删除")
    private String operateType;
    /**
     * 用例实际执行时间
     */
    @ApiModelProperty(value = "用例实际执行时间")
    private Long truexecTime;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

}
