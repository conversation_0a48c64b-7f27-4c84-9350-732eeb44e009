package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 测试任务用例执行表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例执行表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTaskCaseResultSaveDTO
 * @date 2025-08-13
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseResultSaveDTO", description = "测试任务用例执行表")
public class TestTaskCaseResultSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "请填写id")
    private Long id;
    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    private Long taskId;
    /**
     * 用例历史id
     */
    @ApiModelProperty(value = "用例历史id")
    private Long historyId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private Long testcaseId;
    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称")
    @Size(max = 128, message = "计划名称长度不能超过128")
    private String name;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    private String status;
    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    @Size(max = 65535, message = "执行结果详情长度不能超过65,535")
    private String result;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime execTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
    /**
     * 操作状态：create创建/update更新/execute执行/delete删除
     */
    @ApiModelProperty(value = "操作状态：create创建/update更新/execute执行/delete删除")
    @Size(max = 255, message = "操作状态：create创建/update更新/execute执行/delete删除长度不能超过255")
    private String operateType;
    /**
     * 用例实际执行时间
     */
    @ApiModelProperty(value = "用例实际执行时间")
    private Long truexecTime;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Size(max = 255, message = "版本长度不能超过255")
    private String version;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;

}
