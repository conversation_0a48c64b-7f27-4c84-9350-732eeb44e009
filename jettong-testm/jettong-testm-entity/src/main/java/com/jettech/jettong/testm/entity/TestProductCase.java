package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.*;


/**
 * 测试管理中 测试用例 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试管理中 测试用例 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestProductCase
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_product_case")
@ApiModel(value = "TestProductCase", description = "")
@AllArgsConstructor
public class TestProductCase extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @NotEmpty(message = "请填写英文标识")
    @Size(max = 20, message = "英文标识长度不能超过20")
    @TableField(value = "case_key", condition = LIKE)
    @Excel(name = "英文标识")
    private String caseKey;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @TableField(value = "intent", condition = LIKE)
    @Excel(name = "测试意图")
    private String intent;

    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "step_type", condition = LIKE)
    @Excel(name = "测试步骤类型，文本/条目")
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65535")
    @TableField(value = "test_step", condition = LIKE)
    @Excel(name = "测试步骤")
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65535")
    @TableField(value = "expected_result", condition = LIKE)
    @Excel(name = "预期结果")
    private String expectedResult;

    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @TableField(value = "state_id")
    @Excel(name = "评审状态")
    private Long stateId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "product_id")
    @Excel(name = "关联产品")
    private Long productId;

    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    @TableField(value = "requirement_id")
    @Excel(name = "关联需求")
    private String requirementId;

    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    @TableField(value = "tree_id",updateStrategy= FieldStrategy.IGNORED)
    @Excel(name = "关联案例树")
    private Long treeId;

    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    @TableField(value = "priority")
    @Excel(name = "用例等级")
    private Integer priority;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by",updateStrategy = FieldStrategy.IGNORED)
    @Excel(name = "负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65535")
    @TableField(value = "prerequisite", condition = LIKE)
    @Excel(name = "前提条件")
    private String prerequisite;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @TableField(value = "library_id")
    @Excel(name = "用例库")
    private Long libraryId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "version")
    @Excel(name = "版本")
    private String version;
    /**
     * 标签实体类集合
     */
    @ApiModelProperty(value = "标签实体类集合")
    @TableField(exist = false)
    private List<TestTab> tabs;
    /**
     * 关联用例时，当前测试计划是否已关联
     */
    @ApiModelProperty(value = "是否已关联")
    @TableField(exist = false)
    private Boolean isRelated;

    /**
     * 测试用例上传的附件文件
     */
    @ApiModelProperty(value = "关联附件文件")
    @TableField(exist = false)
    private List<File> files;

    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    @TableField(value = "`state`")
    @Excel(name = "回收站状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    /**
     * 草稿
     */
    @ApiModelProperty(value = "草稿")
    @TableField(value = "`draft`")
    @Excel(name = "草稿", replace = {"是_true", "否_false", "_null"})
    private Boolean draft;

    /**
     * 意图附件文件
     */
    @ApiModelProperty(value = "意图附件文件")
    @TableField(exist = false)
    private List<File> intentFiles;

    /**
     * 前置条件附件文件
     */
    @ApiModelProperty(value = "前置条件附件文件")
    @TableField(exist = false)
    private List<File> prerequisiteFiles;

    /**
     * 测试步骤附件文件
     */
    @ApiModelProperty(value = "测试步骤附件文件")
    @TableField(exist = false)
    private List<File> testStepFiles;

    /**
     * 执行用例附件文件
     */
    @ApiModelProperty(value = "执行用例附件文件")
    @TableField(exist = false)
    private List<File> executePlanCaseFiles;

    @ApiModelProperty(value = "预估执行时间")
    @TableField(value = "exec_time")
    @Excel(name = "预估执行时间")
    private Long execTime;

    @ApiModelProperty(value = "执行人Id")
    @TableField(exist = false)
    @Excel(name = "执行人Id")
    private Long execBy;

    @ApiModelProperty(value = "标签Id")
    @TableField(exist = false)
    private String[] TabInfo;

    /**
     *测试覆盖情况
     */
    @ApiModelProperty(value = "测试覆盖情况")
    @TableField(value = "test_coverage")
    @Excel(name = "测试覆盖情况")
    private String testCoverage;
    /**
     *描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "case_describe")
    @Excel(name = "描述")
    private String caseDescribe;
    /**
     *数据需求
     */
    @ApiModelProperty(value = "数据需求")
    @TableField(value = "data_requirements")
    @Excel(name = "数据需求")
    private String dataRequirements;
    /**
     *测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(exist = false)
    @Excel(name = "测试方式")
    private List<Long> testMode = new ArrayList<>();

    /**
     *检查点
     */
    @ApiModelProperty(value = "检查点")
    @TableField(value = "checkpoints")
    @Excel(name = "检查点")
    private String checkpoints;
    /**
     *运行条件
     */
    @ApiModelProperty(value = "运行条件")
    @TableField(value = "run_condition")
    @Excel(name = "运行条件")
    private String runCondition;
    /**
     *评审级别
     */
    @ApiModelProperty(value = "评审级别")
    @TableField(value = "review_status")
    @Excel(name = "评审级别")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.REVIEW_STATUS)
    private String reviewStatus;
    /**
     *案例级别
     */
    @ApiModelProperty(value = "案例级别")
    @TableField(value = "case_level")
    @Excel(name = "案例级别")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_LEVEL)
    private String caseLevel;
    /**
     *用例类型
     */
    @ApiModelProperty(value = "用例类型")
    @TableField(value = "case_type")
    @Excel(name = "用例类型")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_TYPE)
    private String caseType;

    /**
     * 正反例
     */
    @ApiModelProperty(value = "正反例")
    @TableField(value = "is_examples")
    private Boolean isExamples;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    @TableField(value = "module_function_id")
    private Long moduleFunctionId;

    /**
     * 关联交易名称
     */
    @ApiModelProperty(value = "关联交易名称")
    @TableField(exist = false)
    private String moduleFunctionName;

    /**
     * 关联任务
     */
    @ApiModelProperty(value = "关联任务")
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    @TableField(value = "function_points_id")
    @Echo(api = REQUIREMENT_FUNCTION_POINTS_CLASS, beanClass = TestRequirementFunctionPoints.class)
    private Long functionPointsId;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    @TableField(exist = false)
    private String projectCode;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "testreq_id")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "project_id")
    private Long projectId;

    /**
     * 是否被引用
     */
    @ApiModelProperty(value = "是否被引用")
    @TableField(exist = false)
    private Boolean isCited;

    @Builder
    public TestProductCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    String caseKey, String name, String intent, String stepType, String testStep, 
                    String expectedResult, Long stateId, Long productId, String requirementId, Long treeId, Integer priority,
                    Long leadingBy, String prerequisite, Long libraryId,String version, List<File> files, Boolean state ,Boolean draft ,
            List<File> intentFiles , List<File> prerequisiteFiles ,List<File> testStepFiles ,List<File>  executePlanCaseFiles,Long execTime,
            String caseType, Boolean isExamples,String caseLevel, String reviewStatus, String runCondition, String checkpoints, String testMode, String dataRequirements, String caseDescribe, String testCoverage,
                           Long moduleFunctionId,Long taskId,Long functionPointsId,Long testreqId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.caseKey = caseKey;
        this.name = name;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.stateId = stateId;
        this.productId = productId;
        this.requirementId = requirementId;
        this.treeId = treeId;
        this.priority = priority;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.libraryId = libraryId;
        this.version = version;
        this.files = files;
        this.state = state;
        this.draft = draft;
        this.intentFiles = intentFiles;
        this.prerequisiteFiles = prerequisiteFiles;
        this.testStepFiles = testStepFiles;
        this.executePlanCaseFiles = executePlanCaseFiles;
        this.execTime = execTime;
        this.caseType = caseType;
        this.isExamples = isExamples;
        this.caseLevel = caseLevel;
        this.reviewStatus = reviewStatus;
        this.runCondition = runCondition;
        this.checkpoints = checkpoints;
        this.dataRequirements = dataRequirements;
        this.caseDescribe = caseDescribe;
        this.testCoverage = testCoverage;
        this.moduleFunctionId = moduleFunctionId;
        this.taskId = taskId;
        this.functionPointsId = functionPointsId;
        this.testreqId = testreqId;
    }

}
