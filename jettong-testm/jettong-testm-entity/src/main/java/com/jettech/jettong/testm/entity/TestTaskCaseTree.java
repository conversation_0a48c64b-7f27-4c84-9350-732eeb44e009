package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.jettech.basic.base.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import com.jettech.basic.model.EchoVO;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试任务关联用例树表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务关联用例树表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTaskCaseTree
 * @date 2025-08-13
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_task_case_tree")
@ApiModel(value = "TestTaskCaseTree", description = "测试任务关联用例树表")
@AllArgsConstructor
public class TestTaskCaseTree extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 测试任务Id
     */
    @ApiModelProperty(value = "测试任务Id")
    @NotNull(message = "请填写测试任务Id")
    @TableField(value = "`task_id`")
    @Excel(name = "测试任务Id")
    private Long taskId;

    /**
     * 树id
     */
    @ApiModelProperty(value = "树id")
    @NotNull(message = "请填写树id")
    @TableField(value = "`tree_id`")
    @Excel(name = "树id")
    private Long treeId;

    /**
     * 树级名称
     */
    @ApiModelProperty(value = "树名称")
    @NotNull(message = "请填写树名称")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "树名称")
    private String name;

    /**
     * 树父级Id
     */
    @ApiModelProperty(value = "父级树Id")
    @NotNull(message = "请填写父级树id")
    @TableField(value = "`parent_id`")
    @Excel(name = "父级树id")
    private Long parentId;

    /**
     * 测试用例id
     */
    @ApiModelProperty(value = "测试用例id")
    @TableField(value = "`case_id`")
    @Excel(name = "测试用例id")
    private Long caseId;


    /**
     * 维护人
     */
    @ApiModelProperty(value = "维护人")
    @TableField(value = "`managing_by`")
    @Excel(name = "维护人")
    private Long managingBy;


    @Builder
    public TestTaskCaseTree(
                    Long id, Long taskId, Long treeId, String name, Long parentId, 
                    Long caseId, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, Long managingBy)
    {
        this.id = id;
        this.taskId = taskId;
        this.treeId = treeId;
        this.name = name;
        this.parentId = parentId;
        this.caseId = caseId;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.managingBy = managingBy;
    }

}
