package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 测试用例归档表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试用例归档表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestProductCaseArchiveUpdateDTO
 * @date 2022-05-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCaseArchiveUpdateDTO", description = "测试用例归档表")
public class TestProductCaseArchiveUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @NotEmpty(message = "请填写英文标识")
    @Size(max = 20, message = "英文标识长度不能超过20")
    private String caseKey;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    private String intent;
    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    private String stepType;
    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    private String testStep;
    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65,535")
    private String expectedResult;
    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    private Long stateId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private Long requirementId;
    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    private Long treeId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65,535")
    private String prerequisite;
    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    private Long libraryId;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Size(max = 20, message = "版本长度不能超过20")
    private String version;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;


    /**
     * 草稿
     */
    @ApiModelProperty(value = "草稿")
    private Boolean draft;

    @ApiModelProperty(value = "树级归档id")
    private Long treeTypeId;
}
