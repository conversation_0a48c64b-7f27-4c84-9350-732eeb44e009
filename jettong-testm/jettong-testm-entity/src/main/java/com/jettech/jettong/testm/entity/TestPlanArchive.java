package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 测试计划归档表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划归档表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanArchive
 * @date 2022-05-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_archive")
@ApiModel(value = "TestPlanArchive", description = "测试计划归档表")
@AllArgsConstructor
public class TestPlanArchive extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @TableField(value = "parent_id")
    @Excel(name = "父ID")
    private Long parentId;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Size(max = 64, message = "标识长度不能超过64")
    @TableField(value = "plan_key", condition = LIKE)
    @Excel(name = "标识")
    private String planKey;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")

    @TableField(value = "description", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "state_id")
    @Excel(name = "状态")
    private Long stateId;

    /**
     * 计划类型:system系统测试/smoke冒烟测试/regression回归测试
     */
    @ApiModelProperty(value = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @NotEmpty(message = "请填写计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @Size(max = 20, message = "计划类型:system系统测试/smoke冒烟测试/regression回归测试长度不能超过20")
    @TableField(value = "type", condition = LIKE)
    @Excel(name = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    private String type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "project_id")
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "plan_stime")
    @Excel(name = "计划开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "plan_etime")
    @Excel(name = "计划完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "start_time")
    @Excel(name = "开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "end_time")
    @Excel(name = "完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime endTime;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @NotNull(message = "请填写是否延期")
    @TableField(value = "delay")
    @Excel(name = "是否延期", replace = {"是_true", "否_false", "_null"})
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @NotNull(message = "请填写进度0-100")
    @TableField(value = "rate_progress")
    @Excel(name = "进度0-100")
    private Long rateProgress;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 环境Id
     */
    @ApiModelProperty(value = "环境Id")
    @Size(max = 30, message = "环境Id长度不能超过30")
    @TableField(value = "env_id", condition = LIKE)
    @Excel(name = "环境Id")
    private String envId;

    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    @Size(max = 30, message = "产品Id长度不能超过30")
    @TableField(value = "product_id", condition = LIKE)
    @Excel(name = "产品Id")
    private String productId;

    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    @TableField(value = "product_version")
    @Excel(name = "关联产品版本")
    private Long productVersion;

    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    @TableField(value = "program_id")
    @Excel(name = "关联项目集")
    private Long programId;

    /**
     * 迭代计划Id
     */
    @ApiModelProperty(value = "迭代计划Id")
    @Size(max = 30, message = "迭代计划Id长度不能超过30")
    @TableField(value = "plan_id", condition = LIKE)
    @Excel(name = "迭代计划Id")
    private String planId;

    /**
     * 备用查询Id字段
     */
    @ApiModelProperty(value = "备用查询Id字段")
    @Size(max = 500, message = "备用查询Id字段长度不能超过500")
    @TableField(value = "query_id", condition = LIKE)
    @Excel(name = "备用查询Id字段")
    private String queryId;

    /**
     * 测试计划树id
     */
    @ApiModelProperty(value = "测试计划树id")
    @TableField(value = "tree_id")
    @Excel(name = "测试计划树id")
    private Long treeId;

    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @TableField(value = "library_id")
    @Excel(name = "产品用例库id")
    private Long libraryId;

    /**
     * 1-未开始;2-执行中;3-已完成
     */
    @ApiModelProperty(value = "1-未开始;2-执行中;3-已完成")
    @TableField(value = "exec_state")
    @Excel(name = "1-未开始;2-执行中;3-已完成")
    private Integer execState;

    /**
     * 1-必须创建缺陷;0-非必须
     */
    @ApiModelProperty(value = "1-必须创建缺陷;0-非必须")
    @TableField(value = "bug")
    @Excel(name = "1-必须创建缺陷;0-非必须", replace = {"是_true", "否_false", "_null"})
    private Boolean bug;


    @Builder
    public TestPlanArchive(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    Long parentId, String planKey, String name, String description, Long stateId, 
                    String type, Long projectId, LocalDateTime planStime, LocalDateTime planEtime, LocalDateTime startTime, LocalDateTime endTime, 
                    Boolean delay, Long rateProgress, Long leadingBy, String envId, String productId, Long productVersion, 
                    Long programId, String planId, String queryId, Long treeId, Long libraryId, Integer execState, Boolean bug)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.planKey = planKey;
        this.name = name;
        this.description = description;
        this.stateId = stateId;
        this.type = type;
        this.projectId = projectId;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.leadingBy = leadingBy;
        this.envId = envId;
        this.productId = productId;
        this.productVersion = productVersion;
        this.programId = programId;
        this.planId = planId;
        this.queryId = queryId;
        this.treeId = treeId;
        this.libraryId = libraryId;
        this.execState = execState;
        this.bug = bug;
    }

}
