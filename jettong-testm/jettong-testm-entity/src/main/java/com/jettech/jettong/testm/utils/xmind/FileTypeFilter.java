package com.jettech.jettong.testm.utils.xmind;

import java.io.File;
import java.io.FilenameFilter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 过来文件类型
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.utils
 * @className FileFilter
 * @date 2022/3/18 0018 10:08
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class FileTypeFilter implements FilenameFilter {
    @Override
    public boolean accept(File dir, String name) {
        if (name.endsWith(".json")) {
            return true;
        }
        return false;
    }
}
