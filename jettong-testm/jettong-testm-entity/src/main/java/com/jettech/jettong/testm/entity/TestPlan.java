package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 测试计划实例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划实例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlan
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan")
@ApiModel(value = "TestPlan", description = "测试计划实例表")
@AllArgsConstructor
public class TestPlan extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @TableField(value = "`parent_id`")
    @Excel(name = "父ID")
    private Long parentId;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @NotEmpty(message = "请填写标识")
    @Size(max = 64, message = "标识长度不能超过64")
    @TableField(value = "`plan_key`", condition = LIKE)
    @Excel(name = "标识")
    private String planKey;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "`state_id`")
    @Excel(name = "状态")
    private Long stateId;

    /**
     * 计划类型:system系统测试/smoke冒烟测试/regression回归测试
     */
    @ApiModelProperty(value = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @NotEmpty(message = "请填写计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @Size(max = 20, message = "计划类型:system系统测试/smoke冒烟测试/regression回归测试长度不能超过20")
    @TableField(value = "`type`", condition = LIKE)
    @Excel(name = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    private String type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "`project_id`")
    @Excel(name = "项目ID")
    private Long projectId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_stime`")
    @Excel(name = "计划开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "`plan_etime`")
    @Excel(name = "计划完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "`start_time`")
    @Excel(name = "开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "`end_time`")
    @Excel(name = "完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime endTime;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @NotNull(message = "请填写是否延期")
    @TableField(value = "`delay`")
    @Excel(name = "是否延期", replace = {"是_true", "否_false", "_null"})
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @NotNull(message = "请填写进度0-100")
    @TableField(value = "`rate_progress`")
    @Excel(name = "进度0-100")
    private Long rateProgress;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 环境
     */
    @ApiModelProperty(value = "环境")
    @TableField(value = "`env_id`")
    @Excel(name = "环境")
    private String envId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "`product_id`")
    @Excel(name = "关联产品")
    private String productId;

    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    @TableField(value = "`product_version`")
    @Excel(name = "关联产品版本")
    private Long productVersion;

    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    @TableField(value = "`program_id`")
    @Excel(name = "关联项目集")
    private Long programId;

    /**
     * 迭代计划Id
     */
    @ApiModelProperty(value = "迭代计划Id")
    @TableField(value = "`plan_id`")
    @Excel(name = "迭代计划Id")
    private String planId;

    /**
     * 备用查询Id字段
     */
    @ApiModelProperty(value = "备用查询Id字段")
    @TableField(value = "`query_id`")
    @Excel(name = "备用查询Id字段")
    private String queryId;

    /**
     * 关联测试计划分组树id
     */
    @ApiModelProperty(value = "关联测试计划分组树id")
    @TableField(value = "`tree_id`")
    @Excel(name = "关联测试计划分组树id")
    private Long treeId;

    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @TableField(value = "`library_id`")
    @Excel(name = "产品用例库id")
    private Long libraryId;

    /**
     * 执行状态
     */
    @ApiModelProperty(value = "执行状态")
    @TableField(value = "`exec_state`")
    @Excel(name = "执行状态")
    private Integer execState;

    /**
     * 是否必须创建缺陷
     */
    @ApiModelProperty(value = "缺陷")
    @TableField(value = "`bug`")
    @Excel(name = "状态")
    private Boolean bug;


    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    @TableField(value = "`state`")
    @Excel(name = "回收站状态", replace = {"是_true", "否_false", "_null"})
    private Boolean state;


    @Builder
    public TestPlan(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            Long parentId, String planKey, String name, String description, Long stateId,
            String type, Long projectId, LocalDateTime planStime, LocalDateTime planEtime, LocalDateTime startTime,
            LocalDateTime endTime,
            Boolean delay, Long rateProgress, Long leadingBy, String envId, String productId, Long productVersion,
            Long programId, String planId, String queryId ,Long libraryId, Integer execState ,boolean bug ,Boolean state)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.planKey = planKey;
        this.name = name;
        this.description = description;
        this.stateId = stateId;
        this.type = type;
        this.projectId = projectId;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.leadingBy = leadingBy;
        this.envId = envId;
        this.productId = productId;
        this.productVersion = productVersion;
        this.programId = programId;
        this.planId = planId;
        this.queryId = queryId;
        this.libraryId = libraryId;
        this.execState = execState;
        this.bug = bug;
        this.state = state;
    }

}
