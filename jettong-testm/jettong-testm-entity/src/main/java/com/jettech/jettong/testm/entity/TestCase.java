package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试案例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestCase
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_case")
@ApiModel(value = "TestCase", description = "测试案例表")
@AllArgsConstructor
public class TestCase extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @Size(max = 20, message = "英文标识长度不能超过20")
    @TableField(value = "`case_key`", condition = LIKE)
    @Excel(name = "英文标识")
    private String caseKey;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @Size(max = 255, message = "测试意图长度不能超过255")
    @TableField(value = "`intent`", condition = LIKE)
    @Excel(name = "测试意图")
    private String intent;

    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "`step_type`", condition = LIKE)
    @Excel(name = "测试步骤类型，文本/条目")
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65535")
    @TableField(value = "`test_step`", condition = LIKE)
    @Excel(name = "测试步骤")
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65535")
    @TableField(value = "`expected_result`", condition = LIKE)
    @Excel(name = "预期结果")
    private String expectedResult;

    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @TableField(value = "`state_id`")
    @Excel(name = "评审状态")
    private Long stateId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "`product_id`")
    @Excel(name = "关联产品")
    private Long productId;

    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    @TableField(value = "`requirement_id`")
    @Excel(name = "关联需求")
    private Long requirementId;

    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    @TableField(value = "`tree_id`")
    @Excel(name = "关联案例树")
    private Long treeId;

    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    @TableField(value = "`priority`")
    @Excel(name = "用例等级")
    private Integer priority;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65535")
    @TableField(value = "`prerequisite`", condition = LIKE)
    @Excel(name = "前提条件")
    private String prerequisite;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @TableField(value = "`library_id`")
    @Excel(name = "用例库")
    private Long libraryId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    @TableField(exist = false)
    private String[] tabName;

    /**
     * 标签实体类集合
     */
    @ApiModelProperty(value = "标签实体类集合")
    @TableField(exist = false)
    private List<TestTab> tabs;

    @Builder
    public TestCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String caseKey, String name, String intent, String stepType, String testStep,
            String expectedResult, Long stateId, Long productId, Long requirementId, Long treeId, Integer priority,
            Long leadingBy, String prerequisite, Long libraryId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.caseKey = caseKey;
        this.name = name;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.stateId = stateId;
        this.productId = productId;
        this.requirementId = requirementId;
        this.treeId = treeId;
        this.priority = priority;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.libraryId = libraryId;
    }

}
