package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className ProductCaselibraryConnect
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_product_caselibrary_connect")
@ApiModel(value = "ProductCaselibraryConnect", description = "")
@AllArgsConstructor
@Builder
public class ProductCaselibraryConnect extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @NotNull(message = "请填写产品id")
    @TableField(value = "product_id")
    @Excel(name = "产品id")
    private Long productId;

    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @NotNull(message = "请填写产品用例库id")
    @TableField(value = "caselibrary_id")
    @Excel(name = "产品用例库id")
    private Long caselibraryId;


    @Builder
    public ProductCaselibraryConnect(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    Long productId, Long caselibraryId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.productId = productId;
        this.caselibraryId = caselibraryId;
    }

}
