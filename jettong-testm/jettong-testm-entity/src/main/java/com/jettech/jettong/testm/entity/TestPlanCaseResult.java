package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_CLASS;


/**
 * 测试计划用例执行表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例执行表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanCaseResult
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_case_result")
@ApiModel(value = "TestPlanCaseResult", description = "测试计划用例执行表")
@AllArgsConstructor
public class TestPlanCaseResult extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    @NotNull(message = "请填写计划标识")
    @TableField(value = "`plan_id`")
    @Excel(name = "计划标识")
    private Long planId;

    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @TableField(value = "`testcase_id`")
    @Excel(name = "用例标识")
    private Long testcaseId;

    /**
     * 用例历史id
     */
    @ApiModelProperty(value = "用例历史id")
    @NotNull(message = "请填写用例历史id")
    @TableField(value = "`history_id`")
    @Excel(name = "用例历史id")
    private Long historyId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @NotEmpty(message = "请填写执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    @TableField(value = "`status`", condition = LIKE)
    @Excel(name = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    private String status;

    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    @Size(max = 65535, message = "执行结果详情长度不能超过65535")
    @TableField(value = "`result`", condition = LIKE)
    @Excel(name = "执行结果详情")
    private String result;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "`exec_by`")
    @Excel(name = "执行人")
    @Echo(api = USER_ID_CLASS, beanClass = User.class)
    private Long execBy;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @TableField(value = "`exec_time`")
    @Excel(name = "执行时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime execTime;

    /**
     * 执行操作
     */
    @ApiModelProperty(value = "执行操作")
    @TableField(value = "`operate_type`")
    @Excel(name = "执行操作")
    private String operateType;


    @ApiModelProperty(value = "预估执行时间")
    @TableField(value = "truexec_time")
    @Excel(name = "预估执行时间")
    private Long truexecTime;

    @ApiModelProperty(value = "版本")
    @TableField(value = "version")
    @Excel(name = "版本")
    private String version;

    @ApiModelProperty(value = "优先级")
    @TableField(value = "priority")
    @Excel(name = "优先级")
    private int priority;


    @Builder
    public TestPlanCaseResult(Long id, Long createdBy, LocalDateTime createTime, Long planId, Long testcaseId,
            String status, String result, Long execBy, LocalDateTime execTime, String operateType ,Long historyId,
            String name ,Long truexecTime, String version, int priority)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.planId = planId;
        this.testcaseId = testcaseId;
        this.status = status;
        this.result = result;
        this.execBy = execBy;
        this.execTime = execTime;
        this.operateType = operateType;
        this.historyId = historyId;
        this.name = name;
        this.truexecTime = truexecTime;
        this.version = version;
        this.priority = priority;
    }

}
