package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;

/**
 * 测试计划用例表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanCaseSaveDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseSaveVo", description = "测试任务用例表")
public class TestTaskCaseSaveVo
{

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "任务标识")
    private Long taskId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private List<Long> testcaseIds;
    /**
     * 叶级全选用例树级Id
     */
    @ApiModelProperty(value = "用例标识")
    private List<Long> testcaseTreeIds;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @NotNull(message = "请填写创建人")
    private Long createdBy;

    /**
     * 关联用例使用的字段 key = id, value = treeId
     */
    @ApiModelProperty(value = "用例标识")
    private HashMap<Long, Long> map;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private String execBy;

    @ApiModelProperty(value = "当前选择用例所关联的树id")
    private List<Long> connectTreeId;

    @ApiModelProperty(value = "用例执行状态")
    private String status;

    @ApiModelProperty(value = "用例执行等级")
    private Long priority;

    public Long getPriority() {
        return priority;
    }

    public void setPriority(Long priority) {
        this.priority = priority;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public List<Long> getTestcaseIds() {
        return testcaseIds;
    }

    public void setTestcaseIds(List<Long> testcaseIds) {
        this.testcaseIds = testcaseIds;
    }

    public List<Long> getTestcaseTreeIds() {
        return testcaseTreeIds;
    }

    public void setTestcaseTreeIds(List<Long> testcaseTreeIds) {
        this.testcaseTreeIds = testcaseTreeIds;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public HashMap<Long, Long> getMap() {
        return map;
    }

    public void setMap(HashMap<Long, Long> map) {
        this.map = map;
    }

    public String getExecBy() {
        return execBy;
    }

    public void setExecBy(String execBy) {
        this.execBy = execBy;
    }

    public List<Long> getConnectTreeId() {
        return connectTreeId;
    }

    public void setConnectTreeId(List<Long> connectTreeId) {
        this.connectTreeId = connectTreeId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}

