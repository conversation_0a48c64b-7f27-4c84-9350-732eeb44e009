package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 测试案例树表分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例树表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestCaseTree
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCaseTreePageQuery", description = "测试案例树表")
public class TestCaseTreePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;
    /**
     * 案例库ID
     */
    @ApiModelProperty(value = "案例库ID")
    private Long libraryId;
    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;
    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    private Integer level;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;

}
