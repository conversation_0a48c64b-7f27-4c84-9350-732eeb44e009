package com.jettech.jettong.testm.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 测试计划用例表分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanCase
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCasePageQuery", description = "测试计划用例表")
public class TestPlanCasePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    private Long planId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    private Long testcaseId;
    /**
     * 关联测试计划用例树id
     */
    @ApiModelProperty(value = "关联测试计划用例树id")
    @TableField(value = "`tree_id`")
    @Excel(name = "关联测试计划用例树id")
    private Long treeId;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "`exec_By`")
    @Excel(name = "执行人")
    private Long execBy;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态")
    @TableField(value = "`status`")
    @Excel(name = "执行结果状态")
    private String status;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "`priority`")
    @Excel(name = "优先级")
    private Integer priority;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @TableField(value = "`name`")
    @Excel(name = "名称/标题")
    private String name;
    /**
     * 是否含有执行人
     */
    @ApiModelProperty(value = "是否含有执行人")
    @Excel(name = "是否含有执行人")
    private Integer hasExecutor;

}
