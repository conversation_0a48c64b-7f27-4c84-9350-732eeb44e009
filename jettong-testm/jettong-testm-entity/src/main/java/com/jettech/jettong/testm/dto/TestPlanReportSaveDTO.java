package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanReportSaveDTO
 * @date 2022-03-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanReportSaveDTO", description = "")
public class TestPlanReportSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 报告id
     */
    @ApiModelProperty(value = "报告id")
    @NotNull(message = "请填写报告id")
    private Long reportId;
    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    @NotNull(message = "请填写产品ID")
    private Long planId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    private String name;

}
