package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestProductCase
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCasePageQuery", description = "")
public class TestProductCasePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    private String caseKey;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;
    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    private String intent;
    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    private String stepType;
    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    private String testStep;
    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    private String expectedResult;
    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    private Long stateId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private String requirementId;
    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    private Long treeId;
    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    private Integer priority;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    private String prerequisite;
    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    private Long libraryId;
    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String[] tabName;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;
//    /**
//     * 测试计划id，关联用例时使用
//     */
//    @ApiModelProperty(value = "测试计划id，关联用例时使用")
//    private Long planId;

    /**
     * 测试任务id，关联用例时使用
     */
    @ApiModelProperty(value = "测试任务id，关联用例时使用")
    private Long taskId;

    /**
     * 交易id
     */
    @ApiModelProperty(value = "交易id")
    private List<Long> functionIds;

    /**
     * 草稿
     */
    @ApiModelProperty(value = "草稿")
    private Boolean draft;


    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    private Boolean state;

    @ApiModelProperty(value = "预估执行时间")
    private Long execTime;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    private Long moduleFunctionId;

    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    private Long functionPointsId;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;

    /**
     * 是否被引用
     */
    @ApiModelProperty(value = "是否被引用")
    private Boolean isCited;


}
