package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanGroupTreeOrg
 * @date 2022-06-07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_group_tree_org")
@ApiModel(value = "TestPlanGroupTreeOrg", description = "测试计划授权信息")
@AllArgsConstructor
public class TestPlanGroupTreeOrg extends SuperEntity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "测试计划树id")
    @NotNull(message = "请填写测试计划树id")
    @TableField(value = "test_plan_group_tree_id")
    @Excel(name = "测试计划树id")
    private Long testPlanGroupTreeId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @NotNull(message = "请填写机构id")
    @TableField(value = "org_id")
    @Excel(name = "机构id")
    private Long orgId;

    /**
     * 是否创建机构，默认false
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;



    @Builder
    public TestPlanGroupTreeOrg(Long id, Long createdBy, LocalDateTime createTime,
                    Long testPlanGroupTreeId, Long orgId ,Boolean isCreate)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.testPlanGroupTreeId = testPlanGroupTreeId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }

}
