package com.jettech.jettong.testm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.TEST_PROJECT_CASE_ID_CLASS;


/**
 * 项目和测试方式关联表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 项目和测试方式关联表实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.entity.project
 * @className ProjectInfoProject
 * @date 2025-08-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("testproductcase_testmode")
@ApiModel(value = "TestProductCaseTestMode", description = "测试用例和测试方式关联表")
@AllArgsConstructor
public class TestProductCaseTestMode extends Entity<Long>  implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 项目id
     */
    @ApiModelProperty(value = "测试用例id")
    @NotNull(message = "请填写测试用例id")
    @TableField(value = "testproductcase_id")
    @Echo(api = TEST_PROJECT_CASE_ID_CLASS, beanClass = TestProductCase.class)
    private Long testProductCaseId;

    /**
     * 关联项目id
     */
    @ApiModelProperty(value = "关联测试方式字典id")
    @NotNull(message = "请填写关联测试方式字典id")
    @TableField(value = "testmode_id")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.TEST_MODE)
    private Long testmodeId;


    public TestProductCaseTestMode(Long testProductCaseId, Long testmodeId,Long id, Long createdBy,
            LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime) {
        this.testProductCaseId = testProductCaseId;
        this.testmodeId = testmodeId;
        this.id = id;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createTime =  createTime;
        this.updateTime = updateTime;
    }
}
