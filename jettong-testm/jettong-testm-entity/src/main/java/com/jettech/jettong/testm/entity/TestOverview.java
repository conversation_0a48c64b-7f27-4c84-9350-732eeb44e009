package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;


/**
 * 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestOverview
 * @date 2022-05-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_overview")
@ApiModel(value = "TestOverview", description = "")
@AllArgsConstructor
public class TestOverview extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 用例最低优先级
     */
    @ApiModelProperty(value = "用例最低优先级")
    @TableField(value = "case_minimum")
    @Excel(name = "用例最低优先级")
    private Long caseMinimum;

    /**
     * 用例普通优先级
     */
    @ApiModelProperty(value = "用例普通优先级")
    @TableField(value = "case_ordinary")
    @Excel(name = "用例普通优先级")
    private Long caseOrdinary;

    /**
     * 用例较低优先级
     */
    @ApiModelProperty(value = "用例较低优先级")
    @TableField(value = "case_lower")
    @Excel(name = "用例较低优先级")
    private Long caseLower;

    /**
     * 用例较高优先级
     */
    @ApiModelProperty(value = "用例较高优先级")
    @TableField(value = "case_higher")
    @Excel(name = "用例较高优先级")
    private Long caseHigher;

    /**
     * 用例最高优先级
     */
    @ApiModelProperty(value = "用例最高优先级")
    @TableField(value = "case_highest")
    @Excel(name = "用例最高优先级")
    private Long caseHighest;

    /**
     * 计划未开始
     */
    @ApiModelProperty(value = "计划未开始")
    @TableField(value = "plan_todo")
    @Excel(name = "计划未开始")
    private Long planTodo;

    /**
     * 计划进行中
     */
    @ApiModelProperty(value = "计划进行中")
    @TableField(value = "plan_progress")
    @Excel(name = "计划进行中")
    private Long planProgress;

    /**
     * 计划已完成
     */
    @ApiModelProperty(value = "计划已完成")
    @TableField(value = "plan_done")
    @Excel(name = "计划已完成")
    private Long planDone;

    /**
     * 缺陷最低优先级
     */
    @ApiModelProperty(value = "缺陷最低优先级")
    @TableField(value = "bug_minimun")
    @Excel(name = "缺陷最低优先级")
    private Long bugMinimun;

    /**
     * 缺陷较低优先级
     */
    @ApiModelProperty(value = "缺陷较低优先级")
    @TableField(value = "bug_lower")
    @Excel(name = "缺陷较低优先级")
    private Long bugLower;

    /**
     * 缺陷普通优先级
     */
    @ApiModelProperty(value = "缺陷普通优先级")
    @TableField(value = "bug_ordinary")
    @Excel(name = "缺陷普通优先级")
    private Long bugOrdinary;

    /**
     * 缺陷较高优先级
     */
    @ApiModelProperty(value = "缺陷较高优先级")
    @TableField(value = "bug_higher")
    @Excel(name = "缺陷较高优先级")
    private Long bugHigher;

    /**
     * 缺陷最高优先级
     */
    @ApiModelProperty(value = "缺陷最高优先级")
    @TableField(value = "bug_highest")
    @Excel(name = "缺陷最高优先级")
    private Long bugHighest;


    @Builder
    public TestOverview(Long id, 
                    Long caseMinimum, Long caseOrdinary, Long caseLower, Long caseHigher, Long caseHighest, 
                    Long planTodo, Long planProgress, Long planDone, Long bugMinimun, Long bugLower, Long bugOrdinary, 
                    Long bugHigher, Long bugHighest)
    {
        this.id = id;
        this.caseMinimum = caseMinimum;
        this.caseOrdinary = caseOrdinary;
        this.caseLower = caseLower;
        this.caseHigher = caseHigher;
        this.caseHighest = caseHighest;
        this.planTodo = planTodo;
        this.planProgress = planProgress;
        this.planDone = planDone;
        this.bugMinimun = bugMinimun;
        this.bugLower = bugLower;
        this.bugOrdinary = bugOrdinary;
        this.bugHigher = bugHigher;
        this.bugHighest = bugHighest;
    }

}
