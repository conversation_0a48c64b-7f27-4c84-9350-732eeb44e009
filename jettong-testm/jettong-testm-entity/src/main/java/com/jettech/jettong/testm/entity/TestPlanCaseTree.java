package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 测试计划关联用例树表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划关联用例树表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanCaseTree
 * @date 2022-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_case_tree")
@ApiModel(value = "TestPlanCaseTree", description = "测试计划关联用例树表")
@AllArgsConstructor
public class TestPlanCaseTree extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 测试计划Id
     */
    @ApiModelProperty(value = "测试计划Id")
    @NotNull(message = "请填写测试计划Id")
    @TableField(value = "plan_id")
    @Excel(name = "测试计划Id")
    private Long planId;

    /**
     * 树id
     */
    @ApiModelProperty(value = "树id")
    @NotNull(message = "请填写树id")
    @TableField(value = "tree_id")
    @Excel(name = "树id")
    private Long treeId;

    /**
     * 树名称
     */
    @ApiModelProperty(value = "树名称")
    @NotNull(message = "请填写树名称")
    @TableField(value = "name")
    @Excel(name = "树名称")
    private String name;

    /**
     * 父级树id
     */
    @ApiModelProperty(value = "父级树id")
    @NotNull(message = "请填写父级树id")
    @TableField(value = "parent_id")
    @Excel(name = "父级树id")
    private Long parentId;

    /**
     * 测试用例id
     */
    @ApiModelProperty(value = "测试用例id")
    @TableField(value = "case_id")
    @Excel(name = "测试用例id")
    private Long caseId;

    /**
     * 维护人
     */
    @ApiModelProperty(value = "维护人")
    @TableField(value = "managing_by")
    @Excel(name = "维护人")
    private Long managingBy;


    @Builder
    public TestPlanCaseTree(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
                            Long planId, Long treeId, String name, Long parentId, Long caseId, Long managingBy)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.planId = planId;
        this.treeId = treeId;
        this.name = name;
        this.parentId = parentId;
        this.caseId = caseId;
        this.managingBy = managingBy;
    }

}
