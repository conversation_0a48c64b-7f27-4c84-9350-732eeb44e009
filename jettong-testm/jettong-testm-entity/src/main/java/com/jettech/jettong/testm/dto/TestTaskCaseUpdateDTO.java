package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.jettech.basic.base.entity.SuperEntity;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 测试任务用例表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTaskCaseUpdateDTO
 * @date 2025-08-12
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseUpdateDTO", description = "测试任务用例表")
public class TestTaskCaseUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "请填写id")
    private Long id;
    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    private Long taskId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private Long testcaseId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long updatedBy;
    /**
     * 冗余字段:优先级
     */
    @ApiModelProperty(value = "冗余字段:优先级")
    private Integer priority;
    /**
     * 冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    private String status;
    /**
     * 冗余字段:名称/标题
     */
    @ApiModelProperty(value = "冗余字段:名称/标题")
    @Size(max = 500, message = "冗余字段:名称/标题长度不能超过500")
    private String name;
    /**
     * 冗余字段:版本号
     */
    @ApiModelProperty(value = "冗余字段:版本号")
    @Size(max = 20, message = "冗余字段:版本号长度不能超过20")
    private String version;
    /**
     * 冗余字段:执行人
     */
    @ApiModelProperty(value = "冗余字段:执行人")
    private Long execBy;
    /**
     * 冗余字段:用例树id
     */
    @ApiModelProperty(value = "冗余字段:用例树id")
    private Long treeId;
    /**
     * 冗余字段:执行结束时间
     */
    @ApiModelProperty(value = "冗余字段:执行结束时间")
    private LocalDateTime execEtime;
    /**
     * 冗余字段:英文标识
     */
    @ApiModelProperty(value = "冗余字段:英文标识")
    @Size(max = 20, message = "冗余字段:英文标识长度不能超过20")
    private String caseKey;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    private Long moduleFunctionId;

}
