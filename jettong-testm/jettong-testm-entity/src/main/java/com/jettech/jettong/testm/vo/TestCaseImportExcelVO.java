package com.jettech.jettong.testm.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 测试案例导入excel对象
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例导入excel对象
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.testm.vo
 * @className TestCaseImportExcelVO
 * @date 2022/01/17 15:25
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCaseImportExcelVO", description = "测试案例导入excel对象")
public class TestCaseImportExcelVO implements Serializable, IExcelModel, IExcelDataModel
{

    @Excel(name = "用例标题")
    private String name;

    @Excel(name = "执行方式")
    private String runMond;

    @Excel(name = "测试意图")
    private String summary;

    @Excel(name = "前置条件")
    private String prerequisite;

    @Excel(name = "步骤动作")
    private String testStep;

    @Excel(name = "预期结果")
    private String expectedResult;

    private List<TestCaseImportExcelVO> list;

    private String errorMsg;

    private Integer rowNum;

    /**
     * 获取行号
     *
     * @return Integer
     */
    @Override
    public Integer getRowNum()
    {
        return rowNum;
    }

    /**
     * 设置行号
     *
     * @param rowNum 行号
     */
    @Override
    public void setRowNum(Integer rowNum)
    {
        this.rowNum = rowNum;
    }

    /**
     * 获取错误数据
     *
     * @return String
     */
    @Override
    public String getErrorMsg()
    {
        return errorMsg;
    }

    /**
     * 设置错误信息
     *
     * @param errorMsg 错误信息
     */
    @Override
    public void setErrorMsg(String errorMsg)
    {
        this.errorMsg = errorMsg;
    }
}
