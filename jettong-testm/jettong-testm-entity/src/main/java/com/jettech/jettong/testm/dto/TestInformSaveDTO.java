package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.jettech.basic.base.entity.Entity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 新测试报告新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新测试报告新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testInform.dto
 * @className TestInformSaveDTO
 * @date 2025-08-23
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestInformSaveDTO", description = "新测试报告")
public class TestInformSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @Size(max = 100, message = "类型长度不能超过100")
    private String type;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    private String description;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Size(max = 20, message = "状态code长度不能超过20")
    private String stateCode;
    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @Size(max = 20, message = "优先级code长度不能超过20")
    private String priorityCode;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private Long requirementId;
    /**
     * 测试需求
     */
    @ApiModelProperty(value = "测试需求")
    private Long testreqId;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 协作模式1单人 2多人
     */
    @ApiModelProperty(value = "协作模式1单人 2多人")
    private Integer teamMode;

}
