package com.jettech.jettong.testm.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 测试计划用例表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanCaseUpdateDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseUpdateDTO", description = "测试计划用例表")
public class TestPlanCaseUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    @NotNull(message = "请填写计划标识")
    private Long planId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private Long testcaseId;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态")
    @TableField(value = "`status`")
    @Excel(name = "执行结果状态")
    private String status;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "`executor`")
    @Excel(name = "执行人")
    private Long execBy;
}
