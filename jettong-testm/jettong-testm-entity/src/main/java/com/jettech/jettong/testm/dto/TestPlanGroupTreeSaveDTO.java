package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 测试计划分组树表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划分组树表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanGroupTreeSaveDTO
 * @date 2022-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanGroupTreeSaveDTO", description = "测试计划分组树表")
public class TestPlanGroupTreeSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long parentId;
    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "状态")
    private Boolean state;

}
