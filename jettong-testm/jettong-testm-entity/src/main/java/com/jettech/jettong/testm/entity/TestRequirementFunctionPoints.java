package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试分析功能要点表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表实体类
 * @projectName vone
 * @package com.jettech.jettong.testm.entity
 * @className TestRequirementFunctionPoints
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_requirement_function_points")
@ApiModel(value = "TestRequirementFunctionPoints", description = "测试分析功能要点表")
public class TestRequirementFunctionPoints extends Entity<Long> implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 测试需求id
     */
    @ApiModelProperty(value = "测试需求id")
    @TableField(value = "issue_test_req_id")
    @Excel(name = "测试需求id")
    private Long issueTestReqId;

    /**
     * 功能（交易）id
     */
    @ApiModelProperty(value = "功能（交易）id")
    @NotNull(message = "请填写功能（交易）id")
    @TableField(value = "function_id")
    @Excel(name = "功能（交易）id")
    private Long functionId;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Size(max = 20, message = "状态code长度不能超过20")
    @TableField(value = "`state_code`", condition = LIKE)
    private String stateCode;
    /**
     * 需求分析记录id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "task_id")
    @Excel(name = "任务id")
    private Long taskId;
    /**
     * 需求分析记录id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    @Excel(name = "项目id")
    private Long projectId;

    /**
     * 功能点
     */
    @ApiModelProperty(value = "功能点")
    @Size(max = 1000, message = "功能点长度不能超过1000")
    @TableField(value = "function_point", condition = LIKE)
    @Excel(name = "功能点")
    private String functionPoint;

    /**
     * 测试要点
     */
    @ApiModelProperty(value = "测试要点")
    @Size(max = 2000, message = "测试要点长度不能超过2000")
    @TableField(value = "test_points", condition = LIKE)
    @Excel(name = "测试要点")
    private String testPoints;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    @Size(max = 255, message = "规则类型长度不能超过255")
    @TableField(value = "rule_type", condition = LIKE)
    @Excel(name = "规则类型")
    private String ruleType;

    /**
     * 是否涉账
     */
    @ApiModelProperty(value = "是否涉账")
    @TableField(value = "involve_account")
    @Excel(name = "是否涉账", replace = {"是_true", "否_false", "_null"})
    private Boolean involveAccount;

    /**
     * 是否设计批处理
     */
    @ApiModelProperty(value = "是否设计批处理")
    @TableField(value = "involve_batch")
    @Excel(name = "是否设计批处理", replace = {"是_true", "否_false", "_null"})
    private Boolean involveBatch;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Size(max = 255, message = "优先级长度不能超过255")
    @TableField(value = "priority", condition = LIKE)
    @Excel(name = "优先级")
    private String priority;

    /**
     * 规则来源
     */
    @ApiModelProperty(value = "规则来源")
    @Size(max = 255, message = "规则来源长度不能超过255")
    @TableField(value = "rule_source", condition = LIKE)
    @Excel(name = "规则来源")
    private String ruleSource;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 1000, message = "备注长度不能超过1000")
    @TableField(value = "remark", condition = LIKE)
    @Excel(name = "备注")
    private String remark;


    /**
     * 扩展字段 1
     */
    @ApiModelProperty(value = "扩展字段 1")
    @Size(max = 255, message = "扩展字段 1长度不能超过255")
    @TableField(value = "c1", condition = LIKE)
    @Excel(name = "扩展字段 1")
    private String c1;

    /**
     * 扩展字段 2
     */
    @ApiModelProperty(value = "扩展字段 2")
    @Size(max = 255, message = "扩展字段 2长度不能超过255")
    @TableField(value = "c2", condition = LIKE)
    @Excel(name = "扩展字段 2")
    private String c2;

    /**
     * 扩展字段 3
     */
    @ApiModelProperty(value = "扩展字段 3")
    @Size(max = 255, message = "扩展字段 3长度不能超过255")
    @TableField(value = "c3", condition = LIKE)
    @Excel(name = "扩展字段 3")
    private String c3;

    /**
     * 扩展字段 4
     */
    @ApiModelProperty(value = "扩展字段 4")
    @Size(max = 255, message = "扩展字段 4长度不能超过255")
    @TableField(value = "c4", condition = LIKE)
    @Excel(name = "扩展字段 4")
    private String c4;

    /**
     * 扩展字段 5
     */
    @ApiModelProperty(value = "扩展字段 5")
    @Size(max = 255, message = "扩展字段 5长度不能超过255")
    @TableField(value = "c5", condition = LIKE)
    @Excel(name = "扩展字段 5")
    private String c5;
    /**
     * 测试案例计数
     */
    @ApiModelProperty(value = "测试案例计数,默认为0")
    @TableField(value = "case_count")
    @Excel(name = "测试案例计数，默认为0")
    private Integer caseCount;

    @Builder
    public TestRequirementFunctionPoints(
                    Long id, Long issueTestReqId, Long functionId, String functionPoint,
                    String testPoints, String ruleType, Boolean involveAccount, Boolean involveBatch, String priority, String ruleSource, 
                    String remark, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime, String c1, 
                    String c2, String c3, String c4, String c5,Integer caseCount)
    {
        this.id = id;
        this.issueTestReqId = issueTestReqId;
        this.functionId = functionId;
        this.functionPoint = functionPoint;
        this.testPoints = testPoints;
        this.ruleType = ruleType;
        this.involveAccount = involveAccount;
        this.involveBatch = involveBatch;
        this.priority = priority;
        this.ruleSource = ruleSource;
        this.remark = remark;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.c1 = c1;
        this.c2 = c2;
        this.c3 = c3;
        this.c4 = c4;
        this.c5 = c5;
        this.caseCount = caseCount;
    }

}
