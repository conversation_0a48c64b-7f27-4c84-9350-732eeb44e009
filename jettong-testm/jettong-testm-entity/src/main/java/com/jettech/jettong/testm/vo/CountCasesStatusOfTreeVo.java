package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/1/19 9:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "CountCasesStatusOfTreeVo", description = "树级下的用例状态个数")
public class CountCasesStatusOfTreeVo
{

    private Long count;
    //成功
    private Long system;
    //阻塞
    private Long block;
    //未执行
    private Long undo;
    //跳过
    private Long skip;
    //失败
    private Long smoke;
}