package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import com.jettech.basic.base.entity.SuperEntity;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 测试任务关联用例树表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务关联用例树表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTaskCaseTreeUpdateDTO
 * @date 2025-08-13
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseTreeUpdateDTO", description = "测试任务关联用例树表")
public class TestTaskCaseTreeUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;


    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = "请填写主键id")
    private Long id;
    /**
     * 测试任务Id
     */
    @ApiModelProperty(value = "测试任务Id")
    @NotNull(message = "请填写测试任务Id")
    private Long taskId;
    /**
     * 树id
     */
    @ApiModelProperty(value = "树id")
    @NotNull(message = "请填写树id")
    private Long treeId;
    /**
     * 树级名称
     */
    @ApiModelProperty(value = "树级名称")
    @Size(max = 200, message = "树级名称长度不能超过200")
    private String name;
    /**
     * 树父级Id
     */
    @ApiModelProperty(value = "树父级Id")
    private Long parentId;
    /**
     * 测试用例id
     */
    @ApiModelProperty(value = "测试用例id")
    private Long caseId;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long updatedBy;
    /**
     * 维护人
     */
    @ApiModelProperty(value = "维护人")
    private Long managingBy;
}
