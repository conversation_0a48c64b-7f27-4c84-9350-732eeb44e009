package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 柱状图所需的状态对应的配置
 *
 * <AUTHOR>
 * @version 1.0
 * @description 柱状图所需的状态对应的配置
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.dto
 * @className StatisticsHistogramConfigDTO
 * @date 2022/4/11 16:05
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "StatisticsHistogramDTO", description = "")
public class StatisticsHistogramConfigDTO {
    /**
     * 数据库中原始字段
     */
    private Object key;
    /**
     * 前端展示字段
     */
    private String name;
    /**
     * 状态的顺序
     */
    private int order;
    /**
     * 状态的颜色
     */
    private String color;
    /**
     * 状态对应的数据
     */
    private List<Long> data;

    public StatisticsHistogramConfigDTO(String name, String color, int order) {
        this.name = name;
        this.color = color;
        this.order = order;
    }

    public StatisticsHistogramConfigDTO(String name, List<Long> data) {
        this.name = name;
        this.data = data;
    }
}
