package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className ProductCaseLibrarySaveDTO
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductCaseLibrarySaveDTO", description = "")
public class ProductCaseLibrarySaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    private String description;
    /**
     * 描述
     */
    @ApiModelProperty(value = "类型")
    @Size(max = 20, message = "类型长度不能超过20")
    private String type;
    /**
     * 关联多个产品
     */
    @ApiModelProperty(value = "关联多个产品")
    private List<Long> productIds;

    @ApiModelProperty(value = "树深度")
    @Size(max = 5, message = "树深度长度不能超过5")
    private String depth;

    @ApiModelProperty(value = "父用例库id")
    private Long parentId;

    @ApiModelProperty(value = "标签id")
    private String tabId;

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    private Long projectId;

    @ApiModelProperty(value = "英文标识")
    private String caseKey;


}
