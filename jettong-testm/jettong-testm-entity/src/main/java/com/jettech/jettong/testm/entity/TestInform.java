package com.jettech.jettong.testm.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.jettech.basic.base.entity.Entity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import com.jettech.basic.model.EchoVO;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 新测试报告实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新测试报告实体类
 * @projectName jettong
 * @package com.jettech.jettong.testInform.entity
 * @className TestInform
 * @date 2025-08-23
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_inform")
@ApiModel(value = "TestInform", description = "新测试报告")
@AllArgsConstructor
public class TestInform extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @Size(max = 100, message = "类型长度不能超过100")
    @TableField(value = "type", condition = LIKE)
    private String type;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "description", condition = LIKE)
    private String description;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Size(max = 20, message = "状态code长度不能超过20")
    @TableField(value = "state_code", condition = LIKE)
    private String stateCode;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @Size(max = 20, message = "优先级code长度不能超过20")
    @TableField(value = "priority_code", condition = LIKE)
    private String priorityCode;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @TableField(value = "product_id")
    private Long productId;

    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    @TableField(value = "requirement_id")
    private Long requirementId;

    /**
     * 测试需求
     */
    @ApiModelProperty(value = "测试需求")
    @TableField(value = "testreq_Id")
    private Long testreqId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "project_id")
    private Long projectId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by")
    private Long leadingBy;

    /**
     * 协作模式1单人 2多人
     */
    @ApiModelProperty(value = "协作模式1单人 2多人")
    @TableField(value = "team_mode")
    private Integer teamMode;


    @Builder
    public TestInform(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy, 
                    String name, String type, String description, String stateCode, String priorityCode, 
                    Long productId, Long requirementId, Long testreqId, Long taskId, Long projectId, Long leadingBy, Integer teamMode)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.type = type;
        this.description = description;
        this.stateCode = stateCode;
        this.priorityCode = priorityCode;
        this.productId = productId;
        this.requirementId = requirementId;
        this.testreqId = testreqId;
        this.taskId = taskId;
        this.projectId = projectId;
        this.leadingBy = leadingBy;
        this.teamMode = teamMode;
    }

}
