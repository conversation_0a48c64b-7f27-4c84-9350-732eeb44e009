package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 工作台需求概览组件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台用例概览组件
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.issue.vo
 * @className RequirementTypeComponentResult
 * @date 2021/12/2 20:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestReportMainComponent", description = "工作台用例概览组件")
public class TestReportMainComponent implements Serializable
{

    /**
     * 缺陷分布 维度
     */
    private List<TestReportBugComponent> testReportBugComponents;

    /**
     * 优先级维度
     */
    List<TestReportPriorityComponent> testReportPriorityComponents;

    /**
     * 标签维度
     */
    List<TestReportTabComponent> testReportTabComponents;

    /**
     * 执行人维度
     */
    List<TestReportExecComponent> testReportExecComponents;
}
