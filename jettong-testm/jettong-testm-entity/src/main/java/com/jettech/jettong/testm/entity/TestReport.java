package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestReport
 * @date 2021-11-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_report")
@ApiModel(value = "TestReport", description = "")
@AllArgsConstructor
public class TestReport extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 测试计划Id
     */
    @ApiModelProperty(value = "测试任务Id")
    @NotNull(message = "请填写测试任务Id")
    @TableField(value = "`task_id`")
    @Excel(name = "测试任务Id")
    private Long taskId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leader_by`")
    @Excel(name = "负责人")
    private Long leaderBy;

    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    @TableField(value = "`project_id`")
    @Excel(name = "项目Id")
    private Long projectId;

    /**
     * 环境Id
     */
    @ApiModelProperty(value = "环境Id")
    @TableField(value = "`env_id`")
    @Excel(name = "环境Id")
    private Long envId;

    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    @TableField(value = "`product_version`")
    @Excel(name = "关联产品版本")
    private Long productVersion;

    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    @TableField(value = "`program_id`")
    @Excel(name = "关联项目集")
    private Long programId;

    /**
     * 计划执行进度
     */
    @ApiModelProperty(value = "计划执行进度")
    @TableField(value = "`rate_progress`")
    @Excel(name = "计划执行进度")
    private Long rateProgress;

    /**
     * 成功用例个数
     */
    @ApiModelProperty(value = "成功用例个数")
    @TableField(value = "`system_num`")
    @Excel(name = "成功用例个数")
    private Long systemNum;

    /**
     * 失败用例个数
     */
    @ApiModelProperty(value = "失败用例个数")
    @TableField(value = "`smoke_num`")
    @Excel(name = "失败用例个数")
    private Long smokeNum;

    /**
     * 阻塞用例个数
     */
    @ApiModelProperty(value = "阻塞用例个数")
    @TableField(value = "`blocking_num`")
    @Excel(name = "阻塞用例个数")
    private Long blockingNum;

    /**
     * 跳过用例个数
     */
    @ApiModelProperty(value = "跳过用例个数")
    @TableField(value = "`skip_num`")
    @Excel(name = "跳过用例个数")
    private Long skipNum;

    /**
     * 未执行用例个数
     */
    @ApiModelProperty(value = "未执行用例个数")
    @TableField(value = "`undo_num`")
    @Excel(name = "未执行用例个数")
    private Long undoNum;

    /**
     * 关联产品Id
     */
    @ApiModelProperty(value = "关联产品Id")
    @TableField(value = "`product_id`")
    @Excel(name = "关联产品Id")
    private Long productId;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @TableField(value = "`delay`")
    @Excel(name = "是否延期", replace = {"是_true", "否_false", "_null"})
    private Boolean delay;

    /**
     * 测试报告名称
     */
    @ApiModelProperty(value = "测试报告名称")
    @TableField(value = "`test_report_name`")
    @Excel(name = "测试报告名称")
    private String testReportName;

    /**
     * 测试报告类型
     */
    @ApiModelProperty(value = "测试报告类型")
    @TableField(value = "`type`")
    @Excel(name = "测试报告类型")
    private Long type;

    /**
     * 测试报告类型内容
     */
    @ApiModelProperty(value = "测试报告内容")
    @TableField(value = "`type_content`")
    @Excel(name = "测试报告内容")
    private String typeContent;

    /**
     * 是否是产品测试报告
     */
    @ApiModelProperty(value = "是否是产品测试报告")
    @TableField(value = "`product`")
    @Excel(name = "是否是产品测试报告", replace = {"是_true", "否_false", "_null"})
    private Boolean product;
    /**
     * 报表执行结果
     */
    @ApiModelProperty(value = "报表执行结果")
    @TableField(value = "`report_result`")
    @Excel(name = "报表执行结果")
    private String reportResult;

    @Builder
    public TestReport(Long id, Long createdBy, LocalDateTime createTime,
            Long taskId, Long leaderBy, Long projectId, Long envId, Long productVersion,
            Long programId, Long rateProgress, Long systemNum, Long smokeNum, Long blockingNum, Long skipNum,
            Long undoNum, Long productId, Boolean delay, String testReportName, Long type, String typeContent ,Boolean product,String reportResult)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.taskId = taskId;
        this.leaderBy = leaderBy;
        this.projectId = projectId;
        this.envId = envId;
        this.productVersion = productVersion;
        this.programId = programId;
        this.rateProgress = rateProgress;
        this.systemNum = systemNum;
        this.smokeNum = smokeNum;
        this.blockingNum = blockingNum;
        this.skipNum = skipNum;
        this.undoNum = undoNum;
        this.productId = productId;
        this.delay = delay;
        this.testReportName = testReportName;
        this.type = type;
        this.typeContent = typeContent;
        this.product = product;
        this.reportResult = reportResult;
    }

}
