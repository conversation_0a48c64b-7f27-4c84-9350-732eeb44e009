package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/1/19 9:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCastTestStepVO", description = "复制用例信息实体类")
public class TestProductCaseVO
{

    private List<Long> caseId;

    private Long treeId;

}
