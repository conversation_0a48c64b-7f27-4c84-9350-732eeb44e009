package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 测试案例信息导出条件实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例信息导出条件实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.testm.dto
 * @className TestCaseExportQuery
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCaseExportQuery", description = "测试案例信息导出条件实体类")
public class TestCaseExportQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    private String caseKey;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;
    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    private String intent;
    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    private String stepType;
    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    private String testStep;
    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    private String expectedResult;
    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    private Long stateId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private Long requirementId;
    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    private Long treeId;
    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    private Integer priority;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    private String prerequisite;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    private Long libraryId;

}
