package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 测试标签拖拽实体类
 *
 * <AUTHOR>
 * @version 1.2
 * @description 测试标签拖拽实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTabDragUpdateDTO
 * @date 2022-03-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTabDragUpdateDTO")
public class TestTabDragUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @NotNull(message = "请填写父ID")
    private Long parentId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "请填写排序")
    private Integer sort;

    /**
     * 拖拽落入的分组的父ID
     */
    @ApiModelProperty(value = "拖拽落入的分组的父ID")
    @NotNull(message = "请填写拖拽落入的分组的父ID")
    private Long newParentId;
}
