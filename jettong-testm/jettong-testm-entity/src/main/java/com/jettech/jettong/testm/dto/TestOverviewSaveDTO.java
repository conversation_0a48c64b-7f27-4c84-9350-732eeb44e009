package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestOverviewSaveDTO
 * @date 2022-05-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestOverviewSaveDTO", description = "")
public class TestOverviewSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 用例最低优先级
     */
    @ApiModelProperty(value = "用例最低优先级")
    private Long caseMinimum;
    /**
     * 用例普通优先级
     */
    @ApiModelProperty(value = "用例普通优先级")
    private Long caseOrdinary;
    /**
     * 用例较低优先级
     */
    @ApiModelProperty(value = "用例较低优先级")
    private Long caseLower;
    /**
     * 用例较高优先级
     */
    @ApiModelProperty(value = "用例较高优先级")
    private Long caseHigher;
    /**
     * 用例最高优先级
     */
    @ApiModelProperty(value = "用例最高优先级")
    private Long caseHighest;
    /**
     * 计划未开始
     */
    @ApiModelProperty(value = "计划未开始")
    private Long planTodo;
    /**
     * 计划进行中
     */
    @ApiModelProperty(value = "计划进行中")
    private Long planProgress;
    /**
     * 计划已完成
     */
    @ApiModelProperty(value = "计划已完成")
    private Long planDone;
    /**
     * 缺陷最低优先级
     */
    @ApiModelProperty(value = "缺陷最低优先级")
    private Long bugMinimun;
    /**
     * 缺陷较低优先级
     */
    @ApiModelProperty(value = "缺陷较低优先级")
    private Long bugLower;
    /**
     * 缺陷普通优先级
     */
    @ApiModelProperty(value = "缺陷普通优先级")
    private Long bugOrdinary;
    /**
     * 缺陷较高优先级
     */
    @ApiModelProperty(value = "缺陷较高优先级")
    private Long bugHigher;
    /**
     * 缺陷最高优先级
     */
    @ApiModelProperty(value = "缺陷最高优先级")
    private Long bugHighest;

}
