package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 测试标签案例关联实体类
 *
 * <AUTHOR>
 * @version 1.2
 * @description 测试标签案例关联实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTabCase
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_tab_case")
@ApiModel(value = "TestTab", description = "测试标签")
@AllArgsConstructor
public class TestTabCase extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 测试案例ID
     */
    @ApiModelProperty(value = "测试案例ID")
    @TableField(value = "`case_id`")
    @Excel(name = "测试案例ID")
    private Long caseId;

    /**
     * 测试标签ID
     */
    @ApiModelProperty(value = "测试标签ID")
    @TableField(value = "`tab_id`")
    @Excel(name = "测试标签ID")
    private Long tabId;
    /**
     * 产品库用例ID
     */
    @ApiModelProperty(value = "产品库用例ID")
    @TableField(value = "`prod_id`")
    @Excel(name = "产品库用例ID")
    private Long prodId;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    @TableField(value = "`tab_name`")
    @Excel(name = "标签名称")
    private String tabName;

    @Builder
    public TestTabCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
                       Long caseId, Long tabId, Long prodId, String tabName)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.tabId = tabId;
        this.caseId = caseId;
        this.prodId = prodId;
        this.tabName = tabName;
    }
    public TestTabCase(Long id,Long caseId, Long tabId, Long prodId, String tabName)
    {
        this.id = id;
        this.caseId = caseId;
        this.tabId = tabId;
        this.prodId = prodId;
        this.tabName = tabName;
    }

}
