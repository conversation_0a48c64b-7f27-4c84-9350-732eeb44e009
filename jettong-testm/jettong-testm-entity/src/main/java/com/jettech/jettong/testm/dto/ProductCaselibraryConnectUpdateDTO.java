package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className ProductCaselibraryConnectUpdateDTO
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductCaselibraryConnectUpdateDTO", description = "")
public class ProductCaselibraryConnectUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @NotNull(message = "请填写产品id")
    private Long productId;
    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @NotNull(message = "请填写产品用例库id")
    private Long caselibraryId;
}
