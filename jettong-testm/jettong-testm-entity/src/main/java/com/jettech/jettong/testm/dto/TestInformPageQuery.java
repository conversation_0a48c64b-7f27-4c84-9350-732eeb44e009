package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 新测试报告分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新测试报告分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testInform.dto
 * @className TestInform
 * @date 2025-08-23
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestInformPageQuery", description = "新测试报告")
public class TestInformPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    private String stateCode;
    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    private String priorityCode;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private Long requirementId;
    /**
     * 测试需求
     */
    @ApiModelProperty(value = "测试需求")
    private Long testreqId;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 协作模式1单人 2多人
     */
    @ApiModelProperty(value = "协作模式1单人 2多人")
    private Integer teamMode;

}
