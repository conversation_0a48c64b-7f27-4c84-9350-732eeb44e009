package com.jettech.jettong.testm.config;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import java.util.List;

/**
 * 脑图层级配置类
 * <AUTHOR>
 * @version 1.0
 * @description 脑图层级配置类
 * @projectName jettong
 * @package com.jettech.jettong.testm.config
 * @className HierarchyConfig
 * @date 2025-08-25
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class HierarchyConfig {

    /**
     * 层级定义列表
     */
    private List<LevelConfig> hierarchy;

    /**
     * 脑图布局配置
     */
    private String layout;

    /**
     * 脑图主题配置（JSON对象）
     */
    private JSONObject theme;
    
    /**
     * 层级配置
     */
    @Data
    public static class LevelConfig {
        /**
         * 层级编号
         */
        private Integer level;
        
        /**
         * 层级类型：root-根节点, system-系统, trade-交易, testpoint-测试点, attribute-属性, value-值
         */
        private String type;
        
        /**
         * 显示名称模板，支持占位符
         */
        private String nameTemplate;
        
        /**
         * 数据源字段（对于属性和值层级）
         */
        private String dataField;
        
        /**
         * 是否启用
         */
        private Boolean enabled;
        
        /**
         * 排序
         */
        private Integer sort;

        /**
         * 节点样式
         */
        private JSONObject style;
    }
}
