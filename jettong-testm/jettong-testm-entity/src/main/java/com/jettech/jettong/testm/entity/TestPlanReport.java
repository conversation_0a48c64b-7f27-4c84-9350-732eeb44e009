package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanReport
 * @date 2022-03-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_report")
@ApiModel(value = "TestPlanReport", description = "")
@AllArgsConstructor
public class TestPlanReport extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 报告id
     */
    @ApiModelProperty(value = "报告id")
    @NotNull(message = "请填写报告id")
    @TableField(value = "report_id")
    @Excel(name = "报告id")
    private Long reportId;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    @NotNull(message = "请填写产品ID")
    @TableField(value = "plan_id")
    @Excel(name = "产品ID")
    private Long planId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;


    @Builder
    public TestPlanReport(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    Long reportId, Long planId ,String name)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.reportId = reportId;
        this.planId = planId;
        this.name = name;
    }

}
