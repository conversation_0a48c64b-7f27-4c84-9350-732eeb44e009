package com.jettech.jettong.testm.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测试计划用例执行表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例执行表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanCaseResultUpdateDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseResultUpdateDTO", description = "测试计划用例执行表")
public class TestPlanCaseResultUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    @NotNull(message = "请填写计划标识")
    private Long planId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private Long testcaseId;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @NotEmpty(message = "请填写执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    private String status;
    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    @Size(max = 65535, message = "执行结果详情长度不能超过65,535")
    private String result;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime execTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;


    @ApiModelProperty(value = "预估执行时间")
    private Long truexecTime;
}
