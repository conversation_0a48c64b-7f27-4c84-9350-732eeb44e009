package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 测试任务用例执行表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例执行表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTaskCaseResult
 * @date 2025-08-13
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_task_case_result")
@ApiModel(value = "TestTaskCaseResult", description = "测试任务用例执行表")
@AllArgsConstructor
public class TestTaskCaseResult extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    @TableField(value = "`task_id`")
    @Excel(name = "任务标识")
    private Long taskId;

    /**
     * 用例历史id
     */
    @ApiModelProperty(value = "用例历史id")
    @NotNull(message = "请填写用例历史id")
    @TableField(value = "`history_id`")
    @Excel(name = "用例历史id")
    private Long historyId;

    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @TableField(value = "`testcase_id`")
    @Excel(name = "用例标识")
    private Long testcaseId;

    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    @Size(max = 128, message = "任务名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @NotEmpty(message = "请填写执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    @TableField(value = "`status`", condition = LIKE)
    @Excel(name = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    private String status;

    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    @Size(max = 65535, message = "执行结果详情长度不能超过65535")
    @TableField(value = "`result`", condition = LIKE)
    @Excel(name = "执行结果详情")
    private String result;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "`exec_by`")
    @Excel(name = "执行人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long execBy;

    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @TableField(value = "`exec_time`")
    @Excel(name = "执行时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime execTime;


    /**
     * 操作状态：create创建/update更新/execute执行/delete删除
     */
    @ApiModelProperty(value = "操作状态：create创建/update更新/execute执行/delete删除")
    @Size(max = 255, message = "操作状态：create创建/update更新/execute执行/delete删除长度不能超过255")
    @TableField(value = "`operate_type`", condition = LIKE)
    @Excel(name = "执行操作")
    private String operateType;

    /**
     * 用例实际执行时间
     */
    @ApiModelProperty(value = "预估执行时间")
    @TableField(value = "`truexec_time`")
    @Excel(name = "预估执行时间")
    private Long truexecTime;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Size(max = 255, message = "版本长度不能超过255")
    @TableField(value = "`version`", condition = LIKE)
    @Excel(name = "版本")
    private String version;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "`priority`")
    @Excel(name = "优先级")
    private Integer priority;


    @Builder
    public TestTaskCaseResult(
                    Long id, Long taskId, Long historyId, Long testcaseId, String name, 
                    String status, String result, Long execBy, LocalDateTime execTime, Long createdBy, LocalDateTime createTime, 
                    Long updatedBy, LocalDateTime updateTime, String operateType, Long truexecTime, String version, Integer priority)
    {
        this.id = id;
        this.taskId = taskId;
        this.historyId = historyId;
        this.testcaseId = testcaseId;
        this.name = name;
        this.status = status;
        this.result = result;
        this.execBy = execBy;
        this.execTime = execTime;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.operateType = operateType;
        this.truexecTime = truexecTime;
        this.version = version;
        this.priority = priority;
    }

}
