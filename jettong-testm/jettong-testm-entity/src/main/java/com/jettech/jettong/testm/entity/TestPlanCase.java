package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 测试计划用例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestPlanCase
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_plan_case")
@ApiModel(value = "TestPlanCase", description = "测试计划用例表")
@AllArgsConstructor
public class TestPlanCase extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    @NotNull(message = "请填写计划标识")
    @TableField(value = "`plan_id`")
    @Excel(name = "计划标识")
    private Long planId;

    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @TableField(value = "`testcase_id`")
    @Excel(name = "用例标识")
    private Long testcaseId;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "`priority`")
    @Excel(name = "优先级")
    private Integer priority;

    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态")
    @TableField(value = "`status`")
    @Excel(name = "执行结果状态")
    private String status;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @TableField(value = "`name`")
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "`version`")
    @Excel(name = "版本")
    private String version;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @TableField(value = "`exec_by`")
    @Excel(name = "执行人")
    private Long execBy;

    /**
     * 关联测试计划用例树id
     */
    @ApiModelProperty(value = "关联测试计划用例树id")
    @TableField(value = "`tree_id`")
    @Excel(name = "关联测试计划用例树id")
    private Long treeId;

    /**
     * 执行结束时间
     */
    @ApiModelProperty(value = "执行结束时间")
    @TableField(value = "`exec_etime`")
    @Excel(name = "执行结束时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime execEtime;

    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @TableField(value = "`case_key`")
    @Excel(name = "英文标识")
    private String caseKey;


    @Builder
    public TestPlanCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            Long planId, Long testcaseId, Integer priority, String status, String name, String version, Long execBy, Long treeId, LocalDateTime execEtime, String caseKey)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.planId = planId;
        this.testcaseId = testcaseId;
        this.priority = priority;
        this.status = status;
        this.name = name;
        this.version = version;
        this.execBy = execBy;
        this.treeId = treeId;
        this.execEtime = execEtime;
        this.caseKey = caseKey;
    }

}
