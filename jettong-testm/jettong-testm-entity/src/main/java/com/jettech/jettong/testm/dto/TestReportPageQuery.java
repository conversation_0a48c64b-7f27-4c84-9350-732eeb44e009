package com.jettech.jettong.testm.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestReport
 * @date 2021-11-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestReportPageQuery", description = "")
public class TestReportPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 测试计划Id
     */
    @ApiModelProperty(value = "测试任务Id")
    private Long taskId;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leaderBy;
    /**
     * 项目Id
     */
    @ApiModelProperty(value = "项目Id")
    private Long projectId;
    /**
     * 环境Id
     */
    @ApiModelProperty(value = "环境Id")
    private Long envId;
    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    private Long productVersion;
    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    private Long programId;
    /**
     * 计划执行进度
     */
    @ApiModelProperty(value = "计划执行进度")
    private Long rateProgress;
    /**
     * 成功用例个数
     */
    @ApiModelProperty(value = "成功用例个数")
    private Long systemNum;
    /**
     * 失败用例个数
     */
    @ApiModelProperty(value = "失败用例个数")
    private Long smokeNum;
    /**
     * 阻塞用例个数
     */
    @ApiModelProperty(value = "阻塞用例个数")
    private Long blockingNum;
    /**
     * 跳过用例个数
     */
    @ApiModelProperty(value = "跳过用例个数")
    private Long skipNum;
    /**
     * 未执行用例个数
     */
    @ApiModelProperty(value = "未执行用例个数")
    private Long undoNum;
    /**
     * 关联产品Id
     */
    @ApiModelProperty(value = "关联产品Id")
    private Long productId;
    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    private Boolean delay;
    /**
     * 测试报告名称
     */
    @ApiModelProperty(value = "测试报告名称")
    private String testReportName;
    /**
     * 是否是项目测试报告
     */
    @ApiModelProperty(value = "是否是项目测试报告")
    private Boolean product;
    /**
     * 报表执行结果
     */
    @ApiModelProperty(value = "报表执行结果")
    private String reportResult;

    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    private Boolean state;

}
