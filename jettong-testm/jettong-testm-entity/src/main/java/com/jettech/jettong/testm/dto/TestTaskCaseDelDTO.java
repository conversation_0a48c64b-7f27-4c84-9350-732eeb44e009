package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 测试案例库表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例库表删除实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestCaseLibraryUpdateDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseDelDTO", description = "测试案例库表")
public class TestTaskCaseDelDTO implements Serializable
{

    @ApiModelProperty(value = "测试任务Id")
    @NotNull(message = "请填写测试任务Id")
    private Long taskId;

    @ApiModelProperty(value = "用例Id列表")
    private List<Long> testCaseIds;
}
