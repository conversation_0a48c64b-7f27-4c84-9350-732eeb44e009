package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanReport
 * @date 2022-03-29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanReportPageQuery", description = "")
public class TestPlanReportPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 报告id
     */
    @ApiModelProperty(value = "报告id")
    private Long reportId;
    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private Long planId;

}
