package com.jettech.jettong.testm.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 测试分析功能要点表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表实体类
 * @projectName vone
 * @package com.jettech.jettong.testm.entity
 * @className TestRequirementFunctionPoints
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TestRequirementFunctionPoints", description = "测试分析功能要点表")
public class TestRequirementFunctionPointsExcelVo implements Serializable, IExcelModel, IExcelDataModel
{



    private String errorMsg;

    private Integer rowNum;

    @Excel(name = "系统",width = 20,dict = "issueTestReqId")
    private String issueTestReq;

    /**
     * 功能（交易）id
     */
    @Excel(name = "功能（交易）id",width = 20,dict = "functionId")
    private String function;

    private Long issueTestReqId;

    /**
     * 功能（交易）id
     */
    private Long functionId;



    @Excel(name = "功能点",width = 20)
    private String functionPoint;

    /**
     * 测试要点
     */
    @Excel(name = "测试要点",width = 20)
    private String testPoints;

    /**
     * 规则类型
     */
    @Excel(name = "规则类型",width = 20)
    private String ruleType;

    /**
     * 是否涉账
     */
    @Excel(name = "是否涉账", replace = {"是_true", "否_false", "_null"},width = 20)
    private String account;

    /**
     * 是否设计批处理
     */
    @Excel(name = "是否设计批处理", replace = {"是_true", "否_false", "_null"},width = 20)
    private String batch;
    /**
     * 是否涉账
     */
    private Boolean involveAccount;

    /**
     * 是否设计批处理
     */
    private Boolean involveBatch;

    /**
     * 优先级
     */
    @Excel(name = "优先级",width = 20)
    private String priority;

    /**
     * 规则来源
     */
    @Excel(name = "规则来源",width = 20)
    private String ruleSource;

    /**
     * 备注
     */
    @Excel(name = "备注",width = 20)
    private String remark;
    /**
     * 获取行号
     *
     * @return Integer
     */
    @Override
    public Integer getRowNum()
    {
        return rowNum;
    }

    /**
     * 设置行号
     *
     * @param rowNum 行号
     */
    @Override
    public void setRowNum(Integer rowNum)
    {
        this.rowNum = rowNum;
    }

    /**
     * 获取错误数据
     *
     * @return String
     */
    @Override
    public String getErrorMsg()
    {
        return errorMsg;
    }

    /**
     * 设置错误信息
     *
     * @param errorMsg 错误信息
     */
    @Override
    public void setErrorMsg(String errorMsg)
    {
        this.errorMsg = errorMsg;
    }

}
