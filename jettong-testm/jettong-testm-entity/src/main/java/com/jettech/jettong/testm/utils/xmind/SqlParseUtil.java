package com.jettech.jettong.testm.utils.xmind;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName vone-yq
 * @package com.cb.vone.common.util.sqlParse
 * @className SqlParseUtil
 * @date 2023/2/1 15:09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class SqlParseUtil
{

    //sqlList :[{"key":"","value":"","operator":"=、>、<、>=、<=、like","sort":0,"relation":"or/and","level":1}]
    public static String parseToString(Object obj, Map<String, Object> extra){
        List<Map<String,Object>> sqlList = (List<Map<String, Object>>) extra.get("sql");
        if(sqlList == null){
            return "";
        }
        //获取表名
        TableName table = obj.getClass().getAnnotation(TableName.class);
        String tableName = table.value();
        String sql = "";

        //判断是否执行
        Boolean isExecute =true;
        //判断属性参数是否为空；
        Boolean isNull =false;
        String relation ="and";

        for(int i=0; i<sqlList.size(); i++){
            Map<String,Object> param = sqlList.get(i);
            String key = (String) param.get("key");//属性值
            if(StringUtils.isBlank(key)){
                isNull =true;
                continue;
            }
            String val = (String) param.get("value");
            String valStr = val.replaceAll("'", "");
            if(valStr.trim().isEmpty()){
                isNull =true;
                break;
            }
            String operator = (String) param.get("operator");
            Integer sort = (Integer) param.get("sort");
            relation = (String) param.get("relation");
            Integer level = (Integer) param.get("level");
            //根据属性值获取column
            String column = null;
            Map<String,String> columnMap = getColumnForField(obj, key);
            if(columnMap != null){
                Set<String> keySet = columnMap.keySet();
                //添加布尔类型字段判断，添加人员机构字段判断
                for(String tableColumn : keySet){
                    column = tableColumn;
                    if(column.equalsIgnoreCase("updated_by") || column.equalsIgnoreCase("created_by")){
                        if(operator.equals("like")){
                            operator = "=";
                        }
                        if(operator.equals("not like")){
                            operator = "!=";
                        }
                        break;
                    }
                    String typeName = columnMap.get(tableColumn);
                    if(typeName.contains("Boolean") || typeName.contains("com.cb.vone.base.entity.rbac.user.User")
                            || typeName.contains("com.cb.vone.base.entity.rbac.org.Org")){
                        if(operator.equals("like")){
                            operator = "=";
                        }
                        if(operator.equals("not like")){
                            operator = "!=";
                        }
                    }
                }
            }

            column = tableName+"."+column;
            if("or".equals(relation) && isExecute){
                sql += " and ( ";
                //如果是or查询，只需要执行一次就可以 and (条件 or  条件 or .......)
                isExecute =false;
            }else{
                sql += " "+relation+" ";
                isNull =false;
            }

            if(val.contains(",")){
                if(operator.equals("like") || operator.equals("=")){
                    sql += column +" in ("+val+")";
                }
                if(operator.equals("not like") || operator.equals("!=")){
                    sql += column +" not in ("+val+")";
                }
            } else {
                switch (operator){
                    case "like":
                        sql += column +" like '%"+val+"%'";
                        break;
                    case "not like":
                        sql += column +" not like '%"+val+"%'";
                        break;
                    case "start with":
                        sql += column +" like '"+val+"%'";
                        break;
                    case "end with":
                        sql += column +" like '%"+val+"'";
                        break;
                    default:
                        if(operator.equals("=") && column.toLowerCase().contains("time")
                                && !column.contains("cyclic_time")){
                            val = val.replaceAll("'","");
                            sql += column +" like '%"+val+"%'";
                        } else {
                            sql += column +" "+operator +" "+val;
                        }

                        break;
                }
            }
            //如果是or最后需要拼接）
            if("or".equals(relation) && i==sqlList.size()-1){
                sql += ")";
            }

        }
        //处理[{"relation":"or","key":"swcName","operator":"like","value":"SC"},{"relation":"or","key":"swcName","operator":"like","value":""}]这种情况
        if("or".equals(relation) && isNull)
        {
            sql += ")";
        }
        //非法字段判断
        if(sql.toLowerCase().contains("drop") || sql.toLowerCase().contains("delete")
                || sql.toLowerCase().contains("update") || sql.toLowerCase().contains("alter")){
            String checkSql = sql.replaceAll("UPDATEONCHANGE","")
                    .replaceAll("updateContent","")
                    .replaceAll("update_code","");
            if(checkSql.toLowerCase().contains("update")){
                throw BizException.validFail("自定义筛选中包含非法字段!");
            }
        }
        return sql;
    }

    private static Map<String,String> getColumnForField(Object obj,String key){
        Map<String,String> map = new HashMap<>(4);
        //本身和父类的公共字段
        Field[] fields = obj.getClass().getFields();
        for (Field field:fields)
        {
            String name = field.getName();
            String name1= "";
            if(name.contains("_")){
                name1 = name.replace("_","");
            }
            if(name1.equalsIgnoreCase(key)){
                Class<?> type = field.getType();
                String typeName = type.getName();
                map.put(name,typeName);
                return map;
            }
        }
        //类本身字段
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        for (Field field:declaredFields)
        {
            if(field.getName().equals(key)){
                TableField tableField = field.getAnnotation(TableField.class);
                Class<?> type = field.getType();
                String typeName = type.getName();
                Echo echo = field.getAnnotation(Echo.class);
                if(echo != null){//布尔类型echo为空
                    Class<?> aClass = echo.beanClass();
                    typeName = aClass.toString();
                }
                map.put(tableField.value(),typeName);
                return map;
            }

        }

        return null;
    }

    //sqlList :[{"key":"","value":"","operator":"=、>、<、>=、<=、like","sort":0,"relation":"or/and","level":1}]
    public static String parseSearch(String table, List<Map<String, Object>> list)
    {
        String tableColumn = "milestone_config_id";
        if(table.equals("vehicles")){
            tableColumn = "vehicle_config_id";
        }else if(table.equals("customs")){
            tableColumn = "checklist_config_id";
        }
        String searchSql = "";
        List<Map<String, Object>> orList = new ArrayList<>();
        List<Map<String, Object>> andList = new ArrayList<>();
        for(int i=0; i<list.size(); i++){
            Map<String, Object> map = list.get(i);
            String relation = (String) map.get("relation");
            if(relation.equals("or")){
                orList.add(map);
            }else {
                andList.add(map);
            }
        }
        String orSql = "";
        if(orList.size() != 0){
            orSql = parseOr(tableColumn,orList);
        }
        String andSql = "";
        if(andList.size() != 0){
            andSql = parseAnd(tableColumn,andList);
        }
        if(!orSql.equals("")){
            searchSql = " or ("+orSql+")"+andSql;
        } else {
            searchSql = andSql;
        }
        //非法字段判断
        if(searchSql.toLowerCase().contains("drop") || searchSql.toLowerCase().contains("delete")
                || searchSql.toLowerCase().contains("update") || searchSql.toLowerCase().contains("alter")){
            throw BizException.validFail("自定义筛选中包含非法字段!");
        }
        return searchSql;
    }

    private static String parseAnd(String tableColumn, List<Map<String, Object>> andList)
    {
        String sql ="";

        for(int i=0; i<andList.size(); i++)
        {
            Map<String, Object> map = andList.get(i);
            String key = (String) map.get("key");//表头字段id
            if(StringUtils.isBlank(key)){
                continue;
            }
            String val = (String) map.get("value");
            String valStr = val.replaceAll("'", "");
            if (valStr.trim().isEmpty())
            {
                break;
            }
            String operator = (String) map.get("operator");
            if(tableColumn.contains("vehicle")){
                sql += "and function_id in (select function_id from am_project_function_vehicle where ";
            }else if(tableColumn.contains("milestone")){
                sql += "and function_id in (select function_id from am_project_function_milestone where ";
            }else {
                sql += "and checklist_id in (select checklist_id from am_project_deployment_checklist_custom where ";
            }
            sql += tableColumn+" = "+key+ " and value ";
            if(val.contains(",")){
                if(operator.equals("like") || operator.equals("=")){
                    sql += " in ("+val+"))";
                }
                if(operator.equals("not like") || operator.equals("!=")){
                    sql += " not in ("+val+"))";
                }
            } else {
                switch (operator){
                    case "like":
                        sql += " like '%"+val+"%')";
                        break;
                    case "not like":
                        sql += " not like '%"+val+"%')";
                        break;
                    default:
                        sql += " "+operator +" "+val+")";
                        break;
                }

            }

        }
        return sql;
    }

    private static String parseOr(String tableColumn, List<Map<String, Object>> orList)
    {
        String sql = "";
        for(int i=0; i<orList.size(); i++){
            Map<String, Object> map = orList.get(i);
            String key = (String) map.get("key");//表头字段id
            if(StringUtils.isBlank(key)){
                continue;
            }
            String val = (String) map.get("value");
            String valStr = val.replaceAll("'", "");
            if(valStr.trim().isEmpty()){
                break;
            }
            String operator = (String) map.get("operator");

            sql += "or ( "+tableColumn+" = "+ key+" and value ";
            if(val.contains(",")){
                if(operator.equals("like") || operator.equals("=")){
                    sql += " in ("+val+"))";
                }
                if(operator.equals("not like") || operator.equals("!=")){
                    sql += " not in ("+val+"))";
                }
            } else {
                switch (operator){
                    case "like":
                        sql += " like '%"+val+"%')";
                        break;
                    case "not like":
                        sql += " not like '%"+val+"%')";
                        break;
                    default:
                        sql += " "+operator +" "+val+")";
                        break;
                }

            }

        }
        if(sql.contains("or")){
            sql = sql.substring(2);
        }
        return sql;
    }

    /**
     * 将oldColumn 中的字段筛选出来，替换成newColumn中的字段，
     * 并将取出的自定义筛选字段存放在新的map中
     * @param oldColumn
     * @param newColumn
     * @param extra
     * @return {@link Map< String, Object>}
     * @throws
     * <AUTHOR>
     * @date 2023/4/12 17:39
     */
    public static Map<String, Object> checkColumn(String oldColumn, String newColumn, Map<String, Object> extra){
        List<Map<String,Object>> sqlList = (List<Map<String, Object>>) extra.get("sql");
        String[] oldColumns = oldColumn.split(",");
        String[] newColumns = newColumn.split(",");
        List<Map<String,Object>> list =  new ArrayList<>();
        for(int j=0; j<oldColumns.length; j++){
            String column = oldColumns[j];
            for(int i=sqlList.size()-1; i>=0; i--)
            {
                Map<String, Object> param = sqlList.get(i);
                String key = (String) param.get("key");//属性值
                if(column.equals(key)){
                    param.put("key",newColumns[j]);
                    list.add(param);
                    sqlList.remove(i);
                }
            }
        }
        if(list.size() != 0){
            extra.put("newSql",list);
        }
        extra.put("sql",sqlList);
        return extra;
    }
}
