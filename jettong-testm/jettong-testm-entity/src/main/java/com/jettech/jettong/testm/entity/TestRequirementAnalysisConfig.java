package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试分析配置表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析配置表实体类
 * @projectName vone
 * @package com.jettech.jettong.testm.entity
 * @className TestRequirementAnalysisConfig
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_requirement_analysis_config")
@ApiModel(value = "TestRequirementAnalysisConfig", description = "测试分析配置表")
public class TestRequirementAnalysisConfig extends Entity<Long> implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @NotNull(message = "请填写id")
    @TableId(value = "id", type = IdType.INPUT)
    @Excel(name = "id")
    private Long id;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Size(max = 255, message = "标识长度不能超过255")
    @TableField(value = "code", condition = LIKE)
    @Excel(name = "标识")
    private String code;

    /**
     * 是否启用（同时只有一个配置启用）
     */
    @ApiModelProperty(value = "是否启用（同时只有一个配置启用）")
    @NotNull(message = "请填写是否启用（同时只有一个配置启用）")
    @TableField(value = "enable")
    @Excel(name = "是否启用（同时只有一个配置启用）", replace = {"是_true", "否_false", "_null"})
    private Boolean enable;

    /**
     * 字段配置
     */
    @ApiModelProperty(value = "字段配置")
    @TableField(value = "field_config", condition = LIKE)
    @Excel(name = "字段配置")
    private String fieldConfig;

    /**
     * 脑图层级配置
     */
    @ApiModelProperty(value = "脑图层级配置")
    @TableField(value = "hierarchy_config", condition = LIKE)
    @Excel(name = "脑图层级配置")
    private String hierarchyConfig;

    /**
     * 视图配置（数据列展示）
     */
    @ApiModelProperty(value = "视图配置（数据列展示）")
    @TableField(value = "view_config", condition = LIKE)
    @Excel(name = "视图配置（数据列展示）")
    private String viewConfig;

    /**
     * 测试要点分割配置
     */
    @ApiModelProperty(value = "测试要点分割配置")
    @TableField(value = "testpoint_parsing_config", condition = LIKE)
    @Excel(name = "测试要点分割配置")
    private String testpointParsingConfig;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(value = "created_by")
    @Excel(name = "创建人")
    private Long createdBy;





    @Builder
    public TestRequirementAnalysisConfig(
                    Long id, String code, Boolean enable, String fieldConfig, String hierarchyConfig, 
                    String viewConfig, String testpointParsingConfig, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime)
    {
        this.id = id;
        this.code = code;
        this.enable = enable;
        this.fieldConfig = fieldConfig;
        this.hierarchyConfig = hierarchyConfig;
        this.viewConfig = viewConfig;
        this.testpointParsingConfig = testpointParsingConfig;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
    }

}
