package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作台需求概览组件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台用例概览组件
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.issue.vo
 * @className RequirementTypeComponentResult
 * @date 2021/12/2 20:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCseTypeComponentResult", description = "工作台用例概览组件")
public class TestReportTabComponent implements Serializable
{
    /**
     * 标签名称
     */
    private String tabName;
    /**
     * 成功
     */
    @ApiModelProperty(value = "成功")
    private Long system = 0L;
    /**
     * 失败
     */
    @ApiModelProperty(value = "失败")
    private Long smoke = 0L;
    /**
     * 阻塞
     */
    @ApiModelProperty(value = "阻塞")
    private Long blocking = 0L;
    /**
     * 跳过
     */
    @ApiModelProperty(value = "跳过")
    private Long skip = 0L;
    /**
     * 未开始
     */
    @ApiModelProperty(value = "未开始")
    private Long noStart = 0L;
    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    private Integer rate = 0;
}
