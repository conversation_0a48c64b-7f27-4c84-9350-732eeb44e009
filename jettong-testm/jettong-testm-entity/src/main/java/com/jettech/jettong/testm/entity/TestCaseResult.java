package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 测试案例返回结果实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例返回结果实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestCase
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
//@TableName("test_case")
//@ApiModel(value = "TestCase", description = "测试案例表")
@AllArgsConstructor
public class TestCaseResult extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @Excel(name = "英文标识")
    private String caseKey;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @Excel(name = "测试意图")
    private String intent;

    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Excel(name = "测试步骤类型，文本/条目")
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Excel(name = "测试步骤")
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Excel(name = "预期结果")
    private String expectedResult;

    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @Excel(name = "评审状态")
    private Long stateId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @Excel(name = "关联产品")
    private Long productId;

    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    @Excel(name = "关联需求")
    private Long requirementId;

    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    @Excel(name = "关联案例树")
    private Long treeId;

    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    @Excel(name = "用例等级")
    private Integer priority;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Excel(name = "前提条件")
    private String prerequisite;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @Excel(name = "用例库")
    private Long libraryId;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @Excel(name = "执行人")
    private Long execBy;

    /**
     * 测试用例结果
     */
    @ApiModelProperty(value = "测试用例结果")
    @Excel(name = "测试用例结果")
    private String testCaseResult;

    @Builder
    public TestCaseResult(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String caseKey, String name, String intent, String stepType, String testStep,
            String expectedResult, Long stateId, Long productId, Long requirementId, Long treeId, Integer priority,
            Long leadingBy, String prerequisite, Long libraryId, Long execBy, String testCaseResult)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.caseKey = caseKey;
        this.name = name;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.stateId = stateId;
        this.productId = productId;
        this.requirementId = requirementId;
        this.treeId = treeId;
        this.priority = priority;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.libraryId = libraryId;
        this.execBy = execBy;
        this.testCaseResult = testCaseResult;
    }

}
