package com.jettech.jettong.testm.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测试计划用例执行表分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例执行表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanCaseResult
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseResultPageQuery", description = "测试计划用例执行表")
public class TestPlanCaseResultPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    private Long planId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    private Long testcaseId;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    private String status;
    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    private String result;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    private LocalDateTime execTime;

    @ApiModelProperty(value = "预估执行时间")
    private Long truexecTime;

}
