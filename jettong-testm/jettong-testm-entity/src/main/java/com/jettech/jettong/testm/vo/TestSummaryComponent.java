package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 工作台需求概览组件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台用例概览组件
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.issue.vo
 * @className RequirementTypeComponentResult
 * @date 2021/12/2 20:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCseTypeComponentResult", description = "工作台用例概览组件")
public class TestSummaryComponent implements Serializable
{
    /**
     * 总数
     */
    @ApiModelProperty(value = "总数")
    private Integer count;

    /**
     * 饼状图数据
     */
    @ApiModelProperty(value = "饼状图数据")
    private List<Pie> data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = false)
    @Builder
    @ApiModel(value = "Pie", description = "饼状图对象")
    public static class Pie implements Serializable
    {

        /**
         * 名称
         */
        @ApiModelProperty(value = "名称")
        private String name;

        /**
         * 名称
         */
        @ApiModelProperty(value = "颜色")
        private String color;

        /**
         * value值
         */
        @ApiModelProperty(value = "value值")
        private Integer value;
    }
}
