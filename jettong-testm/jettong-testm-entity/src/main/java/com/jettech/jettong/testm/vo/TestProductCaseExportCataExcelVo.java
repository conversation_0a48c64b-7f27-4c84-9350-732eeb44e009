package com.jettech.jettong.testm.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 测试案例Excel导出完整对象
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例Excel导出完整对象
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.alm.testm.vo
 * @className TestCaseExportExcelVO
 * @date 2022/01/17 15:15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCaseExportCataExcelVo", description = "案例库测试案例Excel导出完整对象")
public class TestProductCaseExportCataExcelVo
{

    @Excel(name = "目录")
    private String cata;

    @Excel(name = "用例标题")
    private String name;


//
//    @Excel(name = "版本")
//    private String version;
//
    @Excel(name = "优先级")
    private String priority;

    @Excel(name = "执行方式")
    private String runMond;

    @Excel(name = "测试意图")
    private String summary;

    @Excel(name = "前置条件")
    private String prerequisite;

    @Excel(name = "步骤动作")
    private String testStep;

    @Excel(name = "预期结果")
    private String expectedResult;

}
