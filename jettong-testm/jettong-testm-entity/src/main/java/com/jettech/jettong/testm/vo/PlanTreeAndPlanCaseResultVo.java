package com.jettech.jettong.testm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/1/19 9:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "PlanTreeAndPlanCaseResultVo", description = "树级下的用例状态个数")
public class PlanTreeAndPlanCaseResultVo extends TreeEntity<PlanTreeAndPlanCaseResultVo, Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    private Long id;

    private String name;

    private String status;

    private Long parentId;

    private String version;

    private String type;
    //总计数
    private Long count;
    //成功
    private Long system;
    //阻塞
    private Long block;
    //未执行
    private Long undo;
    //跳过
    private Long skip;
    //失败
    private Long smoke;
    //优先级
    private int priority;
    //用例编号
    private String caseKey;
}