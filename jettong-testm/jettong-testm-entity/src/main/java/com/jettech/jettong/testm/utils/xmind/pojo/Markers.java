package com.jettech.jettong.testm.utils.xmind.pojo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 样式格式
 * @projectName xmindparser-java-master
 * @package org.liufree.xmindparser.pojo
 * @className Markers
 * @date 2022/3/17 0017 10:33
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class Markers {
    private String markerId;
}
