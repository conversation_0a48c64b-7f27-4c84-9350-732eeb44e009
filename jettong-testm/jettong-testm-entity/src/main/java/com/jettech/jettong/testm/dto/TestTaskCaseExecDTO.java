package com.jettech.jettong.testm.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTaskCaseExecDTO", description = "测试计划用例执行对象")
public class TestTaskCaseExecDTO {
    /**
     * 计划标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    @Excel(name = "任务标识")
    private Long taskId;
    /**
     * 执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @NotNull(message = "请填写执行结果状态")
    @Excel(name = "执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    private String status;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @Excel(name = "用例标识")
    private Long caseId;
    /**
     * 执行结果详情
     */
    @ApiModelProperty(value = "执行结果详情")
    @NotNull(message = "请填写执行结果详情")
    @Excel(name = "执行结果详情")
    private String result;
    /**
     * 计划标识字符串类型
     */
    @ApiModelProperty(value = "任务标识字符串类型")
    @Excel(name = "任务标识字符串类型")
    private String taskIdString;

    @ApiModelProperty(value = "测试步骤类型，文本(text)/条目(subclause)")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "step_type", condition = LIKE)
    @Excel(name = "测试步骤类型，文本/条目")
    private String stepType;

    @ApiModelProperty(value = "版本")
    @NotNull(message = "请填写版本")
    private String version;

    @ApiModelProperty(value = "预估执行时间")
    @Excel(name = "预估执行时间")
    private Long truexecTime;

    @ApiModelProperty(value = "最新一次执行时间")
    @TableField(exist = false)
    private LocalDateTime latestTime;

    @ApiModelProperty(value = "优先级")
    @TableField(exist = false)
    private int priority;

    @ApiModelProperty(value = "执行用户名称")
    @TableField(exist = false)
    private String execName;

    @ApiModelProperty(value = "执行用户头像")
    @TableField(exist = false)
    private String execAvatarPath;
}
