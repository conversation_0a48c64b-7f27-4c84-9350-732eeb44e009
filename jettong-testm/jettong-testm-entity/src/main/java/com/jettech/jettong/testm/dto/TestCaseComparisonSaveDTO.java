package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 测试案例对照表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例对照表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestCaseComparisonSaveDTO
 * @date 2022-04-07
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestCaseComparisonSaveDTO", description = "测试案例对照表")
public class TestCaseComparisonSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 英文标识
     */
    @ApiModelProperty(value = "英文标识")
    @NotEmpty(message = "请填写英文标识")
    @Size(max = 20, message = "英文标识长度不能超过20")
    private String caseKey;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @Size(max = 255, message = "测试意图长度不能超过255")
    private String intent;
    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    private String stepType;
    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65,535")
    private String testStep;
    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65,535")
    private String expectedResult;
    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    private Long stateId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private Long requirementId;
    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    private Long treeId;
    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    private Integer priority;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65,535")
    private String prerequisite;
    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    private Long libraryId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Size(max = 20, message = "版本号长度不能超过20")
    private String version;
    /**
     * 保存状态 0 重复数据都不选中，1新数据选中，2旧数据选中
     */
    @ApiModelProperty(value = "保存状态 0 重复数据都不选中，1新数据选中，2旧数据选中")
    private Integer saveState;
    /**
     * 0 不重复，1重复
     */
    @ApiModelProperty(value = "0 不重复，1重复")
    private Integer repeatState;

}
