package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.jettech.basic.base.entity.Entity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import com.jettech.basic.model.EchoVO;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试任务用例表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTaskCase
 * @date 2025-08-12
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_task_case")
@ApiModel(value = "TestTaskCase", description = "测试任务用例表")
@AllArgsConstructor
public class TestTaskCase extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    @TableField(value = "`task_id`")
    @Excel(name = "任务标识")
    private Long taskId;

    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @TableField(value = "`testcase_id`")
    @Excel(name = "用例标识")
    private Long testcaseId;

    /**
     * 冗余字段:优先级
     */
    @ApiModelProperty(value = "冗余字段:优先级")
    @TableField(value = "`priority`")
    @Excel(name = "优先级")
    private Integer priority;

    /**
     * 冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    @TableField(value = "`status`", condition = LIKE)
    @Excel(name = "执行结果状态")
    private String status;

    /**
     * 冗余字段:名称/标题
     */
    @ApiModelProperty(value = "冗余字段:名称/标题")
    @Size(max = 500, message = "冗余字段:名称/标题长度不能超过500")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 冗余字段:版本号
     */
    @ApiModelProperty(value = "冗余字段:版本号")
    @Size(max = 20, message = "冗余字段:版本号长度不能超过20")
    @TableField(value = "`version`", condition = LIKE)
    @Excel(name = "版本")
    private String version;

    /**
     * 冗余字段:执行人
     */
    @ApiModelProperty(value = "冗余字段:执行人")
    @TableField(value = "`exec_by`")
    @Excel(name = "执行人")
    private Long execBy;

    /**
     * 冗余字段:用例树id
     */
    @ApiModelProperty(value = "冗余字段:关联测试计划用例树id")
    @TableField(value = "`tree_id`")
    @Excel(name = "关联测试计划用例树id")
    private Long treeId;

    /**
     * 冗余字段:执行结束时间
     */
    @ApiModelProperty(value = "冗余字段:执行结束时间")
    @TableField(value = "`exec_etime`")
    @Excel(name = "执行结束时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime execEtime;

    /**
     * 冗余字段:英文标识
     */
    @ApiModelProperty(value = "冗余字段:英文标识")
    @Size(max = 20, message = "冗余字段:英文标识长度不能超过20")
    @TableField(value = "`case_key`", condition = LIKE)
    @Excel(name = "英文标识")
    private String caseKey;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "testreq_id")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "project_id")
    private Long projectId;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    @TableField(value = "module_function_id")
    private Long moduleFunctionId;


    @Builder
    public TestTaskCase(
                    Long id, Long taskId, Long testcaseId, Long createdBy, LocalDateTime createTime, 
                    LocalDateTime updateTime, Long updatedBy, Integer priority, String status, String name, String version, 
                    Long execBy, Long treeId, LocalDateTime execEtime, String caseKey, Long testreqId, Long projectId,
                    Long moduleFunctionId)
    {
        this.id = id;
        this.taskId = taskId;
        this.testcaseId = testcaseId;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.priority = priority;
        this.status = status;
        this.name = name;
        this.version = version;
        this.execBy = execBy;
        this.treeId = treeId;
        this.execEtime = execEtime;
        this.caseKey = caseKey;
        this.testreqId = testreqId;
        this.projectId = projectId;
        this.moduleFunctionId = moduleFunctionId;
    }

}
