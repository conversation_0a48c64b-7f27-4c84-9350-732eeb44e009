package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 测试标签与案例关联实体类
 *
 * <AUTHOR>
 * @version 1.2
 * @description 测试标签与案例关联实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestTabCasePageQuery
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestTabCasePageQuery", description = "测试标签与案例关联表")
public class TestTabCasePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 测试案例ID
     */
    @ApiModelProperty(value = "测试案例ID")
    private Long testCaseId;

    /**
     * 测试标签ID
     */
    @ApiModelProperty(value = "测试标签ID")
    private Long testTabId;
}
