package com.jettech.jettong.testm.dto;

import com.jettech.jettong.testm.vo.CaseAndTreeReturnVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * 测试计划用例表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanCaseSaveDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseSaveDTO", description = "测试计划用例表")
public class TestPlanCaseSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 计划标识
     */
    @ApiModelProperty(value = "计划标识")
    private Long planId;
    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    private List<Long> testcaseIds;
    /**
     * 叶级全选用例树级Id
     */
    @ApiModelProperty(value = "用例标识")
    private List<Long> testcaseTreeIds;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @NotNull(message = "请填写创建人")
    private Long createdBy;

    /**
     * 关联用例使用的字段 key = id, value = treeId
     */
    @ApiModelProperty(value = "用例标识")
    private HashMap<Long, Long> map;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;

    @ApiModelProperty(value = "当前选择用例所关联的树id")
    private List<Long> connectTreeId;

    @ApiModelProperty(value = "用例执行状态")
    private String status;

    @ApiModelProperty(value = "用例执行状态")
    private List<CaseAndTreeReturnVo> CaseAndTreeReturnVo;
}
