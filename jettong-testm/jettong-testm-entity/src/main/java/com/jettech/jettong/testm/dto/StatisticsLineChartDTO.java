package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 统计中使用的折线图 DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @description 统计中使用的折线图 DTO
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.dto
 * @className lineChart
 * @date 2022/4/8 16:10
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "StatisticsLineChartDTO", description = "")
public class StatisticsLineChartDTO {
    private List<String> key;
    private List<Long> num;
}
