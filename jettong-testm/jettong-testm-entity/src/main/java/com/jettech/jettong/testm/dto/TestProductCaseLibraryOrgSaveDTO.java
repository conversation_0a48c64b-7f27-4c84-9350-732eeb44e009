package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestProductCaseLibraryOrgSaveDTO
 * @date 2022-03-17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCaseLibraryOrgSaveDTO", description = "")
public class TestProductCaseLibraryOrgSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @NotNull(message = "请填写产品用例库id")
    private Long testProductCaseLibraryId;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @NotNull(message = "请填写机构id")
    private Long orgId;

}
