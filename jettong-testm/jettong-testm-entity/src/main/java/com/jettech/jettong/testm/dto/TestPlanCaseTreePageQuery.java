package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 测试计划关联用例树表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划关联用例树表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanCaseTree
 * @date 2022-03-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanCaseTreePageQuery", description = "测试计划关联用例树表")
public class TestPlanCaseTreePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 测试计划Id
     */
    @ApiModelProperty(value = "测试计划Id")
    private Long planId;
    /**
     * 树id
     */
    @ApiModelProperty(value = "树id")
    private Long treeId;
    /**
     * 测试用例id
     */
    @ApiModelProperty(value = "测试用例id")
    private Long caseId;
    /**
     * 维护人
     */
    @ApiModelProperty(value = "维护人")
    private Long managingBy;

}
