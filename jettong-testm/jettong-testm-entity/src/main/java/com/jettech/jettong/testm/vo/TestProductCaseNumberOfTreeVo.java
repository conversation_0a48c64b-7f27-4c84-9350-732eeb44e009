package com.jettech.jettong.testm.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/1/19 9:57
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCaseNumberOfTreeVo", description = "树级下的用例个数")
public class TestProductCaseNumberOfTreeVo
{

    private Long treeId;

    private Long count;

    private Long mark;

    //执行结果状态数量
    private String statusCountNum;

    private String status;
}