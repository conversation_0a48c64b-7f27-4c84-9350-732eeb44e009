package com.jettech.jettong.testm.dto;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;

/**
 * 测试分析功能要点表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表分页实体类
 * @projectName vone
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestRequirementFunctionPoints
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestRequirementFunctionPointsPageQuery", description = "测试分析功能要点表")
public class TestRequirementFunctionPointsPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    private Long id;
    /**
     * ID
     */
    @ApiModelProperty(value = "projectId")
    private Long projectId;
    /**
     * ID
     */
    @ApiModelProperty(value = "taskId")
    private Long taskId;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    private String stateCode;
    /**
     * ID
     */
    @ApiModelProperty(value = "功能（交易）id,查询会查询子级")
    private Long functionAndModuleId;

    /**
     * 测试需求id
     */
    @ApiModelProperty(value = "测试需求id")
    private Long issueTestReqId;
    /**
     * 功能（交易）id
     */
    @ApiModelProperty(value = "功能（交易）id")
    private Long functionId;

    /**
     * 功能点
     */
    @ApiModelProperty(value = "功能点")
    private String functionPoint;
    /**
     * 测试要点
     */
    @ApiModelProperty(value = "测试要点")
    private String testPoints;
    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private String ruleType;
    /**
     * 是否涉账
     */
    @ApiModelProperty(value = "是否涉账")
    private Boolean involveAccount;
    /**
     * 是否设计批处理
     */
    @ApiModelProperty(value = "是否设计批处理")
    private Boolean involveBatch;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priority;
    /**
     * 规则来源
     */
    @ApiModelProperty(value = "规则来源")
    private String ruleSource;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
    /**
     * 扩展字段 1
     */
    @ApiModelProperty(value = "扩展字段 1")
    private String c1;
    /**
     * 扩展字段 2
     */
    @ApiModelProperty(value = "扩展字段 2")
    private String c2;
    /**
     * 扩展字段 3
     */
    @ApiModelProperty(value = "扩展字段 3")
    private String c3;
    /**
     * 扩展字段 4
     */
    @ApiModelProperty(value = "扩展字段 4")
    private String c4;
    /**
     * 扩展字段 5
     */
    @ApiModelProperty(value = "扩展字段 5")
    private String c5;

}
