package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 测试计划归档表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划归档表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanArchive
 * @date 2022-05-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanArchivePageQuery", description = "测试计划归档表")
public class TestPlanArchivePageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    private String planKey;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Long stateId;
    /**
     * 计划类型:system系统测试/smoke冒烟测试/regression回归测试
     */
    @ApiModelProperty(value = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    private String type;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;
    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime endTime;
    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    private Boolean delay;
    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    private Long rateProgress;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 环境Id
     */
    @ApiModelProperty(value = "环境Id")
    private String envId;
    /**
     * 产品Id
     */
    @ApiModelProperty(value = "产品Id")
    private String productId;
    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    private Long productVersion;
    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    private Long programId;
    /**
     * 迭代计划Id
     */
    @ApiModelProperty(value = "迭代计划Id")
    private String planId;
    /**
     * 备用查询Id字段
     */
    @ApiModelProperty(value = "备用查询Id字段")
    private String queryId;
    /**
     * 测试计划树id
     */
    @ApiModelProperty(value = "测试计划树id")
    private Long treeId;
    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    private Long libraryId;
    /**
     * 1-未开始;2-执行中;3-已完成
     */
    @ApiModelProperty(value = "1-未开始;2-执行中;3-已完成")
    private Integer execState;
    /**
     * 1-必须创建缺陷;0-非必须
     */
    @ApiModelProperty(value = "1-必须创建缺陷;0-非必须")
    private Boolean bug;

}
