package com.jettech.jettong.testm.dto;

import com.jettech.jettong.testm.vo.CaseAndTreeReturnVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 测试计划实例表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划实例表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestPlanSaveDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanSaveDTO", description = "测试计划实例表")
public class TestPlanSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    private Long parentId;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Size(max = 64, message = "标识长度不能超过64")
    private String planKey;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "名称长度不能超过255")
    private String description;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Long stateId;
    /**
     * 计划类型:system系统测试/smoke冒烟测试/regression回归测试
     */
    @ApiModelProperty(value = "计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @NotEmpty(message = "请填写计划类型:system系统测试/smoke冒烟测试/regression回归测试")
    @Size(max = 20, message = "计划类型:system系统测试/smoke冒烟测试/regression回归测试长度不能超过20")
    private String type;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;
    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;
    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime endTime;
    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @NotNull(message = "请填写是否延期")
    private Boolean delay;
    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @NotNull(message = "请填写进度0-100")
    private Long rateProgress;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 环境
     */
    @ApiModelProperty(value = "环境")
    private String envId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private String productId;
    /**
     * 关联产品版本
     */
    @ApiModelProperty(value = "关联产品版本")
    private Long productVersion;
    /**
     * 关联项目集
     */
    @ApiModelProperty(value = "关联项目集")
    private Long programId;
    /**
     * 迭代计划Id
     */
    @ApiModelProperty(value = "迭代计划Id")
    private Long planId;
    /**
     * 关联测试计划分组树id
     */
    @ApiModelProperty(value = "关联测试计划分组树id")
    private Long treeId;
    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    private Long libraryId;
    /**
     * 用例id
     */
    @ApiModelProperty(value = "用例id")
    private List<Long> caseId;

    @ApiModelProperty(value = "当前选择用例所关联的树id")
    private List<Long> connectTreeId;

    @ApiModelProperty(value = "用例执行状态")
    private List<CaseAndTreeReturnVo> CaseAndTreeReturnVo;
//    /**
//     * 叶级全选用例树级Id
//     */
//    @ApiModelProperty(value = "用例标识")
//    private List<Long> testcaseTreeIds;
//    /**
//     * 关联用例使用的字段 key = id, value = treeId
//     */
//    @ApiModelProperty(value = "用例标识")
//    private HashMap<Long, Long> map;

    /**
     * 是否必须创建缺陷
     */
    @ApiModelProperty(value = "缺陷")
    private Boolean bug;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private Long execBy;


    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    private Boolean state;




}
