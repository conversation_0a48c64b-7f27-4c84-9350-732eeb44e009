package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试标签实体类
 *
 * <AUTHOR>
 * @version 1.2
 * @description 测试标签实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTab
 * @date 2022-03-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_tab")
@ApiModel(value = "TestTab", description = "测试标签")
@AllArgsConstructor
public class TestTab extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 10, message = "名称/标题长度不能超过10")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父ID")
    @TableField(value = "`parent_id`")
    @Excel(name = "父ID")
    private Long parentId;

    /**
     * 等级
     */
    @ApiModelProperty(value = "等级")
    @TableField(value = "`level`")
    @Excel(name = "等级")
    private Integer level;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    @Excel(name = "排序")
    private Integer sort;

    @TableField(exist = false)
    private List<TestTab> subTabs;

    @Builder
    public TestTab(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
                   String name, Long parentId, Integer level, Integer sort)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.parentId = parentId;
        this.level = level;
        this.sort = sort;
    }

    public TestTab(Long id,String name, Long parentId)
    {
        this.id = id;
        this.name = name;
        this.parentId = parentId;
    }

    public TestTab(String name)
    {
        this.name = name;
    }
}
