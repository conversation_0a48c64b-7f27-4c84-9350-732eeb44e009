package com.jettech.jettong.testm.dto;

import com.jettech.jettong.testm.entity.TestPlan;
import com.jettech.jettong.testm.entity.TestPlanGroupTree;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 测试计划分组树及对应的测试计划
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划分组树及对应的测试计划
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestPlanGroupTreeAndPlanDTO
 * @date 2022-4-8
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestPlanGroupTreeAndPlanDTO", description = "测试计划分组树及对应的测试计划")
public class TestPlanGroupTreeAndPlanDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 测试计划分组树
     */
    @ApiModelProperty(value = "测试计划分组树")
    private List<TestPlanGroupTree> treeList;

    /**
     * 测试计划树对应的测试计划
     */
    @ApiModelProperty(value = "测试计划")
    private List<TestPlan> testPlanList;

}
