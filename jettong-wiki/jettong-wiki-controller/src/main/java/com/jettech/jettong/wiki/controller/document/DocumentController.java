package com.jettech.jettong.wiki.controller.document;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.wiki.document.dto.DocumentPageQuery;
import com.jettech.jettong.wiki.document.dto.DocumentSaveDTO;
import com.jettech.jettong.wiki.document.dto.DocumentUpdateDTO;
import com.jettech.jettong.wiki.document.entity.Document;
import com.jettech.jettong.wiki.service.document.DocumentService;
import com.jettech.jettong.wiki.service.space.SpaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;


/**
 * 知识库文档信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库文档信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.document
 * @className DocumentController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/document")
@Api(value = "Document", tags = "知识库文档信息")
@RequiredArgsConstructor
public class DocumentController extends SuperSimpleController<DocumentService, Document>
{

    private final EchoService echoService;

    private final SpaceService spaceService;

    //private final ReleaseVersionApi releaseVersionApi;

    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    public R<Document> handlerSave(@Validated @RequestBody DocumentSaveDTO model)
    {
        Document document = BeanPlusUtil.toBean(model, Document.class);
        baseService.saveDocument(document);

        return success(document);
    }

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeDocumentByIds(ids);

        return success();
    }

    @ApiOperation(value = "修改", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> handlerUpdate(@Validated @RequestBody DocumentUpdateDTO model)
    {
        Document document = BeanPlusUtil.toBean(model, Document.class);
        baseService.updateDocument(document);
        return success();
    }

    @ApiOperation(value = "分页列表查询，管理员使用")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<Document>> page(@Validated @RequestBody PageParams<DocumentPageQuery> params)
    {
        IPage<Document> page = params.buildPage(Document.class);
        DocumentPageQuery documentPage = params.getModel();

        baseService.page(page, Wraps.<Document>lbQ()
                .eq(Document::getCatalogId, documentPage.getCatalogId()));

        return success(page);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_PATH),
    })
    @ApiOperation(value = "单体查询", notes = "单体查询")
    @GetMapping("/{id}")
    @SysLog("'查询:' + #id")
    public R<Document> get(@PathVariable("id") Long id)
    {
        Document document = baseService.getById(id);
        echoService.action(document);
        return success(document);
    }

    @ApiOperation(value = "批量保存", notes = "批量保存")
    @PostMapping("/saveBatch")
    @SysLog(value = "批量保存", request = false, optType = OptLogTypeEnum.ADD)
    public R<Boolean> saveBatch(@RequestBody List<DocumentUpdateDTO> models)
    {
        baseService.saveBatch(models);
        return success();
    }

    @ApiOperation(value = "根据空间id查询空间下有权限的文档,树结构", notes = "根据空间id查询空间下有权限的文档,树结构")
    @GetMapping("/myDocumentBySpaceId/{spaceId}")
    @SysLog("根据空间id查询空间下有权限的文档")
    public R<List<Document>> myDocumentBySpaceId(@PathVariable Long spaceId)
    {
        // TODO 查询所有文档，暂不做权限
//        List<Document> documents = baseService.list(Wraps.<Document>lbQ()
//                .select(Document::getId, Document::getName, Document::getParentId, Document::getSpaceId,
//                        Document::getSort, Document::getCreateTime, Document::getCreatedBy,
//                        Document::getUpdateTime, Document::getUpdatedBy).eq(Document::getSpaceId, spaceId));
//        echoService.action(documents);
//        return success(TreeUtil.buildTree(documents));
        return null;
    }

    @ApiOperation(value = "根据空间类型id和业务id查询空间下有权限的文档,树结构", notes = "根据空间类型id和业务id查询空间下有权限的文档,树结构")
    @GetMapping("/myDocumentByTypeIdAndBizId/{typeId}/{bizId}")
    @SysLog("根据空间类型id和业务id查询空间下有权限的文档")
    public R<List<Document>> myDocumentByTypeIdAndBizId(@PathVariable Long typeId, @PathVariable Long bizId)
    {
//        Space space =
//                spaceService.getOne(Wraps.<Space>lbQ().eq(Space::getTypeId, typeId).eq(Space::getBizId, bizId), false);
//        if (space == null)
//        {
//            return success(Collections.emptyList());
//        }
//        // TODO 查询所有文档，暂不做权限
//        List<Document> documents = baseService.list(Wraps.<Document>lbQ()
//                .select(Document::getId, Document::getName, Document::getParentId, Document::getSpaceId,
//                        Document::getSort, Document::getCreateTime, Document::getCreatedBy,
//                        Document::getUpdateTime, Document::getUpdatedBy).eq(Document::getSpaceId, space.getId()));
//        echoService.action(documents);
//        return success(TreeUtil.buildTree(documents));
        return null;
    }

    @ApiOperation(value = "搜索有权限的空间和文档，普通用户使用")
    @PostMapping("/search")
    @SysLog(value = "'搜索有权限的空间和文档:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<Document>> search(@Validated @RequestBody PageParams<DocumentPageQuery> params)
    {
        // TODO 暂不做权限控制
//        IPage<Document> page = params.buildPage(Document.class);
//        DocumentPageQuery documentPage = params.getModel();
//
//        baseService.page(page, Wraps.<Document>lbQ().like(Document::getName, documentPage.getName())
//                .like(Document::getContent, documentPage.getContent())
//                .eq(Document::getSpaceId, documentPage.getSpaceId())
//                .eq(Document::getParentId, documentPage.getParentId()));
//
//        return success(page);
        return null;
    }

    /**
     * 条件查询产品,用于其他模块查询
     * 如果是发布工单-2或者发布版本-3，通过产品id查询文档，
     * 这块暂时去掉判断逻辑，不在通过产品查询
     * @return R
     * @throws
     * <AUTHOR>
     * @date 2022/07/14 10:24
     * @update lxr 2022/08/22 14:24
     * @since 1.0
     */
    @ApiOperation(value = "查询可以关联的文档信息,用于其他模块查询", notes = "查询可以关联的文档信息,用于其他模块查询")
    @PostMapping(value = "/getDocumentByRelationIdAndTypeAndFileId")
    @SysLog("查询可以关联的文档信息,用于其他模块查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R getPipelineListExcludePipelineIds(@RequestBody Map<String, Object> map)
    {
        // todo 此处不用
//        try
//        {
//            ReleaseWikiVo vo =null;
//            if(map.get("relationId")!=null){
//                vo =   releaseVersionApi.getFileIds(map);
//            }else {
//                return success(new ArrayList<>());
//            }
//            LbqWrapper<Document> documentLbqWrapper = Wraps.<Document>lbQ();
//            if(vo!=null && vo.getWikiIdList() !=null && vo.getWikiIdList().size()>0){
//                documentLbqWrapper.notIn(Document::getId, vo.getWikiIdList());
//            }
//            if(vo.getSourceType()==null)
//            {
//                return R.success(new ArrayList<>());
//            }
//            //如果是发布工单-2或者发布版本-3，通过产品id查询文档，去掉判断，不在通过产品查询
//            if(vo.getSourceType().equals("33") || vo.getSourceType().equals("23"))
//            {
//                if(vo.getProductId()!=null){
//                    List<Space> spaces = spaceService.list(Wraps.<Space>lbQ().eq(Space::getBizId, vo.getProductId()));
//                    if(!spaces.isEmpty())
//                    {
//                        List<Long> spacesIdList = spaces.stream().map(Space::getId).collect(Collectors.toList());
//                        documentLbqWrapper.in(Document::getSpaceId,spacesIdList);
//                    }
//                }else {
//                    return R.success();
//                }
//            }
//            else
//            {
//                if(map.get("spacesIdList")!=null)
//                {
//                   List<Long> spacesIdList= (List<Long>) map.get("spacesIdList");
//                    documentLbqWrapper.in(Document::getSpaceId,spacesIdList);
//                }
//
//            }
//            List<Document> list = baseService.list(documentLbqWrapper);
//            return success(list);
//        }
//        catch (Exception e)
//        {
//            log.error("查询可以关联的文档信息异常:{}", e.getMessage(), e);
//            return fail("查询可以关联的文档信息异常");
//        }
        return R.success(new ArrayList<>());
    }

    /**
     * 分页条件条件查询产品,用于其他模块查询
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/07/13 18:07
     * @update lxr 2022/07/13 18:07
     * @since 1.0
     */
    @ApiOperation(value = "分页条件查询关联的文档信息,用于其他模块查询", notes = "分页条件查询关联的文档信息,用于其他模块查询")
    @PostMapping(value = "/getDocumentPageByRelationIdAndTypeAndFileId")
    public IPage<Document> jobPage(@RequestBody PageParams<Map<String, Object>> params)
    {
        //todo 此处不用
//        IPage<Document> page = params.buildPage(Space.class);
//        Map<String, Object> map = params.getModel();
//        List<Long> fileIds=null;
//        if(map.get("relationId")!=null||map.get("sourceType")!=null){
//            ReleaseWikiVo vo=   releaseVersionApi.getFileIds( map);
//            fileIds=vo.getWikiIdList();
//            if(fileIds==null || fileIds.size()==0){
//                return page;
//            }
//        }
//        IPage<Document> productVersionIPage = baseService.page(page, Wraps.<Document>lbQ()
//                .in(Document::getId, fileIds));
//        echoService.action(page);
        return null;
    }
}
