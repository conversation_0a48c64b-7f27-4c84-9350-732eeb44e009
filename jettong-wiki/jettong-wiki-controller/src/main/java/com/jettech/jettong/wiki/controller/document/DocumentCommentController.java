package com.jettech.jettong.wiki.controller.document;

import cn.hutool.core.collection.CollUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.wiki.document.dto.DocumentCommentSaveDTO;
import com.jettech.jettong.wiki.document.dto.DocumentCommentUpdateDTO;
import com.jettech.jettong.wiki.document.entity.DocumentComment;
import com.jettech.jettong.wiki.service.document.DocumentCommentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 知识库文档评论信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库文档评论信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.document
 * @className DocumentCommentController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/documentComment")
@Api(value = "DocumentComment", tags = "知识库文档评论信息")
@RequiredArgsConstructor
public class DocumentCommentController extends SuperSimpleController<DocumentCommentService, DocumentComment>
{
    private final EchoService echoService;

    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Boolean> handlerSave(@Validated @RequestBody DocumentCommentSaveDTO model)
    {
        DocumentComment documentComment = BeanPlusUtil.toBean(model, DocumentComment.class);
        baseService.saveDocumentComment(documentComment);

        return success();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeDocumentCommentByIds(ids);

        return success();
    }

    @ApiOperation(value = "修改", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<Boolean> handlerUpdate(@Validated @RequestBody DocumentCommentUpdateDTO model)
    {
        DocumentComment documentComment = BeanPlusUtil.toBean(model, DocumentComment.class);
        baseService.updateDocumentComment(documentComment);
        return success();
    }

    @ApiOperation(value = "根据文档id查询文档评论信息", notes = "根据文档id查询文档评论信息")
    @GetMapping("findByDocumentId/{documentId}")
    @SysLog("根据文档id查询文档评论信息")
    public R<List<DocumentComment>> findByDocumentId(@PathVariable("documentId") Long documentId)
    {
        List<DocumentComment> documentComments =
                baseService.list(Wraps.<DocumentComment>lbQ().eq(DocumentComment::getDocumentId, documentId));
        echoService.action(documentComments);
        return success(buildTree(documentComments));
    }


    /**
     * 构建Tree结构
     *
     * @param treeList 待转换的集合
     * @return 树结构
     */
    private List<DocumentComment> buildTree(List<DocumentComment> treeList)
    {
        if (CollUtil.isEmpty(treeList))
        {
            return treeList;
        }
        //记录自己是自己的父节点的id集合
        List<Long> selfIdEqSelfParent = new ArrayList<>();
        // 为每一个节点找到子节点集合
        for (DocumentComment parent : treeList)
        {
            Long id = parent.getId();
            for (DocumentComment children : treeList)
            {
                if (parent != children)
                {
                    //parent != children 这个来判断自己的孩子不允许是自己，因为有时候，根节点的parent会被设置成为自己
                    if (id.equals(children.getParentId()))
                    {
                        children.setChildren(Collections.emptyList());
                        parent.getChildren().add(children);
                    }
                }
                else if (id.equals(parent.getParentId()))
                {
                    selfIdEqSelfParent.add(id);
                }
            }
        }
        // 找出根节点集合
        List<DocumentComment> trees = new ArrayList<>();

        List<Long> allIds = treeList.stream().map(DocumentComment::getId).collect(Collectors.toList());
        for (DocumentComment baseNode : treeList)
        {
            if (!allIds.contains(baseNode.getParentId()) || selfIdEqSelfParent.contains(baseNode.getParentId()))
            {
                trees.add(baseNode);
            }
        }
        return trees;
    }
}
