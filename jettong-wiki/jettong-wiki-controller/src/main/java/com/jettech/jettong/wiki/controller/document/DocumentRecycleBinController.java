package com.jettech.jettong.wiki.controller.document;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.wiki.document.dto.DocumentRecycleBinPageQuery;
import com.jettech.jettong.wiki.document.entity.DocumentRecycleBin;
import com.jettech.jettong.wiki.service.document.DocumentRecycleBinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;

/**
 * 知识库回收站信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库回收站信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.document
 * @className DocumentRecycleBinController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/documentRecycleBin")
@Api(value = "DocumentRecycleBin", tags = "知识库回收站信息")
public class DocumentRecycleBinController extends SuperSimpleController<DocumentRecycleBinService, DocumentRecycleBin>
{

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeDocumentRecycleBinByIds(ids);

        return success();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "parentId", value = "要恢复到的父页面id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "恢复")
    @PutMapping("/recovery/{parentId}/{id}")
    @SysLog(value = "恢复", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> recovery(@PathVariable("parentId") Long parentId, @PathVariable("id") Long id)
    {
        baseService.recovery(id, parentId);
        return success();
    }

    @ApiOperation(value = "分页列表查询，管理员使用")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<DocumentRecycleBin>> page(@Validated @RequestBody PageParams<DocumentRecycleBinPageQuery> params)
    {
        IPage<DocumentRecycleBin> page = params.buildPage(DocumentRecycleBin.class);
        DocumentRecycleBinPageQuery documentPage = params.getModel();

        baseService.page(page, Wraps.<DocumentRecycleBin>lbQ().like(DocumentRecycleBin::getName, documentPage.getName())
                .eq(documentPage.getBizType()!=null,DocumentRecycleBin::getBizType, documentPage.getBizType())
                .eq(documentPage.getBizId()!=null,DocumentRecycleBin::getBizId, documentPage.getBizId())
                .eq(DocumentRecycleBin::getSpaceId, documentPage.getSpaceId()));

        return success(page);
    }


}
