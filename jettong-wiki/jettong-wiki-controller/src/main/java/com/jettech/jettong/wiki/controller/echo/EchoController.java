package com.jettech.jettong.wiki.controller.echo;

import cn.hutool.core.convert.Convert;
import com.jettech.basic.annotation.base.IgnoreResponseBodyAdvice;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.wiki.document.entity.Catalog;
import com.jettech.jettong.wiki.service.document.CatalogService;
import com.jettech.jettong.wiki.service.space.SpaceService;
import com.jettech.jettong.wiki.space.entity.Space;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * 数据注入查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据注入查询接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.base.controller.echo
 * @className EchoController
 * @date 2021/10/27 19:30
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/wiki")
@IgnoreResponseBodyAdvice
@Api(value = "数据注入查询接口", tags = "数据注入查询接口， 不建议前端调用")
@ApiIgnore
public class EchoController
{

    private final SpaceService spaceService;
    private final CatalogService catalogService;

    @PostMapping("/space/echo")
    public boolean saveSpace(@RequestBody Space space)
    {
        return spaceService.saveSpaceAndInitDocument(space);
    }

    @PutMapping("/space/echo/updateSpaceByTypeIdAndBizId")
    public boolean updateSpaceByTypeIdAndBizId(@RequestBody Space space)
    {
        spaceService.update(Wraps.<Space>lbU().set(Space::getName, space.getName())
                .set(Space::getDescription, space.getDescription()).eq(Space::getBizId, space.getBizId())
                .eq(Space::getTypeId, space.getTypeId()));
        return true;
    }

    @DeleteMapping("/space/echo/{typeId}")
    boolean removeByTypeIdAndBizIds(@PathVariable("typeId") Long typeId, @RequestBody List<Long> bizIds)
    {
        List<Long> spaceIds = spaceService.listObjs(
                Wraps.<Space>lbQ().select(Space::getId).eq(Space::getTypeId, typeId).in(Space::getBizId, bizIds)
                        .eq(Space::getReadonly, false), Convert::toLong);

        return spaceService.removeAllByIds(spaceIds);
    }

    @GetMapping("/space/echo/{typeId}/{bizId}")
    public Space findByTypeIdAndBizId(@PathVariable("typeId") Long typeId, @PathVariable("bizId") Long bizId)
    {
        return spaceService.getOne(Wraps.<Space>lbQ().eq(Space::getTypeId, typeId).eq(Space::getBizId, bizId), false);
    }


    @PutMapping("/catalog/echo")
    public boolean updateCatalog(@RequestBody Catalog catalog)
    {
        return catalogService.updateById( catalog);
    }

    @GetMapping("/catalog/echo/{id}")
    public Catalog getCatalogById(@PathVariable Long id){
        return catalogService.getById(id);
    }
}
