package com.jettech.jettong.wiki.controller.space;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.wiki.service.space.SpaceService;
import com.jettech.jettong.wiki.service.space.SpaceTypeService;
import com.jettech.jettong.wiki.space.dto.SpacePageQuery;
import com.jettech.jettong.wiki.space.dto.SpaceQuery;
import com.jettech.jettong.wiki.space.dto.SpaceSaveDTO;
import com.jettech.jettong.wiki.space.dto.SpaceUpdateDTO;
import com.jettech.jettong.wiki.space.entity.Space;
import com.jettech.jettong.wiki.space.entity.SpaceType;
import com.jettech.jettong.wiki.space.enumeration.SpaceSourceType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 知识库空间信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库空间信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.space
 * @className SpaceController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/space")
@Api(value = "Space", tags = "知识库空间信息")
@PreAuth(replace = "wiki:space:")
@RequiredArgsConstructor
public class SpaceController
        extends SuperSimpleController<SpaceService, Space>
{

    private final EchoService echoService;

    private final PersonalizedTableViewApi tableViewApi;

    private final SpaceTypeService spaceTypeService;

    @ApiOperation(value = "查询项目测试方案空间")
    @GetMapping("/getTestSchemeSpace/{projectId}")
    public R<Space> getTestSchemeSpace(@PathVariable Long projectId)
    {
        return R.success(baseService.getTestSchemeSpace(projectId));
    }

    @ApiOperation(value = "查询业务类型空间")
    @GetMapping("/{bizType}/{projectId}")
    public R<Space> getBizSpace(@PathVariable String bizType,@PathVariable Long projectId)
    {
        return R.success(baseService.getBizSpace(bizType,projectId));
    }

    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Boolean> handlerSave(@Validated @RequestBody SpaceSaveDTO model)
    {
        Space space = BeanPlusUtil.toBean(model, Space.class);
        if (null == space.getSourceType())
        {
            space.setSourceType(SpaceSourceType.JETTONG);
        }
        baseService.saveSpace(space);

        return success();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeSpaceByIds(ids);

        return success();
    }

    @ApiOperation(value = "修改", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<Boolean> handlerUpdate(@Validated @RequestBody SpaceUpdateDTO model)
    {
        Space space = BeanPlusUtil.toBean(model, Space.class);
        baseService.updateSpace(space);
        return success();
    }

    @ApiOperation(value = "分页列表查询")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<Space>> page(@Validated @RequestBody PageParams<SpacePageQuery> params)
    {
        IPage<Space> page = params.buildPage(Space.class);
        SpacePageQuery spacePage = params.getModel();

        baseService.page(page, Wraps.<Space>lbQ().like(Space::getName, spacePage.getName())
                .eq(Space::getTypeId, spacePage.getTypeId()).eq(Space::getBizId, spacePage.getBizId())
                        .eq(Space::getSourceType,spacePage.getSourceType())
                        .notIn(Space::getTypeId,spaceTypeService.list(Wraps.<SpaceType>lbQ().eq(SpaceType::getShowable,false)).stream().collect(Collectors.toList()))
        .eq(Space::getLeadingBy,spacePage.getLeadingBy()));

        echoService.action(page);

        // 保存最后一次查询条件
        tableViewApi.saveLastSearch(getUserId(), "wiki-cards", params);

        return success(page);
    }

    @ApiOperation(value = "批量查询", notes = "批量查询")
    @PostMapping("/query")
    @SysLog("批量查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<Space>> query(@Validated @RequestBody SpaceQuery query)
    {
        List<Space> spaces = baseService.list(Wraps.<Space>lbQ().like(Space::getName, query.getName())
                .eq(Space::getTypeId, query.getTypeId())
                .eq(Space::getBizId, query.getBizId()).eq(Space::getLeadingBy,query.getLeadingBy()));
        echoService.action(spaces);
        return success(spaces);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_PATH),
    })
    @ApiOperation(value = "单体查询", notes = "单体查询")
    @GetMapping("/{id}")
    @SysLog("'查询:' + #id")
    @PreAuth("hasAnyPermission('{}view')")
    public R<Space> get(@PathVariable("id") Long id)
    {
        Space space = baseService.getById(id);
        echoService.action(space);
        return success(space);
    }

    @ApiOperation(value = "查询我的空间", notes = "查询我的空间")
    @GetMapping("/my")
    @SysLog("查询我的空间")
    public R<List<Space>> mySpace()
    {
        // TODO 查询所有空间，暂不做权限
        List<Space> spaces = baseService.list();
        echoService.action(spaces);
        return success(spaces);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测名称是否可用", notes = "检测名称是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测名称是否可用", request = false)
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String name)
    {
        return success(baseService.check(id, name));
    }
    /**
     * 条件查询产品,用于其他模块查询
     * @return R
     * @throws
     * <AUTHOR>
     * @date 2022/07/14 10:24
     * @update lxr 2022/07/14 10:24
     * @since 1.0
     */
    @ApiOperation(value = "查询可以关联的文档信息,用于其他模块查询", notes = "查询可以关联的文档信息,用于其他模块查询")
    @PostMapping(value = "/getSpaceByRelationIdAndTypeAndFileId")
    @SysLog("查询可以关联的文档信息,用于其他模块查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R getPipelineListExcludePipelineIds(@RequestBody Map<String, Object> map)
    {
        //todo 此处不用
//        try
//        {
//            ReleaseWikiVo vo =null;
//            if(map.get("relationId")!=null){
//                vo =   releaseVersionApi.getFileIds(map);
//            }else {
//                return success(new ArrayList<>());
//            }
//            LbqWrapper<Space> spaceLbqWrapper = Wraps.<Space>lbQ();
//            if(vo!=null && vo.getWikiIdList() !=null && vo.getWikiIdList().size()>0){
//                spaceLbqWrapper.notIn(Space::getId, vo.getWikiIdList());
//            }
//            if(vo.getSourceType()==null)
//            {
//                return R.success(new ArrayList<>());
//            }
//            //如果是发布工单或者发布版本，通过产品id查询文档
//            if(vo.getSourceType().equals("3") || vo.getSourceType().equals("2"))
//            {
//                if(vo.getProductId()!=null){
//                    spaceLbqWrapper.eq(Space::getBizId, vo.getProductId());
//                }else {
//                    return R.success();
//                }
//            }
//            List<Space> list = baseService.list(spaceLbqWrapper);
//            return success(list);
//        }
//        catch (Exception e)
//        {
//            log.error("查询可以关联的文档信息异常:{}", e.getMessage(), e);
//            return fail("查询可以关联的文档信息异常");
//        }
        return R.success(new ArrayList<>());
    }

    /**
     * 分页条件条件查询产品,用于其他模块查询
     * @return {@link R}
     * @throws
     * <AUTHOR>
     * @date 2022/07/13 18:07
     * @update lxr 2022/07/13 18:07
     * @since 1.0
     */
    @ApiOperation(value = "分页条件查询关联的文档信息,用于其他模块查询", notes = "分页条件查询关联的文档信息,用于其他模块查询")
    @PostMapping(value = "/getSpacePageByRelationIdAndTypeAndFileId")
    public IPage<Space> jobPage(@RequestBody PageParams<Map<String, Object>> params)
    {
        //todo 此处不用
//        IPage<Space> page = params.buildPage(Space.class);
//        Map<String, Object> map = params.getModel();
//        List<Long> fileIds=null;
//        if(map.get("relationId")!=null||map.get("sourceType")!=null){
//            ReleaseWikiVo vo=   releaseVersionApi.getFileIds( map);
//            fileIds=vo.getWikiIdList();
//            if(fileIds==null || fileIds.size()==0){
//                return page;
//            }
//        }
//        IPage<Space> productVersionIPage = baseService.page(page, Wraps.<Space>lbQ()
//                .in(Space::getId, fileIds));
//        return productVersionIPage;
        return null;
    }
}
