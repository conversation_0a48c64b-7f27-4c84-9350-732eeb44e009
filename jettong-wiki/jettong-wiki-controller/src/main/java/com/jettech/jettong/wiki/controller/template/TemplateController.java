package com.jettech.jettong.wiki.controller.template;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.wiki.service.template.TemplateService;
import com.jettech.jettong.wiki.service.template.TemplateTypeService;
import com.jettech.jettong.wiki.template.dto.TemplatePageQuery;
import com.jettech.jettong.wiki.template.dto.TemplateSaveBizDTO;
import com.jettech.jettong.wiki.template.dto.TemplateSaveDTO;
import com.jettech.jettong.wiki.template.dto.TemplateUpdateDTO;
import com.jettech.jettong.wiki.template.entity.Template;
import com.jettech.jettong.wiki.template.entity.TemplateType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;


/**
 * 知识库模板信息控制器
 * <AUTHOR>
 * @version 1.0
 * @description 知识库模板信息控制器
 * @projectName jettong
 * @package com.jettech.template.controller
 * @className TemplateController
 * @date 2025-07-30
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/template")
@Api(value = "Template", tags = "知识库模板信息")
@AllArgsConstructor
public class TemplateController extends SuperController<TemplateService, Long, Template, TemplatePageQuery, TemplateSaveDTO, TemplateUpdateDTO>
{


    private final TemplateTypeService templateTypeService;

    @ApiOperation("保存模板")
    @PostMapping("/saveCatalogTemplate")
    @SysLog(value = "保存模板", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Template> saveCatalogTemplate(@RequestBody TemplateSaveBizDTO templateSaveDTO)
    {
        return success(baseService.saveCatalogTemplate(templateSaveDTO));
    }



    @Override
    public IPage<Template> query(PageParams<TemplatePageQuery> params) {
        this.handlerQueryParams(params);
        IPage<Template> page = params.buildPage(this.getEntityClass());
        Template model = BeanUtil.toBean(params.getModel(), this.getEntityClass());
        QueryWrap<Template> wrapper = this.handlerWrapper(model, params);
        if (model.getId()==null&&params.getModel().getBizType()!=null){
            wrapper.lambda().eq(Template::getTypeId,templateTypeService.getOne(Wraps.<TemplateType>lbQ().eq(TemplateType::getCode, params.getModel().getBizType().getCode())).getId());
        }
        this.getBaseService().page(page, wrapper);
        this.handlerResult(page);
        return page;
    }

    @Override
    public R<List<Template>> query(@RequestBody Template data) {
        if (data.getBizType()!=null){
            TemplateType templateType = templateTypeService.getOne(Wraps.<TemplateType>lbQ().eq(TemplateType::getCode, data.getBizType().getCode()));
            if (templateType==null){
                return success(Collections.EMPTY_LIST);
            }
            data.setTypeId(templateType.getId());
        }
        QueryWrap<Template> wrapper = Wraps.q(data);
        return this.success(this.getBaseService().list(wrapper));
    }
}
