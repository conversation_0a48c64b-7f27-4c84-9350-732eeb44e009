package com.jettech.jettong.wiki.controller.document;

import com.jettech.basic.base.controller.SuperController;
import com.jettech.jettong.wiki.document.dto.DocumentModuleFunctionPageQuery;
import com.jettech.jettong.wiki.document.dto.DocumentModuleFunctionSaveDTO;
import com.jettech.jettong.wiki.document.dto.DocumentModuleFunctionUpdateDTO;
import com.jettech.jettong.wiki.document.entity.DocumentModuleFunction;
import com.jettech.jettong.wiki.service.document.DocumentModuleFunctionService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 控制器
 * <AUTHOR>
 * @version 1.0
 * @description 控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller
 * @className DocumentModuleFunctionController
 * @date 2025-08-01
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/documentModuleFunction")
@Api(value = "DocumentModuleFunction", tags = "")
public class DocumentModuleFunctionController extends SuperController<DocumentModuleFunctionService, Long, DocumentModuleFunction, DocumentModuleFunctionPageQuery, DocumentModuleFunctionSaveDTO, DocumentModuleFunctionUpdateDTO>
{

}
