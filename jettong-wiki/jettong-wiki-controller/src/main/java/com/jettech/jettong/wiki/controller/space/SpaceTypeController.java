package com.jettech.jettong.wiki.controller.space;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.wiki.service.space.SpaceTypeService;
import com.jettech.jettong.wiki.space.dto.SpaceTypePageQuery;
import com.jettech.jettong.wiki.space.dto.SpaceTypeQuery;
import com.jettech.jettong.wiki.space.dto.SpaceTypeSaveDTO;
import com.jettech.jettong.wiki.space.dto.SpaceTypeUpdateDTO;
import com.jettech.jettong.wiki.space.entity.SpaceType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 知识库空间类型信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库空间类型信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.space
 * @className SpaceTypeController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/spaceType")
@Api(value = "SpaceType", tags = "知识库空间类型信息")
@PreAuth(replace = "wiki:spaceType:")
public class SpaceTypeController extends SuperSimpleController<SpaceTypeService, SpaceType>
{

    @ApiOperation(value = "新增")
    @PostMapping
    @SysLog(value = "新增", request = false, optType = OptLogTypeEnum.ADD)
    @PreAuth("hasAnyPermission('{}add')")
    public R<Boolean> handlerSave(@Validated @RequestBody SpaceTypeSaveDTO model)
    {
        SpaceType spaceType = BeanPlusUtil.toBean(model, SpaceType.class);
        baseService.saveSpaceType(spaceType);

        return success();
    }

    @ApiOperation(value = "删除")
    @DeleteMapping
    @SysLog(value = "'删除:' + #ids", optType = OptLogTypeEnum.DELETE)
    @PreAuth("hasAnyPermission('{}delete')")
    public R<Boolean> handlerDelete(@RequestBody List<Long> ids)
    {
        baseService.removeSpaceTypeByIds(ids);

        return success();
    }

    @ApiOperation(value = "修改", notes = "修改UpdateDTO中不为空的字段")
    @PutMapping
    @SysLog(value = "'修改:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<Boolean> handlerUpdate(@Validated @RequestBody SpaceTypeUpdateDTO model)
    {
        SpaceType spaceType = BeanPlusUtil.toBean(model, SpaceType.class);
        baseService.updateSpaceType(spaceType);
        return success();
    }

    @ApiOperation(value = "分页列表查询")
    @PostMapping("/page")
    @SysLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<SpaceType>> page(@Validated @RequestBody PageParams<SpaceTypePageQuery> params)
    {
        IPage<SpaceType> page = params.buildPage(SpaceType.class);
        SpaceTypePageQuery spaceTypePage = params.getModel();

        baseService.page(page, Wraps.<SpaceType>lbQ().like(SpaceType::getName, spaceTypePage.getName())
                .like(SpaceType::getDescription, spaceTypePage.getDescription()));

        return success(page);
    }

    @ApiOperation(value = "批量查询", notes = "批量查询")
    @PostMapping("/query")
    @SysLog("批量查询")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<SpaceType>> query(@Validated @RequestBody SpaceTypeQuery query)
    {
        List<SpaceType> spaceTypes = baseService.list(Wraps.<SpaceType>lbQ().like(SpaceType::getName, query.getName())
                .like(SpaceType::getDescription, query.getDescription()));

        return success(spaceTypes);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键", dataType =DATA_TYPE_LONG, paramType = PARAM_TYPE_PATH),
    })
    @ApiOperation(value = "单体查询", notes = "单体查询")
    @GetMapping("/{id}")
    @SysLog("'查询:' + #id")
    @PreAuth("hasAnyPermission('{}view')")
    public R<SpaceType> get(@PathVariable("id") Long id)
    {
        return success(baseService.getById(id));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "ID", dataType = DATA_TYPE_LONG, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "name", value = "名称", dataType = DATA_TYPE_STRING, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "检测名称是否可用", notes = "检测名称是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测名称是否可用", request = false)
    public R<Boolean> check(@RequestParam(required = false) Long id, @RequestParam String name)
    {
        return success(baseService.check(id, name));
    }
}
