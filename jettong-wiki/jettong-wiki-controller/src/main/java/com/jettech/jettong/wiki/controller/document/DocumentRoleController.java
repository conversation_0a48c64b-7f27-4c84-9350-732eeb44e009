package com.jettech.jettong.wiki.controller.document;

import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.jettong.wiki.document.dto.DocumentRolePageQuery;
import com.jettech.jettong.wiki.document.dto.DocumentRoleSaveDTO;
import com.jettech.jettong.wiki.document.dto.DocumentRoleUpdateDTO;
import com.jettech.jettong.wiki.document.entity.DocumentRole;
import com.jettech.jettong.wiki.service.document.DocumentRoleService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 知识库权限角色信息控制器
 * <AUTHOR>
 * @version 1.0
 * @description 知识库权限角色信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.document.controller
 * @className DocumentRoleController
 * @date 2025-07-18
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/documentRole")
@Api(value = "DocumentRole", tags = "知识库权限角色信息")
@PreAuth(replace = "wiki:documentRole:")
public class DocumentRoleController extends SuperController<DocumentRoleService, Long, DocumentRole, DocumentRolePageQuery, DocumentRoleSaveDTO, DocumentRoleUpdateDTO>
{

}
