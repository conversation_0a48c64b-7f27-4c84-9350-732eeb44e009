package com.jettech.jettong.wiki.controller.document;

import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.jettong.wiki.document.entity.DocumentShare;
import com.jettech.jettong.wiki.service.document.DocumentShareService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库文件分享信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 知识库文件分享信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller.document
 * @className DocumentShareController
 * @date 2021-11-25
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/wiki/documentShare")
@Api(value = "DocumentShare", tags = "知识库文件分享信息")
public class DocumentShareController extends SuperSimpleController<DocumentShareService, DocumentShare>
{

}
