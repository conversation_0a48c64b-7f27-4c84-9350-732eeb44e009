package com.jettech.jettong.wiki.controller.component;

import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.jettong.wiki.component.dto.*;
import com.jettech.jettong.wiki.component.entity.ComponentTable;
import com.jettech.jettong.wiki.service.component.ComponentTableService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 自定义表格组件主表控制器
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表格组件主表控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.controller
 * @className ComponentTableController
 * @date 2025-08-23
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/componentTable")
@Api(value = "ComponentTable", tags = "自定义表格组件主表")
@PreAuth(replace = "wiki:componentTable:")
@AllArgsConstructor
public class ComponentTableController extends SuperController<ComponentTableService, Long, ComponentTable, ComponentTablePageQuery, ComponentTableSaveDTO, ComponentTableUpdateDTO>
{


    @ApiOperation(value = "组件内容查询")
    @PostMapping("/execute")
    public R<ComponentTableDTO> execute(@RequestBody PageParams<ComponentTableQuery> query){
        return R.success(baseService.execute(query));
    }


    @Override
    public R<List<ComponentTable>> query(@RequestBody ComponentTable data) {
        if (data.getEnable()==null){
            data.setEnable(Boolean.TRUE);
        }
        QueryWrap<ComponentTable> wrapper = Wraps.q(data);
        List<ComponentTable> componentTables = this.getBaseService().list(wrapper);
        componentTables.forEach(componentTable -> {
            componentTable.setSqlContent(null);
            componentTable.setTableStyle(null);
            componentTable.setDbConfig( null);
        });

        return this.success(componentTables);
    }

}
