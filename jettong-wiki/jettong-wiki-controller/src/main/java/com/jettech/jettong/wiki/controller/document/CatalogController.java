package com.jettech.jettong.wiki.controller.document;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.api.StateApi;
import com.jettech.jettong.alm.issue.dto.StateTransitionDTO;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.wiki.document.dto.*;
import com.jettech.jettong.wiki.document.entity.Catalog;
import com.jettech.jettong.wiki.document.entity.Document;
import com.jettech.jettong.wiki.document.entity.DocumentVersion;
import com.jettech.jettong.wiki.service.document.CatalogService;
import com.jettech.jettong.wiki.template.dto.TemplateSaveBizDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 知识库文档目录控制器
 * <AUTHOR>
 * @version 1.0
 * @description 知识库文档目录控制器
 * @projectName jettong
 * @package com.jettech.jettong.wiki.document.controller
 * @className CatalogController
 * @date 2025-07-18
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/catalog")
@Api(value = "Catalog", tags = "知识库文档目录")
@RequiredArgsConstructor
@RefreshScope
public class CatalogController extends SuperController<CatalogService, Long, Catalog, CatalogPageQuery, CatalogSaveDTO, CatalogUpdateDTO>
{

    private final EchoService echoService;
    private final StateApi stateApi;

    @Value("${wiki.catalog.workflow:8}")
    private Long workflowId;

    @Override
    public R<Catalog> get(Long id){
        Catalog catalog = baseService.getById(id);
        echoService.action(catalog);
        return success(catalog);
    }

    @ApiOperation(value = "根据目录id获取文档")
    @GetMapping("/getDocumentsByCatalogId")
    public R<List<Document>> getDocumentsByCatalogId(@RequestParam Long catalogId){
        List<Document> documents = baseService.getDocumentsByCatalogId(catalogId);
        echoService.action(documents);
        return R.success(documents);
    }


    @ApiOperation(value = "按业务查询知识库文档目录,附加当前用户权限")
    @PostMapping("/listByBiz")
    @SysLog("按业务查询知识库文档目录")
    public R<List<Catalog>> listByBiz(@RequestBody CatalogQuery query){
        query.setUserId(getUserId());
        return R.success(baseService.getCatalogsByBiz(query));
    }

    @ApiOperation(value = "按业务查询知识库所有文档")
    @PostMapping("/allDocument")
    @SysLog("按业务查询知识库文档目录")
    public R<List<Document>> allDocument(@RequestBody CatalogQuery query){
        query.setUserId(getUserId());
        return R.success(baseService.getAllDocumentsByBiz(query));
    }

    @Override
    public R<Catalog> handlerSave(CatalogSaveDTO model){
        Catalog catalog = BeanPlusUtil.toBean(model, Catalog.class);
        if (catalog.getLeadingBy()==null){
            catalog.setLeadingBy(getUserId());
        }
        baseService.save(catalog);
        return success(catalog);
    }

    @ApiOperation(value = "批量保存")
    @SysLog("批量保存")
    @PostMapping("/saveBatch")
    public R<Boolean> saveBatch(@RequestBody List<CatalogSaveDTO> saveDTOList){
        return R.success(baseService.saveBatch(saveDTOList));
    }

    @ApiOperation(value = "设置章节负责人")
    @SysLog("设置章节负责人")
    @PostMapping("/setCatalogLeadingBy")
    public R<Boolean> setCatalogLeadingBy(@RequestBody List<CatalogUpdateLeadingByDTO> updateDTOList){
        return R.success(baseService.updateLeadingBy(updateDTOList));
    }

    @ApiOperation(value = "设置章节协作人")
    @SysLog("设置章节协作人")
    @PostMapping("/setCatalogCollaborator")
    public R<Boolean> setCatalogCollaborator(@RequestBody List<CatalogUpdateRoleDTO> updateDTOList){
        return R.success(baseService.updateCollaborator(updateDTOList));
    }


    @ApiOperation("使用模板")
    @PostMapping("/useTemplate")
    @SysLog(value = "使用模板", request = false, optType = OptLogTypeEnum.ADD)
    public R<Boolean> useTemplate(@RequestBody TemplateSaveBizDTO templateSaveDTO)
    {
        baseService.useTemplate(templateSaveDTO);
        return success();
    }


    @ApiOperation(value = "章节调整顺序")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "目录id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "sort", value = "排序", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "bizId", value = "bizId", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "bizType", value = "业务类型", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY)
    })
    @PostMapping("/sort")
    @SysLog(value = "章节调整顺序", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> updateSort(@RequestBody CatalogUpdateDTO sortDTO)
    {
        baseService.updateSort(sortDTO);
        return R.success();
    }

    @ApiOperation(value = "wiki目录状态流转", notes = "wiki目录状态流转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "catalogId", value = "目录id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "sourceStateCode", value = "源状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "targetStateCode", value = "目标状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @PutMapping("/transitionState/{catalogId}/{sourceStateCode}/{targetStateCode}")
    @SysLog(value = "wiki目录状态流转", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> transitionWikiCatalogState(@PathVariable("catalogId") Long catalogId,
            @PathVariable("sourceStateCode") String sourceStateCode,
            @PathVariable("targetStateCode") String targetStateCode)
    {
        Catalog catalog = baseService.getById(catalogId);
        if (catalog == null){
            return R.fail("目录不存在");
        }
        StateTransitionDTO stateTransitionDTO = new StateTransitionDTO();
        stateTransitionDTO.setWorkFlowId(workflowId);
        stateTransitionDTO.setBizId(catalogId);
        stateTransitionDTO.setSourceStateCode(sourceStateCode);
        stateTransitionDTO.setTargetStateCode(targetStateCode);
        stateTransitionDTO.setTypeCode("WIKI_CATALOG");
        stateTransitionDTO.setCreateTime(catalog.getCreateTime());
        Boolean result = stateApi.transitionStateByWorkflowIdAndBizId(stateTransitionDTO);
        if (!result){
            return R.fail("状态流转失败");
        }
        catalog.setStateCode(targetStateCode);
        baseService.updateById(catalog);
        return success();
    }

    @ApiOperation(value = "查询wiki目录可流转的状态节点", notes = "查询wiki目录可流转的状态节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "catalogId", value = "目录id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findNextNode/{catalogId}")
    public R<List<WorkflowNode>> findWikiCatalogNextNode(@PathVariable Long catalogId)
    {
        Catalog catalog = baseService.getById(catalogId);
        if (catalog == null){
            return R.fail("目录不存在");
        }
        List<WorkflowNode> nextNodes = stateApi.findNextNodeByWorkflowIdAndLeadingBy(workflowId, catalog.getLeadingBy(), catalog.getStateCode());
        return success(nextNodes);
    }

}
