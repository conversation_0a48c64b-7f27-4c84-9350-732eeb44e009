# Jettong-TM 项目

Jettong-TM 是一个基于 Spring Boot 的企业级微服务项目，采用 Maven 多模块架构设计。

## 项目概述

- **项目名称**: jettong-tm
- **技术栈**: Java 8, Spring Boot 2.5.3, Maven
- **架构模式**: 微服务架构 + 分层架构
- **包管理**: Maven 多模块管理

## 项目结构

```
jettong-tm/
├── jettong-tm-common/          # 公共模块 - 通用工具类和配置
├── jettong-tm-base/            # 基础服务模块
│   ├── jettong-tm-base-api/    # API接口定义
│   ├── jettong-tm-base-entity/ # 实体类
│   ├── jettong-tm-base-biz/    # 业务逻辑层
│   ├── jettong-tm-base-controller/ # 控制器层
│   ├── jettong-tm-base-job/    # 定时任务
│   └── jettong-tm-base-server/ # 服务启动模块
├── jettong-cmdb/               # 资源中心服务
│   ├── jettong-cmdb-api/       # API接口定义
│   ├── jettong-cmdb-entity/    # 实体类
│   ├── jettong-cmdb-biz/       # 业务逻辑层
│   ├── jettong-cmdb-controller/ # 控制器层
│   ├── jettong-cmdb-job/       # 定时任务
│   └── jettong-cmdb-server/    # 服务启动模块
├── jettong-alm/                # 项目协同服务
│   ├── jettong-alm-api/        # API接口定义
│   ├── jettong-alm-entity/     # 实体类
│   ├── jettong-alm-job/        # 定时任务
│   ├── jettong-alm-server/     # 服务启动模块
│   ├── jettong-project-biz/    # 项目管理业务层
│   ├── jettong-project-controller/ # 项目管理控制器
│   ├── jettong-workflow-biz/   # 工作流业务层
│   ├── jettong-workflow-controller/ # 工作流控制器
│   ├── jettong-kanban-biz/     # 看板业务层
│   ├── jettong-kanban-controller/ # 看板控制器
│   ├── jettong-workinghours-biz/ # 工时管理业务层
│   └── jettong-workinghours-controller/ # 工时管理控制器
├── jettong-product/            # 产品管理服务
│   ├── jettong-product-api/    # API接口定义
│   ├── jettong-product-entity/ # 实体类
│   ├── jettong-product-biz/    # 业务逻辑层
│   ├── jettong-product-controller/ # 控制器层
│   └── jettong-product-server/ # 服务启动模块
├── jettong-testm/              # 测试管理服务
│   ├── jettong-testm-api/      # API接口定义
│   ├── jettong-testm-entity/   # 实体类
│   ├── jettong-testm-biz/      # 业务逻辑层
│   ├── jettong-testm-controller/ # 控制器层
│   └── jettong-testm-server/   # 服务启动模块
├── jettong-insight/            # 数据洞察服务
│   ├── jettong-insight-entity/ # 实体类
│   ├── jettong-insight-biz/    # 业务逻辑层
│   ├── jettong-insight-controller/ # 控制器层
│   └── jettong-insight-server/ # 服务启动模块
├── jettong-wiki/               # 知识库服务
│   ├── jettong-wiki-api/       # API接口定义
│   ├── jettong-wiki-entity/    # 实体类
│   ├── jettong-wiki-biz/       # 业务逻辑层
│   ├── jettong-wiki-controller/ # 控制器层
│   └── jettong-wiki-server/    # 服务启动模块
└── pom.xml                     # 父级POM配置文件
```

## 模块说明

### 核心业务模块

| 模块名称              | 功能描述     | 主要职责                                           |
| --------------------- | ------------ | -------------------------------------------------- |
| **jettong-tm-common** | 公共模块     | 提供通用工具类、配置和基础组件                     |
| **jettong-tm-base**   | 基础服务     | 系统基础功能，如用户管理、权限控制等               |
| **jettong-cmdb**      | 资源中心服务 | 配置管理数据库，管理IT资源和配置信息               |
| **jettong-alm**       | 项目协同服务 | 应用生命周期管理，包括项目、工作流、看板、工时管理 |
| **jettong-product**   | 产品管理服务 | 产品信息管理和产品生命周期管理                     |
| **jettong-testm**     | 测试管理服务 | 测试用例管理、测试执行和测试报告                   |
| **jettong-insight**   | 数据洞察服务 | 数据分析、报表生成和业务洞察                       |
| **jettong-wiki**      | 知识库服务   | 文档管理、知识分享和协作                           |

### 分层架构说明

每个业务模块都采用标准的分层架构：

- **api**: API接口定义层，定义对外暴露的接口
- **entity**: 实体层，定义数据模型和实体类
- **biz**: 业务逻辑层，实现核心业务逻辑
- **controller**: 控制器层，处理HTTP请求和响应
- **job**: 定时任务层，处理后台定时任务
- **server**: 服务启动层，Spring Boot应用启动入口

## 技术栈

### 核心框架
- **Java**: 8
- **Spring Boot**: 2.5.3
- **Spring Cloud OpenFeign**: 3.0.8
- **Maven**: 项目构建和依赖管理

### 工具库
- **Lombok**: 简化Java代码
- **Hutool**: Java工具类库
- **Fastjson**: JSON处理
- **Guava**: Google核心库
- **SLF4J**: 日志框架

### Jettong基础组件
项目集成了多个自研的基础组件：
- jettong-boot-base: 基础启动组件
- jettong-security-starter: 安全组件
- jettong-cache-starter: 缓存组件
- jettong-databases: 数据库组件
- jettong-swagger2-starter: API文档组件
- jettong-jwt-starter: JWT认证组件
- jettong-log-starter: 日志组件
- jettong-mq-starter: 消息队列组件
- jettong-job-starter: 定时任务组件

## 构建和部署

### 环境配置
- **开发环境**: dev (默认激活)
- **生产环境**: prod

### 构建命令
```bash
# 编译整个项目
mvn clean compile

# 打包整个项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install
```

### 运行服务

每个服务模块的server子模块都是独立的Spring Boot应用，可以单独启动：

## 开发规范

1. **模块依赖**: 遵循分层依赖原则，避免循环依赖
2. **代码规范**: 使用Lombok简化代码，统一使用SLF4J进行日志记录
3. **配置管理**: 使用Spring Boot的配置文件管理，支持多环境配置
4. **API设计**: 使用Swagger2生成API文档，遵循RESTful设计原则