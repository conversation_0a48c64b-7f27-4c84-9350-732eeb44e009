<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jettech.jettong</groupId>
    <artifactId>jettong-tm</artifactId>
    <version>develop</version>

    <modules>
        <module>jettong-tm-common</module>
        <module>jettong-tm-base</module>
        <module>jettong-cmdb</module>
        <module>jettong-alm</module>
        <module>jettong-product</module>
        <module>jettong-testm</module>
        <module>jettong-insight</module>
        <module>jettong-wiki</module>

    </modules>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>jettong-tm</description>

    <properties>
        <jettong-util.version>1.0.0</jettong-util.version>
        <jettong-project.version>develop</jettong-project.version>
        <!-- jdk8 将这里改成 8 -->
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-jar-plugin.version>3.2.0</maven-jar-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <maven-javadoc-plugin.version>3.2.0</maven-javadoc-plugin.version>
        <spring-boot-maven-plugin.version>2.5.3</spring-boot-maven-plugin.version>
        <spring.cloud.openfeign.version>3.0.8</spring.cloud.openfeign.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-all</artifactId>
                <version>${jettong-util.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-mq-starter</artifactId>
                <version>${jettong-project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.jettong</groupId>
                <artifactId>jettong-tm-common</artifactId>
                <version>${jettong-project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-salt-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-jenkins-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-ftp-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-annotation</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-boot-base</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-cache-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-security-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-util-common</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-databases</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-dozer-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-echo-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-jwt-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-log-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-swagger2-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-validator-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-xss-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-cloud-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-job-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jettech.basic</groupId>
                <artifactId>jettong-file-import-starter</artifactId>
                <version>${jettong-util.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 生产环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
            </properties>
        </profile>

        <profile>
            <id>undertow</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-undertow</artifactId>
                    <exclusions>
                        <exclusion>
                            <artifactId>jboss-annotations-api_1.3_spec</artifactId>
                            <groupId>org.jboss.spec.javax.annotation</groupId>
                        </exclusion>
                    </exclusions>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>blazeds</id>
            <dependencies>
                <dependency>
                    <groupId>com.bes.appserver</groupId>
                    <artifactId>bes-lite-spring-boot-2.x-starter</artifactId>
                    <version>9.5.2.020</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <finalName>${project.artifactId}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven-jar-plugin.version}</version>
                </plugin>
                <!-- resources资源插件 -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>${maven-resources-plugin.version}</version>
                    <configuration>
                        <delimiters>
                            <delimiter>@</delimiter>
                        </delimiters>
                        <useDefaultDelimiters>false</useDefaultDelimiters>
                        <encoding>UTF-8</encoding>
                        <!-- 后缀为pem、pfx的证书文件 -->
                        <nonFilteredFileExtensions>
                            <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                            <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                            <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                            <nonFilteredFileExtension>key</nonFilteredFileExtension>
                            <nonFilteredFileExtension>db</nonFilteredFileExtension>
                            <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                            <nonFilteredFileExtension>otf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                            <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                            <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                            <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        </nonFilteredFileExtensions>
                    </configuration>
                </plugin>
                <!-- java文档插件 -->
                <!--                <plugin>-->
                <!--                    <groupId>org.apache.maven.plugins</groupId>-->
                <!--                    <artifactId>maven-javadoc-plugin</artifactId>-->
                <!--                    <version>${maven-javadoc-plugin.version}</version>-->
                <!--                </plugin>-->

                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
