# TestRequirementFunctionPointsExcelController 功能说明

## 概述

本控制器实现了基于动态字段配置（fieldconfig）的测试分析功能要点Excel操作功能，包括模板导出、数据导入和筛选数据导出。

## 主要功能

### 1. 基于fieldconfig的模板导出

**接口**: `GET /testRequirementFunctionPoints/excel/template/{projectId}/{taskId}?reqId={reqId}`

**参数**:
- `projectId`: 项目ID
- `taskId`: 任务ID
- `reqId`: 需求ID（可选）

**功能描述**:
- 根据数据库中存储的`TestRequirementAnalysisConfig.fieldConfig`配置动态生成Excel模板
- 第一列固定为"系统"，第二列固定为"交易"
- 其他列按fieldconfig中的定义顺序排列
- 为所有字段添加数据约束（下拉选项）
- 系统和交易的范围为当前测试分析任务关联的系统交易范围

**实现要点**:
- 调用`ProductModuleFunctionApi.findByProjectId(projectId, taskId, reqId, null)`获取任务关联的系统交易
- 解析fieldconfig JSON配置，动态构建Excel列头和约束
- 使用EasyPOI生成Excel模板并应用下拉约束
- 根据nodeType过滤：0=系统，1=模块，2=交易

### 2. 数据导入功能

**接口**: `POST /testRequirementFunctionPoints/excel/import`

**参数**:
- `file`: Excel文件
- `projectId`: 项目ID
- `taskId`: 任务ID
- `reqId`: 需求ID（可选）
- `versionId`: 版本ID（可选）

**功能描述**:
- 参考`com.jettech.jettong.testm.controller.poi`包下的类实现数据导入
- 校验系统、交易及其他字段的取值是否符合约束
- 支持多种字段类型验证：TEXT、TEXTAREA、SELECT、MULTI-SELECT
- 详细的错误提示，包含行号信息

**校验规则**:
- 必填字段验证
- 系统和交易值必须在允许范围内
- 选择类型字段值必须在options中
- 文本类型字段长度验证

### 3. 筛选数据导出功能

**接口**: `POST /testRequirementFunctionPoints/excel/export`

**功能描述**:
- 参考`TestRequirementFunctionPointsController`中的query方法实现查询条件
- 支持所有原有的筛选条件
- 导出格式与模板导出保持一致
- 根据fieldconfig动态设置导出字段

**查询条件**:
- `projectId`: 项目ID
- `taskId`: 任务ID
- `functionAndModuleId`: 功能模块ID
- `issueTestReqId`: 需求ID
- `functionId`: 功能ID
- 其他字段筛选条件

## 技术实现

### API调用

1. **获取任务关联的系统交易**:
   ```
   GET /product/productModuleFunction/findByProjectId/{projectId}?bizId={taskId}&reqId={reqId}
   ```

2. **查询测试分析功能点**:
   ```
   POST /testm/testRequirementFunctionPoints/page
   ```

### 字段配置格式

fieldconfig JSON格式示例：
```json
[
    {
        "key": "functionPoint",
        "display_name": "功能点",
        "field_type": "TEXT",
        "required": true,
        "max_length": 100
    },
    {
        "key": "priority",
        "display_name": "优先级",
        "field_type": "SELECT",
        "required": true,
        "options": [
            {"text": "高", "value": "HIGH"},
            {"text": "中", "value": "MEDIUM"},
            {"text": "低", "value": "LOW"}
        ]
    }
]
```

### 支持的字段类型

- **TEXT**: 文本字段，支持max_length验证
- **TEXTAREA**: 多行文本字段
- **SELECT**: 单选下拉字段，需要options配置
- **MULTI-SELECT**: 多选字段，需要options配置

## 代码结构

### 主要方法

- `template()`: 模板导出
- `importExcel()`: 数据导入
- `export()`: 筛选数据导出

### 辅助方法

- `getEnabledConfig()`: 获取启用的配置
- `getSystemAndTransactionByTask()`: 获取任务关联的系统交易
- `buildTemplateWorkbook()`: 构建模板工作簿
- `validateAndConvertImportData()`: 校验和转换导入数据
- `queryDataForExport()`: 查询数据用于导出

### 常量定义

```java
private static final String EXCEL_SHEET_NAME = "测试分析";
private static final String TEMPLATE_FILE_NAME = "测试分析导入模板.xls";
private static final String EXPORT_FILE_PREFIX = "测试分析信息";
private static final int NODE_TYPE_SYSTEM = 0;
private static final int NODE_TYPE_MODULE = 1;
private static final int NODE_TYPE_TRANSACTION = 2;
```

## 使用示例

### 1. 下载模板

```bash
curl -X GET "http://localhost:8080/api/testm/testRequirementFunctionPoints/excel/template/1962330750892113920/1962346497966080000?reqId=1962331161988431872" \
  -H "Accept: application/octet-stream" \
  --output template.xls
```

### 2. 导入数据

```bash
curl -X POST "http://localhost:8080/api/testm/testRequirementFunctionPoints/excel/import" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@data.xls" \
  -F "projectId=1962330750892113920" \
  -F "taskId=1962346497966080000" \
  -F "reqId=1962331161988431872"
```

### 3. 导出数据

```bash
curl -X POST "http://localhost:8080/api/testm/testRequirementFunctionPoints/excel/export" \
  -H "Content-Type: application/json" \
  -H "Accept: application/octet-stream" \
  -d '{
    "size": 20,
    "current": 1,
    "model": {
      "projectId": "1962330750892113920",
      "taskId": "1962346497966080000",
      "functionAndModuleId": "1959505688296161280"
    }
  }' \
  --output export.xls
```

## 注意事项

1. **配置依赖**: 功能依赖于`TestRequirementAnalysisConfig`表中的启用配置
2. **API依赖**: 需要确保`ProductModuleFunctionItemApi`服务可用
3. **权限控制**: 建议添加适当的权限控制
4. **性能考虑**: 大量数据导出时建议分批处理
5. **错误处理**: 已实现详细的错误提示和日志记录

## 测试

运行单元测试：
```bash
mvn test -Dtest=TestRequirementFunctionPointsExcelControllerTest
```

## 扩展性

- 支持新增字段类型，只需在字段验证方法中添加相应逻辑
- 支持自定义导出格式，可扩展`buildExportWorkbook`方法
- 支持复杂查询条件，可扩展`queryDataForExport`方法
