<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.base.EngineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.sys.engine.Engine">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="classify" jdbcType="VARCHAR" property="classify"/>
        <result column="instance" jdbcType="VARCHAR" property="instance"/>
        <result column="engine_url" jdbcType="VARCHAR" property="engineUrl"/>
        <result column="engine_login_name" jdbcType="VARCHAR" property="engineLoginName"/>
        <result column="engine_password" jdbcType="VARCHAR" property="enginePassword"/>
        <result column="engine_token" jdbcType="VARCHAR" property="engineToken"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="host_id" jdbcType="BIGINT" property="hostId"/>
        <result column="engine_ip" jdbcType="VARCHAR" property="engineIp"/>
        <result column="patrol_strategy" jdbcType="INTEGER" property="patrolStrategy"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`create_time`,`created_by`,`update_time`,`updated_by`,
        `name`, `state`, `description`, `classify`, `instance`, `engine_url`, `engine_login_name`, `engine_password`, `engine_token`, `parent_id`, `host_id`, `engine_ip`, `patrol_strategy`, `org_id`
    </sql>

    <select id="findByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        `sys_engine`
        where (
        (org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or org_id = #{cm.orgId, jdbcType=BIGINT})
        or exists (
        select
        seo.`engine_id`
        from
        `sys_engine_org` seo
        where
        seo.`engine_id` = `id`
        and (seo.org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or seo.org_id = #{cm.orgId, jdbcType=BIGINT})))
        and `instance` != 'JENKINS_SLAVE'
    </select>
</mapper>
