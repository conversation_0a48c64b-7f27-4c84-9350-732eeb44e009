<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.base.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.base.entity.rbac.role.Role">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="readonly" jdbcType="BIT" property="readonly"/>
        <result column="ds_type" jdbcType="VARCHAR" property="dsType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`updated_by`,`update_time`,
        `name`, `code`, `description`, `state`, `readonly`, `ds_type`
    </sql>

    <select id="findByUserId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `sys_role` sr where exists (select sur.`user_id` from `sys_user_role` sur where sur.`role_id` = sr.`id` and
        sur.`user_id` = #{userId, jdbcType=BIGINT}) and sr.`state` = true
    </select>


</mapper>
