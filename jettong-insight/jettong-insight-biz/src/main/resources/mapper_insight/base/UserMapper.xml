<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.base.UserMapper">

    <select id="selectIdByOrgIds" resultType="java.lang.Long">
        select `id`
        from `sys_user`
        where `org_id` in (select `child_id` from `sys_org_child` where `parent_id` in
        <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
            #{orgId}
        </foreach>
        )
        or `org_id` in
        <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
            #{orgId}
        </foreach>
    </select>
    <select id="selectCountByOrgId" resultType="java.lang.Long">
        select count(id)
        from `sys_user`
        where `org_id` in (select `child_id` from `sys_org_child` where `parent_id` = #{orgId})
            or org_id = #{orgId}
    </select>

    <select id="findPage" resultType="com.jettech.jettong.insight.vo.base.UserComponentResult">
        select *
        from sys_user
        <where>
            <if test="user.orgId != null">
                and `org_id` in (select `child_id` from `sys_org_child` where `parent_id` = #{user.orgId})
                or org_id = #{user.orgId}
            </if>
        </where>

    </select>


</mapper>
