<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.alm.ProductInfoMapper">

    <select id="selectIdByProductsetIds" resultType="java.lang.Long">
        select `product_id` from `product_productset` where `productset_id` in
        <foreach close=")" collection="productsetIds" item="productsetId" open="(" separator=",">
            #{productsetId}
        </foreach>
    </select>


</mapper>
