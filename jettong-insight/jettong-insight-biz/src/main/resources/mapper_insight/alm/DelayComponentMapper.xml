<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.alm.DelayComponentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.insight.vo.alm.DelayComponentResult">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type_classify" jdbcType="VARCHAR" property="typeClassify"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="plan_etime" jdbcType="TIMESTAMP" property="planEtime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`code`,`name`,`type_classify`,`type_code`,
        `plan_etime`
    </sql>


    <select id="findDelayByProjectId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from (
        select id, 'ISSUE' type_classify, type_code,code,name,plan_etime from issue_requirement
        where plan_etime &lt; now() and end_time is NULL and project_id = #{projectId, jdbcType=BIGINT}
        union all
        select id, 'TASK' type_classify, type_code,code,name,plan_etime from issue_task
        where plan_etime &lt; now() and end_time is NULL and project_id = #{projectId, jdbcType=BIGINT}
        union all
        select id, 'BUG' type_classify, type_code,code,name,plan_etime from issue_bug
        where plan_etime &lt; now() and end_time is NULL and project_id = #{projectId, jdbcType=BIGINT}
        ) t order by t.plan_etime
    </select>

</mapper>
