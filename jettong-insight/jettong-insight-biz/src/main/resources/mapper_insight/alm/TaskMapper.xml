<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.alm.TaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.alm.issue.entity.Task">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="LONGVARCHAR" property="description"/>
        <result column="state_code" jdbcType="VARCHAR" property="stateCode"/>
        <result column="priority_code" jdbcType="VARCHAR" property="priorityCode"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="plan_id" jdbcType="BIGINT" property="planId"/>
        <result column="requirement_id" jdbcType="BIGINT" property="requirementId"/>
        <result column="bug_id" jdbcType="BIGINT" property="bugId"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="delay" jdbcType="BIT" property="delay"/>
        <result column="rate_progress" jdbcType="INTEGER" property="rateProgress"/>
        <result column="estimate_hour" jdbcType="DOUBLE" property="estimateHour"/>
        <result column="used_hour" jdbcType="DOUBLE" property="usedHour"/>
        <result column="leading_by" jdbcType="BIGINT" property="leadingBy"/>
        <result column="handle_by" jdbcType="BIGINT" property="handleBy"/>
        <result column="plan_stime" jdbcType="TIMESTAMP" property="planStime"/>
        <result column="plan_etime" jdbcType="TIMESTAMP" property="planEtime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="source_code" jdbcType="VARCHAR" property="sourceCode"/>
        <result column="product_version_id" jdbcType="BIGINT" property="productVersionId"/>
        <result column="product_module_function_id" jdbcType="BIGINT" property="productModuleFunctionId"/>
        <result column="product_repair_version_id" jdbcType="BIGINT" property="productRepairVersionId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`update_time`,`updated_by`,
        `parent_id`, `code`, `name`, `description`, `state_code`, `priority_code`, `project_id`, `plan_id`,
        `requirement_id`, `bug_id`, `type_code`, `delay`, `rate_progress`, `estimate_hour`, `used_hour`,
        `leading_by`, `handle_by`, `plan_stime`, `plan_etime`, `start_time`, `end_time`,`source_code`,
        `product_version_id`, `product_module_function_id`,`product_repair_version_id`
    </sql>

    <select id="queryCountByQuery" resultType="java.lang.Integer">
            select count(id) from issue_task
        <include refid="queryCount"/>
    </select>

    <select id="queryCountTypeByQuery" resultType="java.util.Map">

        select a.num,b.name,b.code,b.color from (
        select count(id) as num,type_code as typeCode from issue_task
        <include refid="queryCount"/>
        group by type_code
        ) a left join  issue_type b on a.typeCode = b.code

    </select>

    <select id="querySumEstimateHour" resultType="com.jettech.jettong.insight.vo.alm.IssueHourCountComponentResult">
    select sum(x.estimate_hour ) as estimateHour,
        SUM(x.completeHour) AS completeHour
    from (
         select sum(estimate_hour) as estimate_hour,
        SUM(CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END) AS completeHour
         from issue_task
         <include refid="queryCount"/>
         <include refid="querySumEstimateHour"/>
        union all
        select sum(estimate_hour)  as estimate_hour,
        SUM(CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END) AS completeHour
        from issue_bug
        <include refid="queryCount"/>
        <include refid="querySumEstimateHour"/>
        union all
        select sum(estimate_hour)  as estimate_hour,
        SUM(CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END) AS completeHour
         from issue_requirement
        <include refid="queryCount"/>
        <include refid="querySumEstimateHour"/>
        )x
    </select>


    <select id="queryFilledHour" resultType="java.lang.Double">

        select sum(duration) from working_hours_info
        where
          (isnull(valid) or valid = 1 )
        <if test="query.projectIds != null and query.projectIds.size()>0">
            and project_id in
                <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                     #{projectId}
                </foreach>
        </if>
        <if test="query.orgIds != null and query.orgIds.size()>0">
            and filled_by in (select id from sys_user where org_id in ( select child_id from sys_org_child where
            parent_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
            )
             or org_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach> )
        </if>
        <if test="query.startDate != null and query.endDate != null">
            and (filling_time &gt;= #{query.startDate} and filling_time &lt;= #{query.endDate})
        </if>
    </select>

    <select id="queryFilledHourByProducset" resultType="java.lang.Double">
        select sum(x.duration ) from (
        select sum(duration) as duration  from working_hours_info whi
            left join issue_task it on whi.biz_id = it.id
        where
        (isnull(whi.valid) or whi.valid = 1 )
         and whi.type='TASK' and it.product_id in ( select product_id from product_productset where productset_id in
                    <foreach collection="query.productsetIds" item="productsetId" open="(" separator="," close=")">
                        #{productsetId}
                    </foreach>
             )
        <if test="query.startDate != null and query.endDate != null">
            and (filling_time &gt;= #{query.startDate} and filling_time &lt;= #{query.endDate})
        </if>
        union all
        select sum(duration) as duration  from working_hours_info whi
            left join issue_bug ib on whi.biz_id = ib.id
        where
        (isnull(whi.valid) or whi.valid = 1 )
         and whi.type='BUG' and ib.product_id in ( select product_id from product_productset where productset_id in
                <foreach collection="query.productsetIds" item="productsetId" open="(" separator="," close=")">
                    #{productsetId}
                </foreach>
             )
        <if test="query.startDate != null and query.endDate != null">
            and (filling_time &gt;= #{query.startDate} and filling_time &lt;= #{query.endDate})
        </if>
        union all
        select sum(duration) as duration  from working_hours_info whi
            left join issue_requirement ir on whi.biz_id = ir.id
        where
        (isnull(whi.valid) or whi.valid = 1 )
         and whi.type='ISSUE' and ir.product_id in ( select product_id from product_productset where productset_id  in
            <foreach collection="query.productsetIds" item="productsetId" open="(" separator="," close=")">
                #{productsetId}
            </foreach>
        )
        <if test="query.startDate != null and query.endDate != null">
             and (filling_time &gt;= #{query.startDate} and filling_time &lt;= #{query.endDate})
        </if>
    ) x
    </select>

    <select id="querySumEstimateHourGroupBy" resultType="java.util.Map">
        select sum(x.estimate_hour ) as estimateHour,
        SUM(x.completeHour) AS completeHour
        <if test="query.projectIds != null and query.projectIds.size()>0 and query.queryType!=-1">,project_id as bizId</if>
        <if test="query.queryType==-1">,handle_by as bizId</if>
        from (
        select   estimate_hour,
         CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END  AS completeHour ,project_id ,handle_by
        from issue_task
        <include refid="queryCount"/>
        <include refid="querySumEstimateHour"/>

        union all
        select  estimate_hour,
         CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END  AS completeHour,project_id ,handle_by
        from issue_bug
        <include refid="queryCount"/>
        <include refid="querySumEstimateHour"/>
        union all
        select  estimate_hour,
         CASE WHEN isnull(end_time)  THEN 0 ELSE estimate_hour END  AS completeHour,project_id ,handle_by
        from issue_requirement
        <include refid="queryCount"/>
        <include refid="querySumEstimateHour"/>
        )x  group by
        <if test="query.projectIds != null and query.projectIds.size()>0 and query.queryType!=-1">x.project_id</if>
        <if test="query.queryType==-1">x.handle_by</if>
    </select>


    <select id="queryFilledHourGroupBy" resultType="java.util.Map">
        select sum(duration) as filledHour <if test="query.projectIds != null and query.projectIds.size()>0 and query.queryType!=-1">,project_id as bizId</if>
        <if test="query.queryType==-1">,filled_by as bizId</if>
        from working_hours_info
        where
        (isnull(valid) or valid = 1 )
        <if test="query.projectIds != null and query.projectIds.size()>0">
            and project_id in
            <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="query.userIds != null and query.userIds.size()>0">
            and filled_by in
            <foreach collection="query.userIds" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        <if test="query.orgIds != null and query.orgIds.size()>0">
            and filled_by in (select id from sys_user where org_id in ( select child_id from sys_org_child where
            parent_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
            )
            or org_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach> )
        </if>
        <if test="query.startDate != null and query.endDate != null">
            and (filling_time &gt;= #{query.startDate} and filling_time &lt;= #{query.endDate})
        </if>
        group by
        <if test="query.projectIds != null and query.projectIds.size()>0 and query.queryType!=-1">project_id</if>
        <if test="query.queryType==-1">filled_by</if>
    </select>

    <sql id="querySumEstimateHour">
            <if test="query.startDate != null and query.endDate != null">
                and (plan_stime &gt;= #{query.startDate} and plan_stime &lt;= #{query.endDate})
                and (plan_etime &gt;= #{query.startDate} and plan_etime &lt;= #{query.endDate})
            </if>
        and !isnull(estimate_hour)
    </sql>

    <sql id="queryCount">
        where 1=1
        <if test="query.userIds != null and query.userIds.size()>0">
            and handle_by in
            <foreach collection="query.userIds" item="userId" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
        <if test="query.projectIds != null and query.projectIds.size()>0">
            and project_id in
            <foreach collection="query.projectIds" item="projectId" open="(" separator="," close=")">
                #{projectId}
            </foreach>
        </if>
        <if test="query.orgIds != null and query.orgIds.size()>0">
            and handle_by in (select id from sys_user where org_id in ( select child_id from sys_org_child where
            parent_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach>
            )
            or org_id in
            <foreach collection="query.orgIds" item="orgId" separator="," open="(" close=")">
                #{orgId}
            </foreach> )
        </if>
        <if test="query.productsetIds != null and query.productsetIds.size()>0">
            and product_id in ( select product_id from product_productset where productset_id  in
            <foreach collection="query.productsetIds" item="productsetId" open="(" separator="," close=")">
                #{productsetId}
            </foreach>
            )
        </if>
        <if test="query.queryType == 0">
            and ((plan_stime &lt;= #{query.startDate} and (isnull(end_time) or end_time &gt;=#{query.startDate}) )
            or (plan_stime &gt;= #{query.startDate} and plan_stime &lt;= #{query.endDate})
            )
        </if>
        <if test="query.queryType == 1">
            and  (plan_stime &gt;= #{query.startDate} and plan_stime &lt;= #{query.endDate})
        </if>
        <if test="query.queryType == 2">
            and  (end_time &gt;= #{query.startDate} and end_time &lt;= #{query.endDate})
        </if>
        <if test="query.typeCode != null">
            and  type_code = #{query.typeCode}
        </if>
    </sql>


    <select id="queryWorkItemPageByQuery" resultType="com.jettech.jettong.alm.issue.dto.WorkItemDTO">
        select  id ,'TASK' source_type,code,name,state_code,plan_etime,plan_stime,type_code,project_id,product_id,leading_by,handle_by, estimate_hour,used_hour,priority_code,created_by as put_by,create_time from issue_task
        <include refid="queryCount"/>
        and (
        (((create_time &lt;= #{query.startDate} AND (`plan_etime` >=  #{query.startDate} OR `end_time` IS NULL)) OR (create_time >=  #{query.startDate} AND create_time &lt;= #{query.endDate} )) AND `rate_progress` = 0)
            or((create_time &lt;= #{query.startDate} AND `end_time` IS NULL) AND `rate_progress` > 0)
            or(`end_time` BETWEEN #{query.startDate} AND #{query.endDate})
        )
        union all
        select id ,'BUG' source_type,code,name,state_code,plan_etime,plan_stime,type_code,project_id,product_id,leading_by,handle_by, estimate_hour,used_hour,priority_code,put_by,create_time from issue_bug
        <include refid="queryCount"/>
        and (
        ( create_time BETWEEN #{query.startDate} AND #{query.endDate} OR (`end_time` >= #{query.startDate} AND create_time &lt; #{query.startDate}))
        or(create_time &lt;= #{query.endDate} AND `end_time` BETWEEN #{query.startDate} AND #{query.endDate})
        or(`end_time` BETWEEN #{query.startDate} AND #{query.endDate})
        )
        union all
        select id ,'ISSUE' source_type,code,name,state_code,plan_etime,plan_stime,type_code,project_id,product_id,leading_by,handle_by, estimate_hour,used_hour,priority_code,put_by,create_time   from issue_requirement
        <include refid="queryCount"/>
        and (
        (create_time BETWEEN #{query.startDate} AND #{query.endDate} OR (`end_time` >= #{query.startDate} AND create_time &lt; #{query.startDate}))
        or(create_time &lt; #{query.endDate} AND `end_time` BETWEEN #{query.startDate} AND #{query.endDate})
        or(`end_time` BETWEEN #{query.startDate} AND #{query.endDate})
        )
    </select>
</mapper>
