<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.alm.ProjectInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.alm.project.entity.ProjectInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="leading_by" jdbcType="BIGINT" property="leadingBy"/>
        <result column="program_id" jdbcType="BIGINT" property="programId"/>

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`update_time`,`updated_by`,
        `code`, `name`, `description`, `type_code`, `org_id`, `leading_by`, `program_id`
    </sql>


    <select id="findByUserId" parameterType="long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `project_info` pi where (exists (select pu.`project_id` from `project_user` pu where pu.`project_id` =
        pi.`id` and
        pu.`user_id` = #{userId, jdbcType=BIGINT}) or pi.`created_by` = #{userId, jdbcType=BIGINT})
    </select>

    <select id="findUserProject" resultType="com.jettech.jettong.insight.vo.alm.UserProjectResult">

        select pur.user_id, pur.project_id, pi.name as projectName, p.name as productName, p.id as productId from project_user_role pur left join project_info pi on pur.project_id=pi.id
        left join project_product pp on pi.id = pp.project_id
        left join  product_info p on pp.product_id = p.id
            where pur.user_id in
        <foreach collection="userIds" item="userId" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </select>

</mapper>
