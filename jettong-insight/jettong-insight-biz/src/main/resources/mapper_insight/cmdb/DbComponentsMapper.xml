<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.cmdb.DbComponentsMapper">

    <select id="findCountByOrgId" parameterType="long" resultType="int">
        select count(*)
        from `cmdb_db_components`
        where (
                      (org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or org_id = #{cm.orgId, jdbcType=BIGINT})
                      or exists(
                              select caco.`db_components_id`
                              from `cmdb_db_components_org` caco
                              where caco.`db_components_id` = `id`
                                and (caco.org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or caco.org_id = #{cm.orgId, jdbcType=BIGINT})))
    </select>
</mapper>
