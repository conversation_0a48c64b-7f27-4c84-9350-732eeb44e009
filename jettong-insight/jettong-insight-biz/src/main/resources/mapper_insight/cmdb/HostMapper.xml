<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.cmdb.HostMapper">

    <select id="findStateByOrgId" parameterType="long" resultType="boolean">
        select state
        from `cmdb_host`
        where (
                      (org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or org_id = #{cm.orgId, jdbcType=BIGINT})
                      or exists(
                              select cho.`host_id`
                              from `cmdb_host_org` cho
                              where cho.`host_id` = `id`
                                and (cho.org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or cho.org_id = #{cm.orgId, jdbcType=BIGINT})))
    </select>

</mapper>
