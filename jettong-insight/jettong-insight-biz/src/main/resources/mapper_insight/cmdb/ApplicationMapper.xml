<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.cmdb.ApplicationMapper">

    <select id="findCountByOrgId" parameterType="long" resultType="int">
        select count(*)
        from `cmdb_application`
        where (
                      (org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or org_id = #{cm.orgId, jdbcType=BIGINT})
                      or exists(
                              select cao.`application_id`
                              from `cmdb_application_org` cao
                              where cao.`application_id` = `id`
                                and (cao.org_id in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or cao.org_id = #{cm.orgId, jdbcType=BIGINT})))
    </select>

</mapper>
