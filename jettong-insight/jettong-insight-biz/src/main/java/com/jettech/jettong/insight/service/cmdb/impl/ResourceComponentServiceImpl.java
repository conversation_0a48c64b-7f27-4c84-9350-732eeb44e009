package com.jettech.jettong.insight.service.cmdb.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.jettong.insight.dao.cmdb.AppComponentsMapper;
import com.jettech.jettong.insight.dao.cmdb.ApplicationMapper;
import com.jettech.jettong.insight.dao.cmdb.DbComponentsMapper;
import com.jettech.jettong.insight.dao.cmdb.HostMapper;
import com.jettech.jettong.insight.service.cmdb.ResourceComponentService;
import com.jettech.jettong.insight.vo.cmdb.ResourceComponentResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 资源组件业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 资源组件业务处理层
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.cmdb.impl
 * @className ResourceComponentServiceImpl
 * @date 2021/12/2 17:58
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class ResourceComponentServiceImpl implements ResourceComponentService
{
    private final ApplicationMapper applicationMapper;

    private final HostMapper hostMapper;

    private final AppComponentsMapper appComponentsMapper;

    private final DbComponentsMapper dbComponentsMapper;

    @Override
    public ResourceComponentResult findResourceByOrgId(Long orgId)
    {
        // 查询服务应用数量
        int applicationNum = applicationMapper.findCountByOrgId(orgId);

        // 查询服务器状态集合
        List<Boolean> hostStates = hostMapper.findStateByOrgId(orgId);

        Integer[] hostNum = new Integer[2];
        hostNum[0] = hostStates.stream().filter(item -> item).collect(
                Collectors.toList()).size();
        hostNum[1] = hostStates.stream().filter(item -> !item).collect(
                Collectors.toList()).size();

        // 查询应用组件数量
        int appComponentNum = appComponentsMapper.findCountByOrgId(orgId);

        // 查询数据库组件数量
        int dbComponentNum = dbComponentsMapper.findCountByOrgId(orgId);

        return ResourceComponentResult.builder().applicationNum(applicationNum).hostNum(hostNum)
                .appComponentNum(appComponentNum).dbComponentNum(dbComponentNum).build();
    }
}
