package com.jettech.jettong.insight.service.base;

import com.jettech.jettong.base.entity.sys.engine.Engine;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.base
 * @className EngineComponentService
 * @date 2021/12/3 10:19
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface EngineComponentService
{
    /**
     * 查询当前登录人所在机构下的所有引擎信息
     *
     * @param orgId 机构id
     * @return List<Engine> 引擎信息
     * <AUTHOR>
     * @date 2021/12/3 10:20
     * @update zxy 2021/12/3 10:20
     * @since 1.0
     */
    List<Engine> findDashboardByOrgId(Long orgId);
}
