package com.jettech.jettong.insight.service.base;

import com.jettech.jettong.base.entity.sys.log.OptLog;

import java.util.List;

/**
 * 用户操作组件业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户操作组件业务处理层接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.base
 * @className UserOptComponentService
 * @date 2021/12/2 14:38
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserOptComponentService
{
    /**
     * 根据用户id查询用户操作组件信息
     *
     * @param userId 用户id
     * @return List<OptLog> 用户操作组件信息
     * <AUTHOR>
     * @date 2021/12/2 15:31
     * @update zxy 2021/12/2 15:31
     * @since 1.0
     */
    List<OptLog> findOptByUserId(Long userId);
}
