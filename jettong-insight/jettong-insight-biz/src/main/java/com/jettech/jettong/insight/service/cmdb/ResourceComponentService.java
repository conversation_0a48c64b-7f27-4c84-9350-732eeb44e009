package com.jettech.jettong.insight.service.cmdb;

import com.jettech.jettong.insight.vo.cmdb.ResourceComponentResult;

/**
 * 资源组件业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 资源组件业务处理层接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.cmdb
 * @className TaskComponentService
 * @date 2021/12/2 17:56
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface ResourceComponentService
{

    /**
     * 根据机构id查询资源统计数据
     *
     * @param orgId 机构id
     * @return ResourceComponentResult 资源统计数据
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    ResourceComponentResult findResourceByOrgId(Long orgId);

}
