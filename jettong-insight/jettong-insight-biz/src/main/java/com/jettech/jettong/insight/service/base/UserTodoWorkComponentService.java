package com.jettech.jettong.insight.service.base;

import com.jettech.jettong.insight.vo.base.UserTodoWorkComponentResult;

import java.time.LocalDate;
import java.util.List;

/**
 * 用户待办事项组件业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户待办事项组件业务处理层接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.base
 * @className UserTodoWorkComponentService
 * @date 2021/12/2 14:38
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserTodoWorkComponentService
{
    /**
     * 根据时间区间和用户id查询用户待办事项组件信息
     *
     * @param userId 用户id
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return List<UserTodoWorkComponentResult> 用户待办事项组件信息
     * <AUTHOR>
     * @date 2021/12/2 15:31
     * @update zxy 2021/12/2 15:31
     * @since 1.0
     */
    List<UserTodoWorkComponentResult> findTodoWorkByUserId(Long userId, LocalDate startDate, LocalDate endDate);
}
