package com.jettech.jettong.insight.service.base.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.DateUtils;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.insight.dao.alm.*;
import com.jettech.jettong.insight.service.base.UserTodoWorkComponentService;
import com.jettech.jettong.insight.vo.base.UserTodoWorkComponentResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户待办事项组件业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户待办事项组件业务处理层
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.base.impl
 * @className UserTodoWorkComponentServiceImpl
 * @date 2021/12/2 14:39
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class UserTodoWorkComponentServiceImpl implements UserTodoWorkComponentService
{

    private final RequirementMapper requirementMapper;

    private final ProjectInfoMapper projectInfoMapper;

    private final TaskMapper taskMapper;

    private final BugMapper bugMapper;

    private final TypeMapper typeMapper;

    private final StateMapper stateMapper;

    @Override
    public List<UserTodoWorkComponentResult> findTodoWorkByUserId(Long userId, LocalDate startDate, LocalDate endDate)
    {
        // 查询所有事项分类
        List<Type> types = typeMapper.selectList(Wraps.lbQ());
        Map<String, Type> typeCodeMap = types.stream().collect(Collectors.toMap(Type::getCode, type -> type));
        // 查询所有事项状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());
        Map<String, State> stateCodeMap =
                states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        // 查询待办需求
        List<Requirement> requirements = requirementMapper.selectList(
                Wraps.<Requirement>lbQ().eq(Requirement::getHandleBy, userId).isNull(Requirement::getEndTime)
                        .between(Requirement::getPlanEtime, startDate, endDate));
        List<UserTodoWorkComponentResult.TodoWork> requirementTodoWorks =
                BeanPlusUtil.toBeanList(requirements, UserTodoWorkComponentResult.TodoWork.class);

        requirementTodoWorks.forEach(
                item -> item.setClassifyCode(TypeClassify.ISSUE.getCode()).setClassifyName(TypeClassify.ISSUE.getDesc())
                        .setTypeName(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getName() : item.getTypeCode())
                        .setTypeIcon(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getIcon() : item.getTypeCode())
                        .setTypeColor(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getColor() : item.getTypeCode())
                        .setStateName(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getName() : item.getStateCode())
                        .setStateColor(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getColor() : item.getStateCode()));

        // 查询待办任务
        List<Task> tasks = taskMapper.selectList(
                Wraps.<Task>lbQ().eq(Task::getHandleBy, userId).isNull(Task::getEndTime)
                        .between(Task::getPlanEtime, startDate, endDate));
        tasks.forEach(item -> item.setParentId(
                item.getParentId() == null ? (item.getBugId() == null ? item.getRequirementId() : item.getBugId()) :
                        item.getParentId()));
        List<UserTodoWorkComponentResult.TodoWork> taskTodoWorks =
                BeanPlusUtil.toBeanList(tasks, UserTodoWorkComponentResult.TodoWork.class);

        taskTodoWorks.forEach(
                item -> item.setClassifyCode(TypeClassify.TASK.getCode()).setClassifyName(TypeClassify.TASK.getDesc())
                        .setTypeName(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getName() : item.getTypeCode())
                        .setTypeIcon(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getIcon() : item.getTypeCode())
                        .setTypeColor(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getColor() : item.getTypeCode())
                        .setStateName(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getName() : item.getStateCode())
                        .setStateColor(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getColor() : item.getStateCode()));

        // 查询待办缺陷
        List<Bug> bugs = bugMapper.selectList(Wraps.<Bug>lbQ().eq(Bug::getHandleBy, userId).isNull(Bug::getEndTime)
                .between(Bug::getPlanEtime, startDate, endDate));

        List<UserTodoWorkComponentResult.TodoWork> bugTodoWorks = new ArrayList<>();

        bugs.forEach(
                item -> bugTodoWorks.add(UserTodoWorkComponentResult.TodoWork.builder().id(item.getId())
                        .parentId(item.getRequirementId())
                        .projectId(item.getProjectId())
                        .code(item.getCode())
                        .name(item.getName())
                        .description(item.getDescription())
                        .classifyCode(TypeClassify.BUG.getCode())
                        .classifyName(TypeClassify.BUG.getDesc())
                        .typeCode(item.getTypeCode())
                        .typeName(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getName() : item.getTypeCode())
                        .typeIcon(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getIcon() : item.getTypeCode())
                        .typeColor(typeCodeMap.containsKey(item.getTypeCode()) ?
                                typeCodeMap.get(item.getTypeCode()).getColor() : item.getTypeCode())
                        .stateCode(item.getStateCode())
                        .stateName(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getName() : item.getStateCode())
                        .stateColor(stateCodeMap.containsKey(item.getStateCode()) ?
                                stateCodeMap.get(item.getStateCode()).getColor() : item.getStateCode())
                        .planEtime(item.getPlanEtime()).build()));

        List<UserTodoWorkComponentResult.TodoWork> allTodoWorks = new ArrayList<>();
        allTodoWorks.addAll(requirementTodoWorks);
        allTodoWorks.addAll(taskTodoWorks);
        allTodoWorks.addAll(bugTodoWorks);

        // 查询项目信息
        List<Long> projectIds = allTodoWorks.stream().map(UserTodoWorkComponentResult.TodoWork::getProjectId).collect(
                Collectors.toList());
        if (!projectIds.isEmpty())
        {
            List<ProjectInfo> projectInfos = projectInfoMapper.selectBatchIds(projectIds);

            Map<Long, ProjectInfo> projectInfoMap =
                    projectInfos.stream().collect(Collectors.toMap(ProjectInfo::getId, projectInfo -> projectInfo));

            allTodoWorks.forEach(item -> item.setProjectInfo(projectInfoMap.get(item.getProjectId())));
        }
        allTodoWorks.sort(Comparator.comparing(UserTodoWorkComponentResult.TodoWork::getPlanEtime));

        // 按计划完成时间分组
        Map<String, List<UserTodoWorkComponentResult.TodoWork>> todoWorkMap = allTodoWorks.stream()
                .collect(Collectors.groupingBy(item -> DateUtils.formatAsDate(item.getPlanEtime())));
        List<UserTodoWorkComponentResult> userTodoWorkComponentResults = new LinkedList<>();

        todoWorkMap.forEach((key, value) -> userTodoWorkComponentResults.add(UserTodoWorkComponentResult.builder()
                .date(key).data(value).build()));

        return userTodoWorkComponentResults;
    }
}
