package com.jettech.jettong.insight.service.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.jettong.base.dto.rbac.user.UserPageQuery;
import com.jettech.jettong.insight.vo.base.OrgDetailComponentResult;
import com.jettech.jettong.insight.vo.base.UserComponentResult;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 用户组件业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户组件业务处理层接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.base
 * @className UserComponentService
 * @date 2021/12/2 14:38
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserComponentService
{
    /**
     * 根据用户id查询用户组件信息
     *
     * @param userId 用户id
     * @return UserComponentResult 用户组件信息
     * <AUTHOR>
     * @date 2021/12/2 15:31
     * @update zxy 2021/12/2 15:31
     * @since 1.0
     */
    UserComponentResult findDashboardByUserId(Long userId);

    List<OrgDetailComponentResult> findOrgDetailByOrgIds(@RequestParam List<Long> orgIds);

    IPage<UserComponentResult> findPage(IPage<UserComponentResult> page, UserPageQuery userPageQuery);
}
