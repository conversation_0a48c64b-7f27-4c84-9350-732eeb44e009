package com.jettech.jettong.alm.controller.echo;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.jettech.basic.annotation.base.IgnoreResponseBodyAdvice;
import com.jettech.basic.base.R;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.issue.dto.StateTransitionDTO;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.*;
import com.jettech.jettong.alm.issue.vo.BugReportVO;
import com.jettech.jettong.alm.project.dto.ProjectInfoSaveDTO;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectProduct;
import com.jettech.jettong.alm.project.entity.ProjectProgram;
import com.jettech.jettong.alm.project.entity.ProjectUser;
import com.jettech.jettong.alm.project.service.*;
import com.jettech.jettong.alm.project.vo.ProjectPlanItemResult;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.alm.workflow.service.WorkflowNodeService;
import com.jettech.jettong.base.entity.sys.form.CustomFormField;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.testm.entity.TestPlan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jettech.basic.context.ContextUtil.getUserId;

/**
 * 数据注入查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据注入查询接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.product.controller.echo
 * @className EchoController
 * @date 2021/11/11 10:30
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/alm")
@IgnoreResponseBodyAdvice
@Api(value = "数据注入查询接口", tags = "数据注入查询接口， 不建议前端调用")
@ApiIgnore
public class EchoController
{
    private final RequirementService requirementService;
    private final TestreqService testreqService;
    private final IdeaService ideaService;
    private final ProjectPlanService projectPlanService;
    private final ProjectInfoService projectInfoService;
    private final TypeService typeService;
    private final ProjectUserService projectUserService;
    private final ProjectProgramService projectProgramService;
    private final PriorityService priorityService;
    private final BugService bugService;
    private final RiskService riskService;
    private final ProjectCustomFormService projectCustomFormService;
    private final DelayComponentService delayComponentService;
    private final ProjectProductService projectProductService;
    private final TaskService taskService;
    private final IssueRequirementTestCaseService issueRequirementTestCaseService;
    private final ProjectWorkflowService projectWorkflowService;
    private final IssueItemService issueItemService;
    private final StateService stateService;
    private final WorkflowNodeService workflowNodeService;
    private final EchoService echoService;
    private final IssueTestreqProductService issueTestreqProductService;


    @GetMapping("/projectWorkflow/echo/findWorkflowNode")
    public Map<String, List<WorkflowNode>> findWorkflowNode(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam("typeClassify") TypeClassify typeClassify) {
        List<Type> typeList = typeService.findByClassify(typeClassify);
        List<String> types = typeList.stream().map(Type::getCode).collect(Collectors.toList());

        return projectWorkflowService.findWorkflowNode(projectId, Collections.singletonMap(typeClassify, types));
    }

    /**
     * 获取项目ID关联的全部（主办和辅办）产品ID
     *
     * @param projectId 项目ID
     * @return  产品ID
     */
    @GetMapping("/projectProduct/echo/findAllProduct/{projectId}")
    List<ProductInfo> findAllProduct(@PathVariable("projectId") Long projectId) {
        return projectProductService.getAllProductInfoList(projectId);
    }

    /**
     * 获取项目ID关联的主办产品ID
     *
     * @param projectId 项目ID
     * @return  产品ID
     */
    @GetMapping("/projectProduct/echo/findHostProduct/{projectId}")
    ProductInfo findHostProduct(@PathVariable("projectId") Long projectId) {
        return projectProductService.getHostProductInfo(projectId);
    }

    /**
     * 获取项目ID关联的主办产品ID
     *
     * @param productId 产品ID
     * @return  项目
     */
    @GetMapping("/projectProduct/echo/findProject/{productId}")
    List<ProjectInfo> findProject(@PathVariable("productId") Long productId) {
        LbqWrapper<ProjectProduct> wrapper = Wraps.lbQ();
        wrapper.eq(ProjectProduct::getProductId, productId);
        List<ProjectProduct> list = projectProductService.list(wrapper);
        List<Long> projectIds = list.stream().map(ProjectProduct::getProjectId).collect(Collectors.toList());
        return projectInfoService.listByIds(projectIds);
    }

    @PostMapping("/issue/echo/selectRequirementByCondition")
    public List<Requirement> selectRequirementByCondition(@RequestBody Requirement requirement)
    {
        LbqWrapper<Requirement> wrapper = Wraps.lbQ();

        wrapper.eq(Requirement::getCode, requirement.getCode())
                .eq(Requirement::getProjectId, requirement.getProjectId())
                .eq(Requirement::getStateCode, requirement.getStateCode());

        return requirementService.list(wrapper);
    }
    @PostMapping("/issue/echo/selectTestReqByCondition")
    public List<Testreq> selectTestReqByCondition(@RequestBody Testreq testreq)
    {
        LbqWrapper<Testreq> wrapper = Wraps.lbQ();

        wrapper.eq(Testreq::getCode, testreq.getCode())
                .eq(Testreq::getProjectId, testreq.getProjectId())
                .eq(Testreq::getStateCode, testreq.getStateCode());

        return testreqService.list(wrapper);
    }

    @ApiOperation(value = "根据id查询用户需求", notes = "根据id查询用户需求")
    @GetMapping("/idea/echo/findByIds")
    public Map<Serializable, Object> findIdeaByIds(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return ideaService.findByIds(ids);
    }

    @ApiOperation(value = "根据id查询项目计划", notes = "根据id查询项目计划")
    @GetMapping("/projectPlan/echo/findByIds")
    public Map<Serializable, Object> findProjectPlanByIds(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return projectPlanService.findByIds(ids);
    }

    @ApiOperation(value = "根据id查询项目信息", notes = "根据id查询项目信息")
    @GetMapping("/project/echo/findByIds")
    public Map<Serializable, Object> findProjectInfoByIds(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return projectInfoService.findByIds(ids);
    }

    @ApiOperation(value = "根据id查询项目信息", notes = "根据id查询项目信息")
    @GetMapping("/project/echo/findById")
    public ProjectInfo findById(@RequestParam(value = "id") Long id)
    {
        return projectInfoService.findById(id);
    }

    @ApiOperation(value = "根据需求事项查询信息", notes = "根据需求事项查询信息")
    @GetMapping("/issueType/echo/findByCodes")
    public Map<Serializable, Object> findByCodes(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return typeService.findByIds(ids);
    }
    @ApiOperation(value = "根据需求事项查询信息", notes = "根据需求事项查询信息")
    @GetMapping("/priority/echo/findAllStateCodeByCode")
    public Map<String, State> findAllStateCodeByCode(@RequestParam(value = "code") String code)
    {
        LbqWrapper<State> wrapper = Wraps.<State>lbQ();
        wrapper.eq(State::getCode, code);
        List<State> list = stateService.list(wrapper);
        Map<String, State> collect = list.stream().collect(Collectors.toMap(State::getCode, Function.identity()));
        return collect;
    }

    @ApiOperation(value = "根据id查询需求信息", notes = "根据id查询需求信息")
    @GetMapping("/issue/echo/getRequirement")
    public Requirement getRequirement(@RequestParam(value = "id") Long id)
    {
        return requirementService.getById(id);
    }


    @ApiOperation(value = "根据id查询需求信息", notes = "根据id查询需求信息")
    @GetMapping("/testreq/echo/getTestreq")
    public Testreq getTestreq(@RequestParam(value = "id") Long id)
    {
        return testreqService.getById(id);
    }

    @ApiOperation(value = "根据条件批量查询项目或者项目集人员")
    @PostMapping("/project/projectUser/echo/selectProjectUserByCondition")
    public List<ProjectUser> selectProjectUserByCondition(@RequestBody ProjectUser projectUser)
    {
        LbqWrapper<ProjectUser> wrapper = Wraps.<ProjectUser>lbQ();
        wrapper.eq(ProjectUser::getProjectId, projectUser.getProjectId())
                .eq(ProjectUser::getProgramId, projectUser.getProgramId());
        List<ProjectUser> projectUsers = projectUserService.list(wrapper);
        return projectUsers;
    }

    @ApiOperation(value = "根据条件批量查询项目")
    @PostMapping("/project/echo/selectProjectByCondition")
    public List<ProjectInfo> selectProjectByCondition(@RequestBody ProjectInfo projectInfo)
    {
        Wrapper<ProjectInfo> wrapper = Wraps.q(projectInfo);
        return projectInfoService.list(wrapper);
    }

    @ApiOperation(value = "根据条件批量查询项目集")
    @PostMapping("/project/echo/selectProjectMByCondition")
    public List<ProjectProgram> selectProjectMByCondition(@RequestBody ProjectProgram projectProgram)
    {
        LbqWrapper<ProjectProgram> wrapper = Wraps.<ProjectProgram>lbQ();
        wrapper.eq(ProjectProgram::getName, projectProgram.getName());
        List<ProjectProgram> projectInfoList = projectProgramService.list(wrapper);
        return projectInfoList;
    }

    @ApiOperation(value = "查询所有优先级信息")
    @PostMapping("/project/echo/getAll")
    public List<Priority> getAll()
    {
        return priorityService.list(Wraps.lbQ());
    }


    @ApiOperation(value = "从缺陷分布维度查看测试报告", notes = "从缺陷分布维度查看测试报告")
    @GetMapping("/bug/echo/testReportOnBug")
    public List<BugReportVO> testReportOnBug(@RequestParam(value = "planIds") List<Long> planIds)
    {
        List<BugReportVO> bugReportVOS = bugService.queryBugReportVo(planIds);
        Long allBugNum = 0L;
        for (BugReportVO bugReportVO : bugReportVOS){

            if (Objects.equals(bugReportVO.getPriorityCode(), null)){

                bugReportVO.setPriorityCode("MEDIUM");
            }
            allBugNum = allBugNum + bugReportVO.getNum();
        }
        for (BugReportVO bugReportVO : bugReportVOS){

            bugReportVO.setRate(new Long(bugReportVO.getNum() / allBugNum *100).intValue());
        }
        if (!bugReportVOS.contains("HIGHEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGHEST");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("HIGH")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGH");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("MEDIUM")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("MEDIUM");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOW")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOW");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOWEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOWEST");
            bugStatusVO.setNum(0L);
        }
        return bugReportVOS;


//        for (Long t : planIds)
//        {
//            bugs.addAll(bugService.list(Wraps.<Bug>lbQ().eq(Bug::getProjectId, 0).eq(Bug::getTestPlanId, t)));
//        }
//        for (Bug t : bugs)
//        {
//            if (null == t.getPriorityCode())
//            {
//                t.setPriorityCode("MEDIUM");
//            }
//        }
//        Map<String, Long> map =
//                bugs.stream().collect(Collectors.groupingBy(Bug::getPriorityCode, Collectors.counting()));
//        List<BugReportVO> bugReportVOs = new ArrayList<>();
//        // 格式转换
//        for (String t : map.keySet())
//        {
//            BugReportVO bugReportVO = new BugReportVO();
//            Long num = map.get(t);
//            bugReportVO.setPriorityCode(t);
//            bugReportVO.setNum(num);
//            bugReportVO.setRate(new Long(num * 100 / bugs.size()).intValue());
//            bugReportVOs.add(bugReportVO);
//        }

//        // 解决占比和不为100%的问题
//        if (bugReportVOs.size() > 1)
//        {
//            int ratePart = 0;
//            for (int i = 0; i < bugReportVOs.size(); i++)
//            {
//                if (i != bugReportVOs.size() - 1)
//                {
//                    ratePart += bugReportVOs.get(i).getRate();
//                }
//                else
//                {
//                    bugReportVOs.get(i).setRate(100 - ratePart);
//                }
//            }
//        }
//        bugService.defaultBugReportVoData(bugReportVOs);
//        return bugReportVOs;
    }

    @ApiOperation(value = "从缺陷分布维度查看测试报告", notes = "从缺陷分布维度查看测试报告")
    @GetMapping("/bug/echo/testReportOnBugByTaskIds")
    public List<BugReportVO> testReportOnBugByTaskIds(@RequestParam(value = "taskIds") List<Long> taskIds)
    {
        List<BugReportVO> bugReportVOS = bugService.queryBugReportVoByTaskIds(taskIds);
        Long allBugNum = 0L;
        for (BugReportVO bugReportVO : bugReportVOS){

            if (Objects.equals(bugReportVO.getPriorityCode(), null)){

                bugReportVO.setPriorityCode("MEDIUM");
            }
            allBugNum = allBugNum + bugReportVO.getNum();
        }
        for (BugReportVO bugReportVO : bugReportVOS){

            bugReportVO.setRate(new Long(bugReportVO.getNum() / allBugNum *100).intValue());
        }
        if (!bugReportVOS.contains("HIGHEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGHEST");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("HIGH")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGH");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("MEDIUM")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("MEDIUM");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOW")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOW");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOWEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOWEST");
            bugStatusVO.setNum(0L);
        }
        return bugReportVOS;

    }

    @ApiOperation(value = "从缺陷分布维度查看测试报告", notes = "从缺陷分布维度查看测试报告")
    @GetMapping("/bug/echo/getOverview")
    public Map<String, Long> getOverview()
    {
        List<Bug> bugs = new ArrayList<>();
        bugs.addAll(bugService.list(Wraps.<Bug>lbQ().eq(Bug::getProjectId, 0)));
        for (Bug t : bugs)
        {
            if (null == t.getPriorityCode())
            {
                t.setPriorityCode("MEDIUM");
            }
        }
        Map<String, Long> map =
                bugs.stream().collect(Collectors.groupingBy(Bug::getPriorityCode, Collectors.counting()));
        return map;
    }

    @ApiOperation(value = "统计bug数据信息", notes = "统计bug数据信息")
    @GetMapping("/bug/echo/getBugData")
    public Map<String, Long> getBugData() {
        Map<String, Long> map = new HashMap<>();
        map.put("HIGHEST", bugService.count(Wraps.<Bug>lbQ().eq(Bug::getPriorityCode, "HIGHEST")));
        map.put("HIGH", bugService.count(Wraps.<Bug>lbQ().eq(Bug::getPriorityCode, "HIGH")));
        map.put("MEDIUM", bugService.count(Wraps.<Bug>lbQ().eq(Bug::getPriorityCode, "MEDIUM")));
        map.put("LOW", bugService.count(Wraps.<Bug>lbQ().eq(Bug::getPriorityCode, "LOW")));
        map.put("LOWEST", bugService.count(Wraps.<Bug>lbQ().eq(Bug::getPriorityCode, "LOWEST")));
        return map;
    }

    @ApiOperation(value = "根据优先级查询信息", notes = "根据优先级查询信息")
    @GetMapping("/priority/echo/findByCodes")
    public Map<Serializable, Object> findByPriorityCodes(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return priorityService.findByIds(ids);
    }

    @DeleteMapping("/projectCustomForm/echo/{customFormId}/removeCustomField")
    public void removeCustomField(@PathVariable("customFormId") Long customFormId, @RequestBody List<CustomFormField> customFormFields)
    {
        projectCustomFormService.removeCustomField(customFormId, customFormFields);
    }

    @PutMapping("/echo/advent")
    public R<Boolean> advent(@RequestParam TypeClassify classify, @RequestParam Long issueId) {

        Map<String, Object> issue = issueItemService.getIssue(classify, issueId);

        return R.success();
    }


    @ApiOperation(value = "计算延期工作项")
    @PutMapping("/issueItem/echo/calculationDelay")
    public R<Boolean> calculationDelay() {
        requirementService.calculationDelay();
        taskService.calculationDelay();
        bugService.calculationDelay();
        return R.success();
    }

    @ApiOperation(value = "根据事项ids获取事项集合", notes = "根据事项ids获取事项集合")
    @PostMapping("/issueItem/echo/getIssueItemList")
    public List<ProjectPlanItemResult> getIssueItemList(@RequestBody Map<String,Object> map)
    {
        ArrayList<ProjectPlanItemResult> results = new ArrayList<>();
        results.addAll(getBugWrapper(map));
        results.addAll(getTaskWrapper(map));
        results.addAll(getRequirementWrapper(map));
        results.addAll(getIdeaWrapper(map));
        return results;
    }

    @ApiOperation(value = "根据计划产工作项数量", notes = "根据计划产工作项数量")
    @GetMapping("/projectPlan/echo/getIssueItemCount")
    public Map<Long, Long> getIssueItemCount(@RequestParam(value = "productVersionIds") List<Long> productVersionIds) {

        Map<Long, Long> result =new HashMap<>();

        productVersionIds.forEach(vid -> {
            long requirementCount = requirementService.count(Wraps.<Requirement>lbQ()
                    .eq(Requirement::getProductVersionId, vid));
            long bugCount = bugService.count(Wraps.<Bug>lbQ().eq(Bug::getProductVersionId, vid));
            long riskCount = riskService.count(Wraps.<Risk>lbQ().eq(Risk::getProductVersionId, vid));
            long taskCount = taskService.count(Wraps.<Task>lbQ().eq(Task::getProductVersionId, vid));
            long count = requirementCount + bugCount + riskCount + taskCount;
            result.put(vid, count);
        });
        return result;
    }

    private List<ProjectPlanItemResult> getBugWrapper(Map<String,Object> map) {
        List<Bug> bugs=new ArrayList<>();
        List<Long> bugIds= (List<Long>) map.get("bugIds");
        if(!bugIds.isEmpty()){
            bugs = bugService.list(Wraps.<Bug>lbQ()
                    .in(Bug::getId, bugIds));
        }
        else
        {
            return new ArrayList<>();
        }
        return bugs.stream().map(bug -> {
            ProjectPlanItemResult result = BeanPlusUtil.toBean(bug, ProjectPlanItemResult.class);
            result.setBizId(bug.getId());
            result.setParentId(bug.getRequirementId());
            return result;
        }).collect(Collectors.toList());
    }

    private List<ProjectPlanItemResult> getTaskWrapper(Map<String,Object> map) {
        List<Task> tasks=new ArrayList<>();
        List<Long> taskIds= (List<Long>) map.get("taskIds");
        if(!taskIds.isEmpty()){
            tasks = taskService.list(Wraps.<Task>lbQ()
                    .in(Task::getId, taskIds));
        }
        else
        {
            return new ArrayList<>();
        }
        return tasks.stream().map(task -> {
            ProjectPlanItemResult result = BeanPlusUtil.toBean(task, ProjectPlanItemResult.class);
            result.setBizId(task.getId());
            Long parentId = task.getParentId() != null ? task.getParentId() :
                    (task.getBugId() != null ? task.getBugId() : task.getRequirementId());
            result.setParentId(parentId);
            return result;
        }).collect(Collectors.toList());
    }

    private List<ProjectPlanItemResult> getRequirementWrapper(Map<String,Object> map) {
        List<Requirement> requirements=new ArrayList<>();
        List<Long> requirementIds= (List<Long>) map.get("requirementIds");
        if(!requirementIds.isEmpty()) {
            requirements = requirementService.list(Wraps.<Requirement>lbQ()
                    .in(Requirement::getId, requirementIds));
        }
        else
        {
            return new ArrayList<>();
        }
        return requirements.stream().map(task -> {
            ProjectPlanItemResult result = BeanPlusUtil.toBean(task, ProjectPlanItemResult.class);
            result.setBizId(task.getId());
            return result;
        }).collect(Collectors.toList());
    }

    private List<ProjectPlanItemResult> getIdeaWrapper(Map<String,Object> map) {
        List<Idea> ideas=new ArrayList<>();
        List<Long> ideaIds= (List<Long>) map.get("ideaIds");
        if(!ideaIds.isEmpty()) {
            ideas = ideaService.listByIds(ideaIds);
        }
        else
        {
            return new ArrayList<>();
        }
        return ideas.stream().map(task -> {
            ProjectPlanItemResult result = BeanPlusUtil.toBean(task, ProjectPlanItemResult.class);
            result.setBizId(task.getId());
            return result;
        }).collect(Collectors.toList());
    }

    /**
     *
     * 用于测试管理查看用例时其关联需求的返显
     *
     *
     * */
    @ApiOperation(value = "根据用例id查询需求信息", notes = "根据用例id数组查询需求信息")
    @PutMapping("/issue/echo/getRequirementList")
    public List<Requirement> getRequirementConnectCaseId(@RequestParam(value = "id") Long caseId)
    {

        List<IssueRequirementTestCase> issueRequirementTestCaseList = issueRequirementTestCaseService
                .list(Wraps.<IssueRequirementTestCase>lbQ().eq(IssueRequirementTestCase::getProductCaseId, caseId));
        List<Long> issueRequirementIdList = issueRequirementTestCaseList.stream()
                .map(IssueRequirementTestCase::getRequirementId).collect(Collectors.toList());
        List<Requirement> list = requirementService
                .list(Wraps.<Requirement>lbQ().in(Requirement::getId, issueRequirementIdList));
        return list;
    }

    /**
     *
     * 用于测试管理更新/新增用例时修改关联需求和其的关联关系
     *
     *
     * */
    @ApiOperation(value = "根据用例id更新需求关联用例信息", notes = "根据用例id更新需求关联用例信息")
    @PutMapping("/issue/echo/updateRequirementConnectCaseId")
    public void updateRequirementConnectCaseId(@RequestBody Map<String,List<String>> map)
    {

        List<Long> productCaseIds = map.get("productCaseIds")
                .stream().map(a -> Long.valueOf(a))
                .collect(Collectors.toList());
        List<Long> requirementIds = map.get("requirementIds")
                .stream().map(a -> Long.valueOf(a))
                .collect(Collectors.toList());
        Long caseId = productCaseIds.get(0);
        issueRequirementTestCaseService
                .remove(Wraps.<IssueRequirementTestCase>lbQ().eq(IssueRequirementTestCase::getProductCaseId, caseId));
        List<IssueRequirementTestCase> issueRequirementTestCaseList = new ArrayList<>();
        requirementIds.forEach(requirementId -> {

            IssueRequirementTestCase issueRequirementTestCase = new IssueRequirementTestCase();
            issueRequirementTestCase.setId(UidGeneratorUtil.getId());
            issueRequirementTestCase.setProductCaseId(caseId);
            issueRequirementTestCase.setRequirementId(requirementId);
            issueRequirementTestCase.setCreatedBy(getUserId());
            issueRequirementTestCase.setCreateTime(LocalDateTime.now());
            issueRequirementTestCaseList.add(issueRequirementTestCase);
        });
        issueRequirementTestCaseService.saveBatchSomeColumn(issueRequirementTestCaseList);
    }

    /**
     *
     * 根据需求id查询管理系统
     *
     *
     * */
    @ApiOperation(value = "根据需求id查询管理系统", notes = "根据需求id查询管理系统")
    @GetMapping("/issue/echo/findFunctionsByReqId")
    public List<Long> findFunctionsByReqId(Long reqId)
    {
        Requirement byId = requirementService.getById(reqId);
        if(byId!=null){
            List<IssueTestreqProduct> list = issueTestreqProductService.list(
                    Wraps.<IssueTestreqProduct>lbQ().eq(IssueTestreqProduct::getTestreqId, reqId));
            List<Long> collect = list.stream().map(IssueTestreqProduct::getProductId).collect(Collectors.toList());
            collect.add(byId.getProductId());
            return collect;
        }else {
            Testreq byId1 = testreqService.getById(reqId);
            List<IssueTestreqProduct> list = issueTestreqProductService.list(
                    Wraps.<IssueTestreqProduct>lbQ().eq(IssueTestreqProduct::getTestreqId, reqId));
            List<Long> collect = list.stream().map(IssueTestreqProduct::getProductId).collect(Collectors.toList());
            collect.add(byId1.getProductId());
            return collect;
        }

    }

    /**
     *
     * 用于测试管理删除用例时修改关联需求和其的关联关系
     *
     *
     * */
    @ApiOperation(value = "根据用例id删除需求关联用例信息", notes = "根据用例id删除需求关联用例信息")
    @DeleteMapping("/issue/echo/deleteRequirementConnectCaseId")
    public void deleteRequirementConnectCaseId(@RequestParam(value = "caseId") Long caseId)
    {
        issueRequirementTestCaseService
                .remove(Wraps.<IssueRequirementTestCase>lbQ().eq(IssueRequirementTestCase::getProductCaseId, caseId));
    }

    @ApiOperation(value = "根据事项ids获取事项集合", notes = "根据事项ids获取事项集合")
    @PostMapping("/project/echo/saveProject")
    public ProjectInfo saveProject(@RequestBody ProjectInfoSaveDTO model)
    {
        ProjectInfo project = BeanPlusUtil.toBean(model, ProjectInfo.class);
        projectInfoService.saveProject(project);
        return project;
    }

    @ApiOperation(value = "根据状态codes获取状态集合", notes = "根据状态codes获取状态集合")
    @GetMapping("/state/echo/findByIds")
    public Map<Serializable, Object> findByStateCodes(@RequestParam(value = "ids") Set<Serializable> ids)
    {
        return stateService.findByIds(ids);
    }

    @ApiOperation(value = "根据工作流获取初始节点", notes = "根据工作流获取初始节点")
    @GetMapping("/state/echo/getStartStateCode")
    public String getStartStateCode(@RequestParam Long workflowId){
        WorkflowNode workflowNode =
                workflowNodeService.getOne(Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, workflowId)
                        .eq(WorkflowNode::getNodeType, WorkflowNodeType.START_NODE));
        if (workflowNode != null){
            return workflowNode.getStateCode();
        }
        return null;
    }
    @GetMapping("/state/echo/findByStateCode")
    State findByStateCode(@RequestParam(value = "stateCode") String stateCode){
        return stateService.findByCode(stateCode);
    }
    @PutMapping("/state/echo/transitionStateByWorkflowIdAndBizId")
    Boolean transitionStateByWorkflowIdAndBizId(@RequestBody StateTransitionDTO stateTransitionDTO){
        return stateService.transitionStateByWorkflowIdAndBizId(stateTransitionDTO);
    }


    @GetMapping("/state/echo/findNextNode/{workFlowId}/{leadingBy}/{sourceStateCode}")
    List<WorkflowNode> findNextNodeByWorkflowIdAndLeadingBy(
            @PathVariable("workFlowId")Long workFlowId,
            @PathVariable("leadingBy")Long leadingBy,
            @PathVariable("sourceStateCode")String sourceStateCode){
        return stateService.findNextNodeByWorkflowIdAndLeadingBy(workFlowId, leadingBy, sourceStateCode);
    }

    @GetMapping("/issue/echo/findByIds")
    public Map<Serializable, Object> findRequirementByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        return requirementService.findByIds(ids);
    }

    @GetMapping("/task/echo/findByIds")
    public Map<Serializable, Object> findTaskByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        List<Task> tasks = taskService.listByIds(ids);
        echoService.action(tasks);
        return tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity()));
    }
    @GetMapping("/task/echo/getById")
    public Task getById(@RequestParam(value = "taskId") Long taskId) {
        return taskService.getById(taskId);
    }


    @GetMapping("/testreq/echo/findByIds")
    public Map<Serializable, Object> findTestreqByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        return testreqService.findByIds(ids);
    }
    @GetMapping("/task/echo/getTasksByReqId")
    public List<Task> getTasksByReqId(@RequestParam(value = "reqId")  Long reqId){
        return taskService.list(Wraps.<Task>lbQ()
                .and(wrap -> wrap.eq(Task::getRequirementId, reqId).or().eq(Task::getTestreqId, reqId)));
    }
    @GetMapping("/bug/echo/queryByTestTaskId")
    public List<Bug> queryByTestTaskId(@RequestParam(value = "testTaskId") Long testTaskId){
        List<Bug> bugList = bugService.list(Wraps.<Bug>lbQ()
                .and(wrap -> wrap.eq(Bug::getTestTaskId, testTaskId)));
        echoService.action(bugList);
        return bugList;
    }

    @GetMapping("/bug/echo/queryByTestTaskIds")
    public List<Bug> queryByTestTaskIds(@RequestParam(value = "testTaskIds") List<Long> testTaskIds){
        return bugService.list(Wraps.<Bug>lbQ()
                .and(wrap -> wrap.in(Bug::getTestTaskId, testTaskIds)));
    }
}
