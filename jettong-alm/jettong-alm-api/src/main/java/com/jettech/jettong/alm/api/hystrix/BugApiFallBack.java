package com.jettech.jettong.alm.api.hystrix;

import com.jettech.jettong.alm.api.BugApi;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.vo.BugReportVO;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class BugApiFallBack implements BugApi
{
    @Override
    public List<BugReportVO> testReportOnBug(List<Long> planIds)
    {
        return Collections.emptyList();
    }

    @Override
    public Map<String, Long> getOverview()
    {
        return null;
    }

    @Override
    public Map<String, Long> getBugData() {
        return null;
    }

    @Override
    public List<BugReportVO> testReportOnBugByTaskIds(List<Long> taskIds)
    {
        return Collections.emptyList();
    }

    @Override
    public List<Bug> queryByTestTaskId(Long testTaskId) {
        return Collections.emptyList();
    }

    @Override
    public List<Bug> queryByTestTaskIds(List<Long> testTaskIds) {
        return Collections.emptyList();
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }
}
