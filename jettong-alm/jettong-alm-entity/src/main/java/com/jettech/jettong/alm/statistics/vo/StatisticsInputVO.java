package com.jettech.jettong.alm.statistics.vo;

import com.jettech.jettong.alm.statistics.enumeration.TimeCycleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 仪表板输入参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 仪表板输入参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className StatisticsInputVO
 * @date 2023/4/13 下午2:22
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "StatisticsInputVO", description = "工作项筛选器通用统计输入参数")
public class StatisticsInputVO {

    /**
     * 是否查询的原始数据
     */
    @ApiModelProperty("是否查询的原始数据")
    protected boolean showSeries;

    /**
     * 筛选器ID
     */
    @ApiModelProperty("筛选器ID")
    @NotNull(message = "筛选器ID")
    protected Long tableViewId;
    /**
     * 图形类型
     */
    @ApiModelProperty("图形类型")
    protected String type;

    /**
     * 待统计的字段
     */
    @ApiModelProperty("X轴，待统计的字段")
    protected String x;

    /**
     * X轴时间格式化
     */
    @ApiModelProperty("X轴，时间格式化，不是时间格式时设为null")
    protected TimeCycleEnum timeCycle;

    /**
     * 统计字段，统计方式，count(*) sum(field) avg(field) min(field) max(field)
     */
    @ApiModelProperty("y轴，统计方式：count(*) sum(field) avg(field) min(field) max(field)")
    protected String y;
    /**
     * X轴分组字段
     */
    @ApiModelProperty("X轴分组字段，多条线时使用")
    protected String group;
    /**
     * 排序字段？
     */
    @ApiModelProperty("排序字段，预留字段")
    protected String sortField;
    /**
     * 排序顺序
     */
    protected boolean asc;

}
