package com.jettech.jettong.alm.workinghours.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 工时分组查询
 * @projectName jettong
 * @package com.jettech.jettong.alm.workinghours.vo
 * @className WorkingHoursGroup
 * @date 2023/7/13 20:58
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class WorkingHoursGroup
{
    private String bizId;

    private int count;

    private String groupType;

    private Object data;
}
