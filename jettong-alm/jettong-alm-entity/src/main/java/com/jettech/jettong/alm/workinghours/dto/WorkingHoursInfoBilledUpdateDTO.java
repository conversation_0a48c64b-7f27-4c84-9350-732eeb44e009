package com.jettech.jettong.alm.workinghours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 修改工时记录是否计费
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong
 * @package com.jettech.jettong.alm.controller.workinghours
 * @className WorkingHoursInfoBilledUpdateDTO
 * @date 2022/9/2 14:49
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursInfoBilledUpdateDTO", description = "修改工时记录是否计费")
public class WorkingHoursInfoBilledUpdateDTO implements Serializable {

    @NotNull(message = "填写 是否计费")
    @ApiModelProperty(value = "是否计费")
    private Boolean billed;

    @NotEmpty(message = "工时记录ID不能为空")
    @ApiModelProperty(value = "工时记录ID")
    private List<Long> ids;
}
