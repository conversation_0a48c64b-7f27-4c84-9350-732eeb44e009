package com.jettech.jettong.alm.workflow.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 工作流节点类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流节点类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.alm.workflow.enumeration
 * @className WorkflowBizType
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "WorkflowNodeType",
        description = "工作流节点类型枚举 #WorkflowNodeType{HANDLER:处理人;RESPONSIBLE:负责人;HANDLER_RESPONSIBLE:责任人和处理人;USER:指定用户;" +
                "ROLE:指定角色-枚举")
public enum WorkflowNodeType implements BaseEnum
{

    /**
     * START_NODE = 开始节点
     */
    START_NODE("开始节点"),

    /**
     * INTERMEDIATE_NODE = 中间节点
     */
    INTERMEDIATE_NODE("中间节点"),
    /**
     * END_NODE = 结束节点
     */
    END_NODE("结束节点");

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static WorkflowNodeType match(String val, WorkflowNodeType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static WorkflowNodeType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(WorkflowNodeType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "START_NODE,INTERMEDIATE_NODE,END_NODE", example = "START_NODE")
    public String getCode()
    {
        return this.name();
    }

}
