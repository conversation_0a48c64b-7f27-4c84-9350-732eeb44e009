package com.jettech.jettong.alm.workinghours.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 工时填报配置实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报配置实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursConf
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("working_hours_conf")
@ApiModel(value = "WorkingHoursConf", description = "工时填报配置")
@AllArgsConstructor
public class WorkingHoursConf extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 配置项的键
     */
    @ApiModelProperty(value = "配置项的键")
    @NotEmpty(message = "请填写配置项的键")
    @Size(max = 100, message = "配置项的键长度不能超过100")
    @TableField(value = "`key`", condition = LIKE)
    private String key;

    /**
     * 配置项名称
     */
    @ApiModelProperty(value = "配置项名称")
    @NotEmpty(message = "请填写配置项名称")
    @Size(max = 100, message = "配置项名称长度不能超过100")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @NotEmpty(message = "请填写数据类型")
    @Size(max = 100, message = "数据类型长度不能超过100")
    @TableField(value = "`type`", condition = LIKE)
    private String type;

    /**
     * 配置项的元配置，用于前端渲染，json格式
     */
    @ApiModelProperty(value = "配置项的元配置，用于前端渲染，json格式")
    @Size(max = 65535, message = "配置项的元配置，用于前端渲染，json格式长度不能超过65535")
    @TableField(value = "`config`", condition = LIKE)
    private String config;

    /**
     * 配置项的值
     */
    @ApiModelProperty(value = "配置项的值")
    @Size(max = 200, message = "配置项的值长度不能超过200")
    @TableField(value = "`value`", condition = LIKE)
    private String value;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`", condition = LIKE)
    private String description;


    @Builder
    public WorkingHoursConf(Long id, Long createdBy, LocalDateTime createTime, 
                    String key, String name, String type, String config, String value, String description)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.key = key;
        this.name = name;
        this.type = type;
        this.config = config;
        this.value = value;
        this.description = description;
    }

}
