package com.jettech.jettong.alm.workinghours.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户每天工时统计
 * @projectName jettong
 * @package com.jettech.jettong.alm.workinghours.vo
 * @className WorkingHoursDay
 * @date 2023/7/5 10:41
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class WorkingHoursDay
{
    @ApiModelProperty("工时")
    private double duration;

    @ApiModelProperty("其他项目工时")
    private double otherDuration;

    @ApiModelProperty("日期")
    private LocalDate day;

    private Long userId;
}
