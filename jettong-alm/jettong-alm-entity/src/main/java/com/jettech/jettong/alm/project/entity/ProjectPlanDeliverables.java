package com.jettech.jettong.alm.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_PLAN_ID_CLASS;

/**
 * 项目计划交付物实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目计划交付物实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.entity
 * @className ProjectPlanDeliverablesEntity
 * @date 2022-08-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
@TableName("project_plan_deliverables")
public class ProjectPlanDeliverables extends Entity<Long> implements EchoVO {

    private static final long serialVersionUID = 2018885832107514703L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 交付物名称
     */
    @ApiModelProperty(value = "交付物名称")
    @NotEmpty(message = "请填写 交付物名称")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`")
    private String description;

    /**
     * 计划ID，外键
     */
    @ApiModelProperty(value = "计划ID，外键")
    @NotEmpty(message = "请填写 计划ID")
    @Echo(api = PROJECT_PLAN_ID_CLASS, beanClass = ProjectPlan.class)
    @TableField(value = "`plan_id`")
    private Long planId;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @TableField(value = "`type`")
    @NotEmpty(message = "请填写 类型")
    private String type;

    /**
     * 交付物状态，TODO-待提交，UNCHECK-待审核，FINISHED-已完成
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "`state_code`")
    @NotEmpty(message = "请填写 状态")
    private String stateCode;

    /**
     * 文件ID
     */
    @ApiModelProperty(value = "文件ID")
    @TableField(value = "`file_id`")
    private Long fileId;

    /**
     * 文件名
     */
    @ApiModelProperty(value = "文件名")
    @TableField(value = "`file_name`", condition = LIKE)
    private String fileName;

    /**
     * 文件大小，单位B
     */
    @ApiModelProperty(value = "文件大小，单位B")
    @TableField(value = "`file_size`")
    private Long fileSize;

    /**
     * 文件大小，单位B
     */
    @ApiModelProperty(value = "文件大小，单位B")
    @TableField(value = "`link`", condition = LIKE)
    private String link;

    /**
     * 交付人
     */
    @ApiModelProperty(value = "交付人")
    @TableField(value = "`deliverer`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long deliverer;

    /**
     * 交付时间
     */
    @ApiModelProperty(value = "交付时间")
    @TableField(value = "`delivery_time`")
    private LocalDateTime deliveryTime;

    @Builder
    public ProjectPlanDeliverables(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime,
            Long updatedBy, String name, String description, Long planId, String type, String stateCode,
            Long fileId, String fileName, Long fileSize, String link, Long deliverer, LocalDateTime deliveryTime)
    {
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.description = description;
        this.planId = planId;
        this.type = type;
        this.stateCode = stateCode;
        this.fileId = fileId;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.link = link;
        this.deliverer = deliverer;
        this.deliveryTime = deliveryTime;
    }
}
