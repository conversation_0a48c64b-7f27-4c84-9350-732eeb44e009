package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 工时填报信息分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursInfo
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursInfoPageQuery", description = "工时填报信息")
public class WorkingHoursInfoPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 分组依据
     */
    @ApiModelProperty(value = "分组依据，可填值：fillingTime、filledBy、project")
    private String groupBy;

    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    private WorkingHoursType type;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 事项ID
     */
    @ApiModelProperty(value = "事项ID")
    private Long bizId;
    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    private String name;
    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    private String description;
    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    private Double duration;
    /**
     * 填报人
     */
    @ApiModelProperty(value = "填报人")
    private Long filledBy;
    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    private LocalDateTime fillingTime;
    /**
     * 是否已确认工时
     */
    @ApiModelProperty(value = "是否已确认工时")
    private Boolean verified;
    /**
     * 工时是否有效
     */
    @ApiModelProperty(value = "工时是否有效")
    private Boolean valid;

    /**
     * 是否计费
     */
    @ApiModelProperty(value = "是否计费")
    private Boolean billed;

    @ApiModelProperty(value = "部门id")
    private Long orgId;

    @ApiModelProperty(value = "开始时间")
    private LocalDate startTime;

    @ApiModelProperty(value = "结束时间")
    private LocalDate endTime;

    @ApiModelProperty(value = "0未确认 1已审批 2已驳回")
    private Integer status;
}
