package com.jettech.jettong.alm.project.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.entity.Type;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.STATE_CODE_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.TYPE_CODE_CLASS;


/**
 * 项目工作流流转历史信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目工作流流转历史信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className ProjectWorkflowTransitionHistory
 * @date 2021-11-15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("project_workflow_transition_history")
@ApiModel(value = "ProjectWorkflowTransitionHistory", description = "项目工作流流转历史信息")
@AllArgsConstructor
public class ProjectWorkflowTransitionHistory extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 事项id
     */
    @ApiModelProperty(value = "事项id")
    @NotNull(message = "请填写事项id")
    @TableField(value = "`biz_id`")
    @Excel(name = "事项id")
    private Long bizId;

    /**
     * 事项code
     */
    @ApiModelProperty(value = "事项code")
    @NotEmpty(message = "请填写事项code")
    @Size(max = 20, message = "事项code长度不能超过20")
    @TableField(value = "`type_code`")
    @Echo(api = TYPE_CODE_CLASS, beanClass = Type.class)
    @Excel(name = "事项code")
    private String typeCode;

    /**
     * 源节点id
     */
    @ApiModelProperty(value = "源节点id")
    @NotNull(message = "请填写源节点id")
    @TableField(value = "`source`")
    @Excel(name = "源节点id")
    private Long source;

    /**
     * 源节点状态code
     */
    @ApiModelProperty(value = "源节点状态code")
    @NotEmpty(message = "请填写源节点状态code")
    @Size(max = 20, message = "源节点状态code长度不能超过20")
    @TableField(value = "`source_state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    @Excel(name = "源节点状态code")
    private String sourceStateCode;

    /**
     * 目标节点id
     */
    @ApiModelProperty(value = "目标节点id")
    @NotNull(message = "请填写目标节点id")
    @TableField(value = "`target`")
    @Excel(name = "目标节点id")
    private Long target;

    /**
     * 目标状态code
     */
    @ApiModelProperty(value = "目标状态code")
    @NotEmpty(message = "请填写目标状态code")
    @Size(max = 20, message = "目标状态code长度不能超过20")
    @TableField(value = "`target_state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    @Excel(name = "目标状态code")
    private String targetStateCode;

    /**
     * 消耗时间 毫秒数
     */
    @ApiModelProperty(value = "消耗时间 毫秒数")
    @TableField(value = "`spend_time`")
    @Excel(name = "消耗时间 毫秒数")
    private Long spendTime;

    @ApiModelProperty(value = "是否为系统自动流转")
    @TableField(value = "`system_flow`")
    @Excel(name = "是否为系统自动流转")
    private Boolean systemFlow;

    @ApiModelProperty(value = "总耗时")
    @TableField(value = "totalSpendTime", exist = false)
    private Long totalSpendTime;

    @Builder
    public ProjectWorkflowTransitionHistory(Long id, Long createdBy, LocalDateTime createTime,
            Long bizId, String typeCode, Long source, String sourceStateCode, Long target,
            String targetStateCode, Long spendTime, Boolean systemFlow)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.bizId = bizId;
        this.typeCode = typeCode;
        this.source = source;
        this.sourceStateCode = sourceStateCode;
        this.target = target;
        this.targetStateCode = targetStateCode;
        this.spendTime = spendTime;
        this.systemFlow = systemFlow;
    }

}
