package com.jettech.jettong.alm.workflow.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 工作流信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.dto
 * @className WorkflowInfoUpdateDTO
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkflowInfoUpdateDTO", description = "工作流信息")
public class WorkflowInfoUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    /**
     * 工作流节点信息
     */
    @ApiModelProperty(value = "工作流节点信息")
    private List<WorkflowNode> workflowNodes;

    /**
     * 工作流流转关系信息
     */
    @ApiModelProperty(value = "工作流流转关系信息")
    private List<WorkflowTransition> workflowTransitions;

    /**
     * 工作流流转权限信息
     */
    @ApiModelProperty(value = "工作流流转权限信息")
    private List<WorkflowAuthority> workflowAuthorities;

}
