package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.common.dto.PeriodQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工时分摊表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时分摊表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.dto.workinghoursPageQuery
 * @className WorkingHoursAllocation
 * @date 2023-07-06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursAllocationPageQuery", description = "工时分摊表")
public class WorkingHoursAllocationPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键 工时id
     */
    @ApiModelProperty(value = "外键 工时id")
    private Long workingHoursId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "分摊项目id")
    private Long sourceProjectId;

    @ApiModelProperty(value = "事项类型")
    private WorkingHoursType type;

    @ApiModelProperty(value = "填报人")
    private Long filledBy;

    @ApiModelProperty(value = "分摊人")
    private Long createdBy;

    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    private PeriodQuery<LocalDateTime> fillingTime;

    @ApiModelProperty(value = "0未确认 1已审批 2已驳回")
    private Integer status;

    @ApiModelProperty(value = "审批结果")
    private Boolean valid;

}
