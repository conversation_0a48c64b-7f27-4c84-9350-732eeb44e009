package com.jettech.jettong.alm.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据下钻输入参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据下钻输入参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue
 * @className DrillDownInputVO
 * @date 2023/4/26 下午4:05
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "DrillDownInputVO", description = "数据下钻输入参数")
public class DrillDownInputVO extends StatisticsInputVO {

    @ApiModelProperty("X轴的数据")
    private String xData;

    @ApiModelProperty("分组的数据")
    private String groupData;

}
