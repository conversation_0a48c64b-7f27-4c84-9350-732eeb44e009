package com.jettech.jettong.alm.workflow.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工作流信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.workflow.dto
 * @className WorkflowInfo
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkflowInfoPageQuery", description = "工作流信息")
public class WorkflowInfoPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 使用中
     */
    @ApiModelProperty(value = "使用中")
    private Boolean state;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    private Boolean readonly;

}
