package com.jettech.jettong.alm.statistics.enumeration;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalUnit;

/**
 * 统计中的时间范围接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 统计中的时间范围接口
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className ITimeCycle
 * @date 2023/4/25 下午5:48
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TimeCycle {

    @NotNull
    TemporalUnit getUnit();

    @NotNull
    String getSqlFormat();

    @NotNull
    DateTimeFormatter getFormatter();

    default String format(LocalDateTime time) {
        if (time == null) {
            return null;
        }
        return getFormatter().format(time);
    }

    default String format(LocalDate date) {
        if (date == null) {
            return null;
        }
        return getFormatter().format(date);
    }

    default String getSql(String filed) {
        return String.format(this.getSqlFormat(), filed);
    }
}
