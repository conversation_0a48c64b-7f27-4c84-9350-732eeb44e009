package com.jettech.jettong.alm.workflow.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.Stage;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.STAGE_CODE_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.STATE_CODE_CLASS;


/**
 * 工作流节点状态信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流节点状态信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className WorkflowNode
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("workflow_node")
@ApiModel(value = "WorkflowNode", description = "工作流节点状态信息")
@AllArgsConstructor
public class WorkflowNode extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 外键，所属工作流id
     */
    @ApiModelProperty(value = "标识，画图用")
    @NotNull(message = "请填写标识，画图用")
    @TableField(value = "`code`")
    @Excel(name = "标识，画图用")
    private String code;

    /**
     * 外键，所属工作流id
     */
    @ApiModelProperty(value = "外键，所属工作流id")
    @NotNull(message = "请填写外键，所属工作流id")
    @TableField(value = "`workflow_id`")
    @Excel(name = "外键，所属工作流id")
    private Long workflowId;

    /**
     * 冗余字段，节点所属阶段id
     */
    @ApiModelProperty(value = "冗余字段，阶段code")
    @TableField(value = "`stage_code`")
    @Echo(api = STAGE_CODE_CLASS, beanClass = Stage.class)
    @Excel(name = "冗余字段，阶段code")
    private String stageCode;

    /**
     * 节点所属状态id
     */
    @ApiModelProperty(value = "节点所属状态Code")
    @TableField(value = "`state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    @Excel(name = "节点所属状态Code")
    private String stateCode;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称")
    @NotEmpty(message = "请填写节点名称")
    @Size(max = 50, message = "节点名称长度不能超过50")
    @TableField(value = "`name`")
    @Excel(name = "节点名称")
    private String name;

    /**
     * 节点描述
     */
    @ApiModelProperty(value = "节点描述")
    @Size(max = 100, message = "节点描述长度不能超过100")
    @TableField(value = "`description`")
    @Excel(name = "节点描述")
    private String description;

    /**
     * 节点类型枚举
     */
    @ApiModelProperty(value = "节点类型枚举")
    @TableField(value = "`node_type`")
    @Excel(name = "节点类型枚举")
    private WorkflowNodeType nodeType;

    /**
     * X坐标，画图用
     */
    @ApiModelProperty(value = "X坐标，画图用")
    @TableField(value = "`x`")
    @Excel(name = "X坐标，画图用")
    private Integer x;

    /**
     * Y坐标，画图用
     */
    @ApiModelProperty(value = "Y坐标，画图用")
    @TableField(value = "`y`")
    @Excel(name = "Y坐标，画图用")
    private Integer y;

    /**
     * size，画图用
     */
    @ApiModelProperty(value = "size，画图用")
    @Size(max = 100, message = "size，画图用长度不能超过100")
    @TableField(value = "`size`")
    @Excel(name = "size，画图用")
    private String size;

    /**
     * shape，画图用
     */
    @ApiModelProperty(value = "shape，画图用")
    @Size(max = 100, message = "shape，画图用长度不能超过100")
    @TableField(value = "`shape`")
    @Excel(name = "shape，画图用")
    private String shape;

    /**
     * 进度，0-100
     */
    @ApiModelProperty(value = "进度，0-100")
    @TableField(value = "`progress`")
    @Excel(name = "进度，0-100")
    private Integer progress;


    @Builder
    public WorkflowNode(Long id, Long createdBy, LocalDateTime createTime,
            Long workflowId, String code, String stageCode, String stateCode, String name, String description,
            WorkflowNodeType nodeType, Integer x, Integer y, String size, String shape, Integer progress)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.workflowId = workflowId;
        this.code = code;
        this.stageCode = stageCode;
        this.stateCode = stateCode;
        this.name = name;
        this.description = description;
        this.nodeType = nodeType;
        this.x = x;
        this.y = y;
        this.size = size;
        this.shape = shape;
        this.progress = progress;
    }

}
