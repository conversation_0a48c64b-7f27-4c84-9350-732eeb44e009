package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.common.dto.PeriodQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工时分摊表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.dto.workinghoursPageQuery
 * @className WorkingHoursAllocation
 * @date 2023-07-06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
public class WorkingHoursPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @ApiModelProperty(value = "事项类型")
    private WorkingHoursType type;

    @ApiModelProperty(value = "填报人")
    private Long filledBy;

    @ApiModelProperty(value = "0未确认 1已审批 2已驳回")
    private Integer status;
    /**
     * 是否确认
     */
    @ApiModelProperty(value = "是否确认")
    private Boolean verified;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "审批结果 是否有效")
    private Boolean valid;

    @ApiModelProperty(value = "填报时间")
    private PeriodQuery<LocalDateTime> fillingTime;

//    @ApiModelProperty(value = "开始时间")
//    private LocalDate startTime;
//
//    @ApiModelProperty(value = "结束时间")
//    private LocalDate endTime;


    @ApiModelProperty(value = "组织id")
    private Long orgId;

    @ApiModelProperty(value = "组织id列表" ,hidden = true)
    List<Long> orgIds;

    private String groupBy;
}
