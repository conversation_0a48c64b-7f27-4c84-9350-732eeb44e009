package com.jettech.jettong.alm.project.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.function.Function;

/**
 *
 *
 * <AUTHOR>
 * @version 1.0
 * @projectname jettong-tm
 * 2022/11/8 10:28
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "WorkflowAuthorityType",
        description = "工作流流转权限类型枚举")
public enum TransitionCheckRule implements BaseEnum {

    /**
     * ROLE="指定角色"
     */
    NOT_NULL("不为空", Objects::nonNull),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;

    @ApiModelProperty(value = "方法")
    private Function<Object, Boolean> function;


    @Override
    @ApiModelProperty(value = "编码", allowableValues = "NOT_NULL",
            example = "NOT_NULL")
    public String getCode()
    {
        return this.name();
    }

}
