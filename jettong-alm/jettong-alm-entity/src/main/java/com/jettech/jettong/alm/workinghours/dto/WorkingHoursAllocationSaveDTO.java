package com.jettech.jettong.alm.workinghours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工时分摊表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时分摊表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.dto.workinghours
 * @className WorkingHoursAllocationSaveDTO
 * @date 2023-07-06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursAllocationSaveDTO", description = "工时分摊表")
public class WorkingHoursAllocationSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键 工时id
     */
    @ApiModelProperty(value = "外键 工时id")
    private Long workingHoursId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 分摊工时，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "分摊工时，单位：小时。最小计量0.5")
    private Double duration;
    /**
     * 是否确认
     */
    @ApiModelProperty(value = "是否确认")
    private Boolean verified;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private Boolean valid;

}
