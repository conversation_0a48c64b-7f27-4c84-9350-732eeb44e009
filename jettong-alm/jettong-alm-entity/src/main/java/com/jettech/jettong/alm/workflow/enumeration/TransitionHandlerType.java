package com.jettech.jettong.alm.workflow.enumeration;

import com.jettech.basic.base.BaseEnum;

/**
 * 工作项装填流转后置处理器类型
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作项装填流转后置处理器类型
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.workflow.enumeration
 * @className TransitionHandlerType
 * @date 2022/11/16 下午4:12
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum TransitionHandlerType implements BaseEnum {
    UPDATE_FIELD("后置修改数据"),
    MESSAGE("后置消息通知"),
    UPDATE_STATE("后置父子工作项状态同步"),
    ;

    private final String desc;

    TransitionHandlerType(String desc) {
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
