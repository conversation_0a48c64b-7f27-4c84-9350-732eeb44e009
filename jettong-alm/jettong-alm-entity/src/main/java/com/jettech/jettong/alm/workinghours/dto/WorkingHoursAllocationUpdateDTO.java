package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 工时分摊表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时分摊表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.dto.workinghours
 * @className WorkingHoursAllocationUpdateDTO
 * @date 2023-07-06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursAllocationUpdateDTO", description = "工时分摊表")
public class WorkingHoursAllocationUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 外键 工时id
     */
    @ApiModelProperty(value = "外键 工时id")
    private Long workingHoursId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 分摊工时，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "分摊工时，单位：小时。最小计量0.5")
    private Double duration;
    /**
     * 是否确认
     */
    @ApiModelProperty(value = "是否确认")
    private Boolean verified;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private Boolean valid;
}
