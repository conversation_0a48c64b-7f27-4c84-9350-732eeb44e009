package com.jettech.jettong.alm.workinghours.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工时管理统计视图
 *
 * @param <T> 显示的类型
 * <AUTHOR>
 * @version 1.0
 * @description 工时管理统计视图
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.workinghours.vo
 * @className WorkingHoursStatisticsVO
 * @date 2022/9/7 下午2:27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkingHoursStatisticsVO<T> {

    /**
     * 每条记录对应的名称
     */
    private List<String> name;

    /**
     * 数据
     */
    private List<T> data;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BarVO {

        /**
         * 类型的名称，每条记录由多种类型组合
         */
        private String name;

        /**
         * 类型对应的数据，与 WorkingHoursStatisticsVO.name 数量一致
         */
        private List<Double> data;

    }
}
