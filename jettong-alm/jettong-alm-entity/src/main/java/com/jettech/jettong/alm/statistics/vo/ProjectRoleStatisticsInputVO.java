package com.jettech.jettong.alm.statistics.vo;

import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 仪表板输入参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 仪表板输入参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className StatisticsInputVO
 * @date 2023/4/13 下午2:22
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "ProjectRoleStatisticsInputVO", description = "项目角色统计图输入参数")
public class ProjectRoleStatisticsInputVO {

    /**
     * 项目
     */
    @ApiModelProperty("项目")
    @NotNull
    protected Long projectId;

    /**
     * 项目角色
     */
    @ApiModelProperty("项目角色")
    @NotNull
    protected Long roleId;

    /**
     * 工作项
     */
    @ApiModelProperty("工作项")
    @NotNull
    protected TypeClassify classify;

    /**
     * 统计字段，统计方式，count(*) sum(field) avg(field) min(field) max(field)
     */
    @ApiModelProperty("y轴，统计方式：count(*) sum(field) avg(field) min(field) max(field)")
    protected String y;
    /**
     * X轴分组字段
     */
    @ApiModelProperty("X轴分组字段，多条线时使用")
    protected String group;

}
