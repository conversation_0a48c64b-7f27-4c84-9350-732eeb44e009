package com.jettech.jettong.alm.statistics.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计图查询数据VO
 *
 * <AUTHOR>
 * @version 1.0
 * @description 统计图查询数据VO
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className StatisticsOutVO
 * @date 2023/4/17 下午7:23
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "StatisticsOutVO", description = "统计图查询数据VO")
public class StatisticsOutVO {
    /**
     * X轴对应的值
     */
    @ApiModelProperty("X轴对应的值")
    private String key;
    /**
     * 多条线对应的类型
     */
    @ApiModelProperty("多条线对应的类型")
    private String group;
    /**
     * 统计结果
     */
    @ApiModelProperty("统计结果")
    private Number value;

    public StatisticsOutVO() {
    }

    public StatisticsOutVO(String key, String group, Number value) {
        this.key = key;
        this.group = group;
        this.value = value;
    }
}
