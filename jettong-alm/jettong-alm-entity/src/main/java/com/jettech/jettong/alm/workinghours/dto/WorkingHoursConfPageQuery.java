package com.jettech.jettong.alm.workinghours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工时填报配置分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报配置分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursConf
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursConfPageQuery", description = "工时填报配置")
public class WorkingHoursConfPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 配置项的键
     */
    @ApiModelProperty(value = "配置项的键")
    private String key;
    /**
     * 配置项名称
     */
    @ApiModelProperty(value = "配置项名称")
    private String name;
    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String type;
    /**
     * 配置项的元配置，用于前端渲染，json格式
     */
    @ApiModelProperty(value = "配置项的元配置，用于前端渲染，json格式")
    private String config;
    /**
     * 配置项的值
     */
    @ApiModelProperty(value = "配置项的值")
    private String value;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

}
