package com.jettech.jettong.alm.workflow.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 工作流历史信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流历史信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className WorkflowHistory
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("workflow_history")
@ApiModel(value = "WorkflowHistory", description = "工作流历史信息")
@AllArgsConstructor
public class WorkflowHistory implements Serializable
{

    private static final long serialVersionUID = 1L;
    /**
     * 工作流id
     */
    @ApiModelProperty(value = "工作流id")
    @NotNull(message = "请填写工作流id")
    @TableId(value = "`workflow_id`", type = IdType.INPUT)
    @Excel(name = "工作流id")
    private Long workflowId;

    /**
     * 工作流名称
     */
    @ApiModelProperty(value = "工作流名称")
    @NotEmpty(message = "请填写工作流名称")
    @TableField(value = "`name`")
    @Excel(name = "工作流名称")
    private String name;

    /**
     * 工作流名称
     */
    @ApiModelProperty(value = "工作流描述")
    @NotEmpty(message = "请填写工作流描述")
    @TableField(value = "`description`")
    @Excel(name = "工作流描述")
    private String description;

    /**
     * 节点状态信息Json
     */
    @ApiModelProperty(value = "节点状态信息Json")
    @NotEmpty(message = "请填写节点状态信息Json")
    @TableField(value = "`workflow_node`")
    @Excel(name = "节点状态信息Json")
    private String workflowNode;

    /**
     * 流转关系Json
     */
    @ApiModelProperty(value = "流转关系Json")
    @NotEmpty(message = "请填写流转关系Json")
    @TableField(value = "`workflow_transition`")
    @Excel(name = "流转关系Json")
    private String workflowTransition;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    protected Long createdBy;

    @Builder
    public WorkflowHistory(
            LocalDateTime createTime, Long createdBy, Long workflowId, String name, String description,
            String workflowNode, String workflowTransition)
    {
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.workflowId = workflowId;
        this.name = name;
        this.description = description;
        this.workflowNode = workflowNode;
        this.workflowTransition = workflowTransition;
    }

}
