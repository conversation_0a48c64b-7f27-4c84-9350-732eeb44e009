package com.jettech.jettong.alm.project.enumeration;

import com.jettech.basic.base.BaseEnum;

import java.util.stream.Stream;

/**
 * 项目阶段
 *
 * <AUTHOR>
 * @version 1.0
 * 2023/8/1 10:30
 * @description 项目阶段
 * @projectName jettong-tm
 */
public enum ProjectStageEnum implements BaseEnum {
    APPROVAL("立项"),
    RUNNING("执行中"),
    CLOSED("结项")
    ;

    private final String desc;

    ProjectStageEnum(String desc) {
        this.desc = desc;
    }

    /**
     * 根据当前枚举的name匹配
     */
    public static ProjectStageEnum match(String val, ProjectStageEnum def)
    {
        return Stream.of(values()).filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ProjectStageEnum get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ProjectStageEnum val)
    {
        return val != null && eq(val.name());
    }

    @Override
    public String getDesc() {
        return desc;
    }
}
