package com.jettech.jettong.alm.project.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 计划实例表实体注释中生成的类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 计划实例表实体注释中生成的类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.alm.enumeration
 * @className ProjectPlanType
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProjectPlanType",
        description = "计划类型 #ProjectPlanType{PROJECT:项目;SPRINT:迭代;MILESTONE:里程碑;PROGRAM:项目集-枚举")
public enum ProjectPlanType implements BaseEnum
{

    /**
     * PROJECT="项目"
     */
    PROJECT("项目"),
    /**
     * SPRINT="迭代"
     */
    SPRINT("迭代"),
    /**
     * PLAN=计划
     */
    PLAN("计划"),
    /**
     * MILESTONE="里程碑"
     */
    MILESTONE("里程碑"),

    /**
     * PROGRAM="项目集"
     */
    PROGRAM("项目集"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ProjectPlanType match(String val, ProjectPlanType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ProjectPlanType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ProjectPlanType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "PROJECT,SPRINT,MILESTONE,PROGRAM", example = "PROJECT")
    public String getCode()
    {
        return this.name();
    }

}
