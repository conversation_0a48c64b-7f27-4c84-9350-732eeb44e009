package com.jettech.jettong.alm.statistics.vo;

import com.jettech.jettong.alm.issue.entity.WorkItemEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 统计结果
 *
 * <AUTHOR>
 * @version 1.0
 * @description 统计结果
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className StatisticsResultVO
 * @date 2023/4/25 下午6:13
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "StatisticsResultVO", description = "统计图查询数据VO")
public class StatisticsResultVO {


    /**
     * 统计的数据
     */
    @ApiModelProperty("统计的数据")
    private List<StatisticsOutVO> outList;

    /**
     * 查询的原始数据
     */
    @ApiModelProperty("查询的原始数据")
    private List<WorkItemEntity> series;

    public StatisticsResultVO() {
    }

    public StatisticsResultVO(List<StatisticsOutVO> outList) {
        this.outList = outList;
    }
}
