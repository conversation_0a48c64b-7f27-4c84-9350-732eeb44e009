package com.jettech.jettong.alm.statistics.enumeration;

import com.jettech.basic.base.BaseEnum;

import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalUnit;

/**
 * 统计中的时间范围
 *
 * <AUTHOR>
 * @version 1.0
 * @description
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className TimeUnit
 * @date 2023/4/19 下午12:05
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum TimeCycleEnum implements TimeCycle, BaseEnum {
    DAY(ChronoUnit.DAYS, "DATE_FORMAT(%s, '%Y-%m-%d')", DateTimeFormatter.ofPattern("yyyy-MM-dd")),
    MOUTH(ChronoUnit.MONTHS, "DATE_FORMAT(%s, '%Y-%m')", DateTimeFormatter.ofPattern("yyyy-MM")),
    WEEK(ChronoUnit.WEEKS, "DATE_FORMAT(%s, '%Y年第%U周')", DateTimeFormatter.ofPattern("yyyy年第ww周")),
    QUARTER(new Quarter(), "DATE_FORMAT(%s, '%Y年Q%q')", DateTimeFormatter.ofPattern("yyyy'年Q'q")),
    YEAR(ChronoUnit.YEARS, "DATE_FORMAT(%s, '%Y年')", DateTimeFormatter.ofPattern("yyyy年")),
    ;

    private final TemporalUnit unit;
    private final String sqlFormat;
    private final DateTimeFormatter formatter;

    TimeCycleEnum(TemporalUnit unit, String sqlFormat, DateTimeFormatter formatter) {
        this.unit = unit;
        this.sqlFormat = sqlFormat;
        this.formatter = formatter;
    }

    @Override
    public TemporalUnit getUnit() {
        return unit;
    }

    @Override
    public String getSqlFormat() {
        return this.sqlFormat;
    }

    @Override
    public DateTimeFormatter getFormatter() {
        return formatter;
    }

    @Override
    public String getDesc() {
        return super.name();
    }

    /**
     * 补充源码 {@link ChronoUnit} 中没有的季度类型
     */
    private static class Quarter implements TemporalUnit {

        /**
         * @see ChronoUnit#MONTHS
         */
        @Override
        public Duration getDuration() {
            return Duration.ofSeconds(31556952L / 3);
        }

        @Override
        public boolean isDurationEstimated() {
            return true;
        }

        @Override
        public boolean isDateBased() {
            return true;
        }

        @Override
        public boolean isTimeBased() {
            return false;
        }

        @Override
        public boolean isSupportedBy(Temporal temporal) {
            return temporal.isSupported(this);
        }

        @SuppressWarnings("unchecked")
        @Override
        public <R extends Temporal> R addTo(R temporal, long amount) {
            return (R) temporal.plus(amount * 3, ChronoUnit.MONTHS);
        }

        @Override
        public long between(Temporal temporal1Inclusive, Temporal temporal2Exclusive) {
            return temporal1Inclusive.until(temporal2Exclusive, this);
        }

        @Override
        public String toString() {
            return super.toString();
        }
    }
}
