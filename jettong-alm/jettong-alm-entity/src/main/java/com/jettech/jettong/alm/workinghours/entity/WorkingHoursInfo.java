package com.jettech.jettong.alm.workinghours.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_INFO_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.STATE_CODE_CLASS;


/**
 * 工时填报信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursInfo
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("working_hours_info")
@ApiModel(value = "WorkingHoursInfo", description = "工时填报信息")
@AllArgsConstructor
public class WorkingHoursInfo extends SuperEntity<Long> implements EchoVO {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    @NotNull(message = "请填写事项类型")
    @TableField(value = "`type`")
    private WorkingHoursType type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 事项ID
     */
    @ApiModelProperty(value = "事项ID")
    @TableField(value = "`biz_id`")
    private Long bizId;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @TableField(value = "`state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    private String stateCode;

    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    @Size(max = 200, message = "事项名称，当时类型为其他事项时使用此字段长度不能超过200")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    @Size(max = 2000, message = "事项描述长度不能超过2000")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    @NotNull(message = "请填写工时时间，单位：小时。最小计量0.5")
    @TableField(value = "`duration`")
    private Double duration;

    /**
     * 填报人
     */
    @ApiModelProperty(value = "填报人")
    @NotNull(message = "请填写填报人")
    @TableField(value = "`filled_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long filledBy;

    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    @NotNull(message = "请填写填报时间")
    @TableField(value = "`filling_time`", condition = "date(%s) = date(#{%s})")
    private LocalDateTime fillingTime;

    /**
     * 是否已确认工时
     */
    @ApiModelProperty(value = "是否已确认工时")
    @NotNull(message = "请填写是否已确认工时")
    @TableField(value = "`verified`")
    private Boolean verified;

    /**
     * 工时是否有效
     */
    @ApiModelProperty(value = "工时是否有效")
    @TableField(value = "`valid`")
    private Boolean valid;

    /**
     * 是否计费
     */
    @ApiModelProperty(value = "是否计费")
    @TableField(value = "`billed`")
    private Boolean billed;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;
    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    @NotNull(message = "请填写事项类型")
    @TableField(exist = false)
    private WorkingHoursType sourceType;

    @Builder
    public WorkingHoursInfo(Long id, Long createdBy, LocalDateTime createTime, WorkingHoursType type, Long projectId,
            Long bizId, String stateCode, String name, String description, Double duration, Long filledBy, LocalDateTime fillingTime,
            Boolean verified, Boolean valid, Boolean billed) {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.type = type;
        this.projectId = projectId;
        this.bizId = bizId;
        this.stateCode = stateCode;
        this.name = name;
        this.description = description;
        this.duration = duration;
        this.filledBy = filledBy;
        this.fillingTime = fillingTime;
        this.verified = verified;
        this.valid = valid;
        this.billed = billed;
    }

}
