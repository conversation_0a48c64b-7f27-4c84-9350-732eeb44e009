package com.jettech.jettong.alm.workinghours.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 工时填报审批记录新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报审批记录新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursConfirmSaveDTO
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursConfirmSaveDTO", description = "工时填报审批记录")
public class WorkingHoursConfirmSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键，工时信息表主键
     */
    @ApiModelProperty(value = "外键，工时信息表主键/工时分摊主键")
    @NotNull(message = "请填写外键，工时信息表主键")
    @NotEmpty(message = "请填写外键，工时信息表主键")
    private List<Long> workingHoursIds;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "请填写是否有效")
    private Boolean valid;

}
