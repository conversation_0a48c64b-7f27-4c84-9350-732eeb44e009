package com.jettech.jettong.alm.workinghours.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 工时管理的工作项类型
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工时管理的工作项类型
 * @projectName jettong
 * @package com.jettech.jettong.alm.workinghours.enumeration
 * @className WorkdingHoursType
 * @date 2022/8/8 14:46
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public enum WorkingHoursType implements BaseEnum
{
    /**
     * ISSUE=需求
     */
    ISSUE("需求"),
    /**
     * TESTREQ=测试需求
     */
    TESTREQ("测试需求"),
    /**
     * BUG=缺陷
     */
    BUG("缺陷"),
    /**
     * TASK=任务
     */
    TASK("任务"),
    /**
     * CUSTOMIZED=自定义事项
     */
    CUSTOMIZED("自定义事项"),

    /**
     * ALLOCATION=分摊工时
     */
    ALLOCATION("分摊工时");



    @ApiModelProperty(value = "描述")
    private final String desc;

    WorkingHoursType(String desc) {
        this.desc = desc;
    }

    @Override
    public String getDesc() {
        return this.desc;
    }
}
