package com.jettech.jettong.alm.workflow.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowAuthorityType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 工作流流转权限信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流流转权限信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.workflow.entity
 * @className WorkflowAuthority
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("workflow_authority")
@ApiModel(value = "WorkflowAuthority", description = "工作流流转权限信息")
@AllArgsConstructor
public class WorkflowAuthority implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 工作流id
     */
    @ApiModelProperty(value = "工作流id")
    @TableField(value = "`workflow_id`")
    @Excel(name = "工作流id")
    private Long workflowId;

    /**
     * 流转权限id
     */
    @ApiModelProperty(value = "流转权限id")
    @TableField(value = "`transition_id`")
    @Excel(name = "流转权限id")
    private Long transitionId;

    /**
     * 权限类型枚举 #WorkFlowAuthorityType{HANDLER:处理人;RESPONSIBLE:负责人;HANDLER_RESPONSIBLE:责任人和处理人;USER:指定用户;ROLE:指定角色}
     */
    @ApiModelProperty(
            value = "权限类型枚举 #WorkFlowAuthorityType{HANDLER:处理人;RESPONSIBLE:负责人;CREATOR:创建人/提出人;USER:指定用户;" +
                    "ROLE:指定角色}")
    @TableField(value = "`type`")
    @Excel(name = "权限类型",
            replace = {"处理人_HANDLER", "负责人_RESPONSIBLE", "创建人/提出人_CREATOR", "指定用户_USER", "指定角色_ROLE",
                    "_null"})
    private WorkflowAuthorityType type;

    /**
     * 角色或用户id，多个以英文逗号隔开
     */
    @ApiModelProperty(value = "角色或用户id，多个以英文逗号隔开")
    @TableField(value = "`user_or_role`")
    @Excel(name = "角色或用户id，多个以英文逗号隔开")
    private Long userOrRole;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    protected Long createdBy;

    @Builder
    public WorkflowAuthority(Long createdBy, LocalDateTime createTime,
            Long workflowId, Long transitionId, WorkflowAuthorityType type,
            Long userOrRole)
    {
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.workflowId = workflowId;
        this.transitionId = transitionId;
        this.type = type;
        this.userOrRole = userOrRole;
    }

}
