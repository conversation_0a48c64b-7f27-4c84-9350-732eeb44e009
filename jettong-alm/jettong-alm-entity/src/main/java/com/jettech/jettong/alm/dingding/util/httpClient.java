package com.jettech.jettong.alm.dingding.util;

import cn.hutool.json.JSONObject;
import com.jettech.basic.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.StringWriter;
import java.net.URISyntaxException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 发送钉钉请求方法
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.dingding.util
 * @className httpClient
 * @date 2022/3/31 10:45
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class httpClient {

    public static String doPost(String url, Map<String, String> headers, Map<String, Object> params, Map<String, String> urlparams) throws IOException {
        String responseResult = "";
        CloseableHttpResponse httpResponse = null;
        try(CloseableHttpClient httpClient = HttpClientBuilder.create().build()) {
            HttpPost request = null;
            //添加请求url中的参数
            if (urlparams != null && !urlparams.isEmpty()) {
                URIBuilder uriBuilder = new URIBuilder(url);
                urlparams.forEach((key, value) -> {
                    uriBuilder.setParameter(key, value);
                });
                request = new HttpPost(uriBuilder.build());
            } else {
                request = new HttpPost(url);
            }

            //设置请求头
            if (headers != null && headers.size() > 0) {
                HttpPost finalRequest = request;
                headers.forEach((key, value) -> {
                    finalRequest.addHeader(key, value);
                });
            }
            //设置请求参数
            String paramStr = "";
            if (params != null && params.size() > 0) {
                //转化json
                JSONObject jsonObject = new JSONObject();
                params.forEach((k, v) -> {
                    jsonObject.putOpt(k, v);
                });
                StringWriter sw = new StringWriter();
                jsonObject.write(sw);
                paramStr = sw.toString();
                //设置请求体
                request.setEntity(new StringEntity(paramStr, "utf-8"));
            }
            //执行请求
            httpResponse = httpClient.execute(request);
            //获取响应数据
            HttpEntity entity = httpResponse.getEntity();
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if (entity != null && statusCode == 200) {
                responseResult = EntityUtils.toString(entity, "UTF-8");
            }
        } catch (Exception e) {
            throw BizException.validFail("请求异常", e);
        } finally {
            if (httpResponse != null) {
                httpResponse.close();
            }
        }
        return responseResult;
    }

    public static String doGet(String url) throws URISyntaxException {
        if (StringUtils.isEmpty(url)) {
            return "";
        }
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        URIBuilder uriBuilder = new URIBuilder(url);
        uriBuilder.setParameter("appkey", "ding5jjojpignrclj1zo");
        uriBuilder.setParameter("appsecret", "1Clg8ubkDiKY7WXxi0Dlmh84OiraEpSWm2HcK3P8PKP2tPoZXqUGarM8z0E1wAlT");
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        CloseableHttpResponse response = null;
        String access_token = "";
        try {
            response = httpClient.execute(httpGet);
            HttpEntity responseEntity = response.getEntity();
            String content = EntityUtils.toString(responseEntity, "utf-8");
            JSONObject jo = new JSONObject(content);
            access_token = jo.getStr("access_token");
        } catch (Exception e) {
            access_token = "";
            throw BizException.validFail("get请求异常", e);
        } finally {
            try {
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                throw BizException.validFail("流关闭异常", e);
            }
        }
        return access_token;
    }


}
