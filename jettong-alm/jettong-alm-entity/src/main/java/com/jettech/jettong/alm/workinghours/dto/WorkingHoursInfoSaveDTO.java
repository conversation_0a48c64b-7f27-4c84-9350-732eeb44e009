package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工时填报信息新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursInfoSaveDTO
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursInfoSaveDTO", description = "工时填报信息")
public class WorkingHoursInfoSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    @NotNull(message = "请填写事项类型")
    private WorkingHoursType type;
    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    private Long projectId;
    /**
     * 事项ID
     */
    @ApiModelProperty(value = "事项ID")
    private Long bizId;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    private String stateCode;
    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    @Size(max = 200, message = "事项名称，当时类型为其他事项时使用此字段长度不能超过200")
    private String name;
    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    @Size(max = 2000, message = "事项描述长度不能超过2000")
    private String description;
    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    @NotNull(message = "请填写工时时间，单位：小时。最小计量0.5")
    private Double duration;
    /**
     * 填报人
     */
    @ApiModelProperty(value = "填报人")
    @NotNull(message = "请填写填报人")
    private Long filledBy;
    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    @NotNull(message = "请填写填报时间")
    private LocalDateTime fillingTime;

    /**
     * 是否计费
     */
    @ApiModelProperty(value = "是否计费")
    private Boolean billed;

    @ApiModelProperty(value = "部门ID")
    private Long orgId;
}
