package com.jettech.jettong.alm.statistics.vo;

import com.jettech.jettong.alm.statistics.enumeration.TimeCycleEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 仪表板输入参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 仪表板输入参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.statistics
 * @className StatisticsInputVO
 * @date 2023/4/13 下午2:22
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ApiModel(value = "WorkItemTrendInputVO", description = "趋势统计图输入参数")
public class WorkItemTrendInputVO {
    /**
     * 筛选器ID
     */
    @ApiModelProperty("筛选器ID")
    @NotNull(message = "筛选器ID 不能为空")
    protected Long tableViewId;

    /**
     * X轴时间格式化
     */
    @ApiModelProperty("X轴，时间格式化")
    @NotNull(message = "时间格式化 不能为空")
    protected TimeCycleEnum timeCycle;

    /**
     * X天之内的筛选
     */
    @ApiModelProperty("X天之内")
    @NotNull(message = "X天之内范围 不能为空")
    protected Long days;

    /**
     * 统计字段，统计方式，count(*)：计数 sum：累积
     */
    @ApiModelProperty("y轴，统计方式：count(*) sum")
    @NotNull(message = "y轴，统计方式 不能为空")
    protected String y;

}
