package com.jettech.jettong.alm.workinghours.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_INFO_FEIGN_CLASS;


/**
 * 工时填报信息实体类
 * <AUTHOR>
 * @version 1.0
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursInfo
 * @date 2023-07-13
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@AllArgsConstructor
public class WorkingHoursInfoAllocationVO implements EchoVO {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    private Long id;

    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    @NotNull(message = "请填写事项类型")
    @TableField(value = "`type`")
    private WorkingHoursType type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    @ApiModelProperty(value = "来源项目ID")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfo.class)
    private Long sourceProjectId;
    /**
     * 事项ID
     */
    @ApiModelProperty(value = "事项ID")
    @TableField(value = "`biz_id`")
    private Long bizId;

    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    @Size(max = 200, message = "事项名称，当时类型为其他事项时使用此字段长度不能超过200")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    @Size(max = 2000, message = "事项描述长度不能超过2000")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    @NotNull(message = "请填写工时时间，单位：小时。最小计量0.5")
    @TableField(value = "`duration`")
    private Double duration;

    /**
     * 填报人
     */
    @ApiModelProperty(value = "填报人")
    @NotNull(message = "请填写填报人")
    @TableField(value = "`filled_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long filledBy;

    /**
     * 填报时间
     */
    @ApiModelProperty(value = "填报时间")
    @NotNull(message = "请填写填报时间")
    @TableField(value = "`filling_time`", condition = "date(%s) = date(#{%s})")
    private LocalDateTime fillingTime;

    /**
     * 是否已确认工时
     */
    @ApiModelProperty(value = "是否已确认工时")
    @NotNull(message = "请填写是否已确认工时")
    @TableField(value = "`verified`")
    private Boolean verified;

    /**
     * 工时是否有效
     */
    @ApiModelProperty(value = "工时是否有效")
    @TableField(value = "`valid`")
    private Boolean valid;

    /**
     * 是否计费
     */
    @ApiModelProperty(value = "是否计费")
    @TableField(value = "`billed`")
    private Boolean billed;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;

    private LocalDateTime createTime;

    @ApiModelProperty(value = "source=2 分摊人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long createdBy;

    @ApiModelProperty(value = "来源 1填报 2分摊")
    private Integer source;
}
