package com.jettech.jettong.alm.workflow.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 工作流信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className WorkflowInfo
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("workflow_info")
@ApiModel(value = "WorkflowInfo", description = "工作流信息")
@AllArgsConstructor
public class WorkflowInfo extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    @TableField(value = "`name`")
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    @Excel(name = "描述")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "使用中")
    @TableField(value = "`state`")
    @Excel(name = "使用中", replace = {"是_true", "否_false", "_null"})
    private Boolean state;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置")
    @TableField(value = "`readonly`")
    @Excel(name = "内置", replace = {"是_true", "否_false", "_null"})
    private Boolean readonly;

    /**
     * 工作流节点信息
     */
    @ApiModelProperty(value = "工作流节点信息")
    @TableField(exist = false)
    private List<WorkflowNode> workflowNodes;

    /**
     * 工作流流转关系信息
     */
    @ApiModelProperty(value = "工作流流转关系信息")
    @TableField(exist = false)
    private List<WorkflowTransition> workflowTransitions;

    /**
     * 工作流流转权限信息
     */
    @ApiModelProperty(value = "工作流流转权限信息")
    @TableField(exist = false)
    private List<WorkflowAuthority> workflowAuthorities;


    @Builder
    public WorkflowInfo(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            String name, String description, Boolean state, Boolean readonly, List<WorkflowNode> workflowNodes,
            List<WorkflowTransition> workflowTransitions, List<WorkflowAuthority> workflowAuthorities)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.name = name;
        this.description = description;
        this.state = state;
        this.readonly = readonly;
        this.workflowNodes = workflowNodes;
        this.workflowTransitions = workflowTransitions;
        this.workflowAuthorities = workflowAuthorities;
    }

}
