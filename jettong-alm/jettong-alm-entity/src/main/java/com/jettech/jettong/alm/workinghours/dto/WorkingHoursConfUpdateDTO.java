package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 工时填报配置修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报配置修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursConfUpdateDTO
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursConfUpdateDTO", description = "工时填报配置")
public class WorkingHoursConfUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 配置项的键
     */
    @ApiModelProperty(value = "配置项的键")
    @NotEmpty(message = "请填写配置项的键")
    @Size(max = 100, message = "配置项的键长度不能超过100")
    private String key;
    /**
     * 配置项名称
     */
    @ApiModelProperty(value = "配置项名称")
    @NotEmpty(message = "请填写配置项名称")
    @Size(max = 100, message = "配置项名称长度不能超过100")
    private String name;
    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    @NotEmpty(message = "请填写数据类型")
    @Size(max = 100, message = "数据类型长度不能超过100")
    private String type;
    /**
     * 配置项的元配置，用于前端渲染，json格式
     */
    @ApiModelProperty(value = "配置项的元配置，用于前端渲染，json格式")
    @Size(max = 65535, message = "配置项的元配置，用于前端渲染，json格式长度不能超过65,535")
    private String config;
    /**
     * 配置项的值
     */
    @ApiModelProperty(value = "配置项的值")
    @Size(max = 200, message = "配置项的值长度不能超过200")
    private String value;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;
}
