package com.jettech.jettong.alm.workinghours.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;


/**
 * 工时填报信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursInfo
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@ApiModel(value = "WorkingHoursInfo", description = "工时填报信息导出")
@AllArgsConstructor
public class WorkingHoursInfoExport  {

    private static final long serialVersionUID = 1L;

    @Excel(name = "工时日期", format = NORM_DATE_PATTERN, width = 15)
    private LocalDateTime fillingTime;


    @Excel(name = "工作项类型",width = 15)
    private String type;

    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    @Excel(name = "工作项名称",width = 30)
    private String name;

    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    @Excel(name = "事项描述",width = 30)
    private String description;

    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    @Excel(name = "工时")
    private Double duration;


    @Excel(name = "来源")
    private String source;

    @Excel(name = "填报时间", format = NORM_DATE_PATTERN,width = 15)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "审批状态")
    @Excel(name = "审批状态")
    private String approvalStatus;

    @Excel(name = "归属项目",width = 20)
    private String projectName;

    @Excel(name = "填报项目",width = 20)
    private String sourceProjectName;

    @ApiModelProperty(value = "填报人")
    @Excel(name = "填报人")
    private String filledBy;

    @Excel(name = "分摊人")
    private String allocationBy;
}
