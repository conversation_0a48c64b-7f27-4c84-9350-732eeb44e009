package com.jettech.jettong.alm.workinghours.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.jettech.basic.model.EchoVO;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_INFO_ID_CLASS;


/**
 * 工时分摊表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时分摊表实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.entity.workinghours
 * @className WorkingHoursAllocation
 * @date 2023-07-06
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("working_hours_allocation")
@ApiModel(value = "WorkingHoursAllocation", description = "工时分摊表")
@AllArgsConstructor
public class WorkingHoursAllocation implements EchoVO {

    private static final long serialVersionUID = 1L;


    @TableId(value = "id", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    protected Long id;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_FEIGN_CLASS , beanClass = User.class)
    protected Long createdBy;

    /**
     * 外键 工时id
     */
    @ApiModelProperty(value = "外键 工时id")
    @TableField(value = "working_hours_id")
    @Excel(name = "外键 工时id")
    private Long workingHoursId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @TableField(value = "project_id")
    @Excel(name = "项目id")
    @Echo(api = PROJECT_INFO_ID_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 分摊工时，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "分摊工时，单位：小时。最小计量0.5")
    @TableField(value = "duration")
    @Excel(name = "分摊工时，单位：小时。最小计量0.5")
    private Double duration;

    /**
     * 是否确认
     */
    @ApiModelProperty(value = "是否确认")
    @TableField(value = "verified")
    @Excel(name = "是否确认", replace = {"是_true", "否_false", "_null"})
    private Boolean verified;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    @TableField(value = "valid")
    @Excel(name = "是否有效", replace = {"是_true", "否_false", "_null"})
    private Boolean valid;

    @TableField(exist = false)
    private WorkingHoursInfo workingHoursInfo;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @Builder
    public WorkingHoursAllocation(Long id, Long createdBy, LocalDateTime createTime, 
                    Long workingHoursId, Long projectId, Double duration, Boolean verified, Boolean valid)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.workingHoursId = workingHoursId;
        this.projectId = projectId;
        this.duration = duration;
        this.verified = verified;
        this.valid = valid;
    }

}
