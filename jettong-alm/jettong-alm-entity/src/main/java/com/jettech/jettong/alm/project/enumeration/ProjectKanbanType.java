package com.jettech.jettong.alm.project.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 项目关联看板信息实体注释中生成的类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目关联看板信息实体注释中生成的类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.enumeration
 * @className ProjectKanbanType
 * @date 2021-11-17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ProjectKanbanType",
        description = "看板类型 #ProjectKanbanType{AGILE_KANBAN:敏捷看板;BUG_KANBAN:缺陷看板;TASK_KANBAN:任务看板;" +
                "CUSTOM_KANBAN:自定义看板-枚举")
public enum ProjectKanbanType implements BaseEnum
{

    /**
     * AGILE_KANBAN="敏捷看板"
     */
    AGILE_KANBAN("敏捷看板"),
    /**
     * 需求看板
     */
    REQUIREMENT_KANBAN("需求看板"),
    /**
     * BUG_KANBAN="缺陷看板"
     */
    BUG_KANBAN("缺陷看板"),
    /**
     * TASK_KANBAN="任务看板"
     */
    TASK_KANBAN("任务看板"),

    /**
     * IDEA_KANBAN="用户需求看板"
     */
    IDEA_KANBAN("用户需求看板"),

    /**
     * ISSUE_REQUIREMENT_KANBAN="需求中心需求看板"
     */
    ISSUE_REQUIREMENT_KANBAN("需求中心需求看板"),
    /**
     * CUSTOM_KANBAN="自定义看板"
     */
    CUSTOM_KANBAN("自定义看板"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ProjectKanbanType match(String val, ProjectKanbanType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ProjectKanbanType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ProjectKanbanType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码",
            allowableValues = "AGILE_KANBAN,REQUIREMENT_KANBAN,BUG_KANBAN,TASK_KANBAN,IDEA_KANBAN," +
                    "ISSUE_REQUIREMENT_KANBAN,CUSTOM_KANBAN",
            example = "AGILE_KANBAN")
    public String getCode()
    {
        return this.name();
    }

}
