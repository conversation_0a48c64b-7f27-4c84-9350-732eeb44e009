package com.jettech.jettong.alm.workflow.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 工作流流转关系信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作流流转关系信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className WorkflowTransition
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("workflow_transition")
@ApiModel(value = "WorkflowTransition", description = "工作流流转关系信息")
@AllArgsConstructor
public class WorkflowTransition extends SuperEntity<Long> implements Serializable
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 外键，所属工作流id
     */
    @ApiModelProperty(value = "标识，画图用")
    @NotNull(message = "请填写标识，画图用")
    @TableField(value = "`code`")
    @Excel(name = "标识，画图用")
    private String code;

    /**
     * 工作流id
     */
    @ApiModelProperty(value = "工作流id")
    @NotNull(message = "请填写工作流id")
    @TableField(value = "`workflow_id`")
    @Excel(name = "工作流id")
    private Long workflowId;

    /**
     * 源节点
     */
    @ApiModelProperty(value = "连线上文字")
    @TableField(value = "`name`")
    @Excel(name = "连线上文字")
    private String name;

    /**
     * 源节点
     */
    @ApiModelProperty(value = "源节点")
    @NotNull(message = "请填写源节点")
    @TableField(value = "`source`")
    @Excel(name = "源节点")
    private Long source;

    /**
     * 画图用
     */
    @ApiModelProperty(value = "画图用")
    @TableField(value = "`source_anchor`")
    @Excel(name = "画图用")
    private String sourceAnchor;

    /**
     * 目标节点
     */
    @ApiModelProperty(value = "目标节点")
    @NotNull(message = "请填写目标节点")
    @TableField(value = "`target`")
    @Excel(name = "目标节点")
    private Long target;

    /**
     * 画图用
     */
    @ApiModelProperty(value = "画图用")
    @TableField(value = "`target_anchor`")
    @Excel(name = "画图用")
    private String targetAnchor;

    /**
     * 连线坐标
     */
    @ApiModelProperty(value = "连线坐标")
    @TableField(value = "`waypoints`")
    @Excel(name = "连线坐标")
    private String waypoints;

    @Builder
    public WorkflowTransition(Long id, LocalDateTime createTime, Long createdBy,
            Long workflowId, String code, String name, Long source, String sourceAnchor, Long target,
            String targetAnchor,
            String waypoints)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.workflowId = workflowId;
        this.code = code;
        this.source = source;
        this.sourceAnchor = sourceAnchor;
        this.target = target;
        this.targetAnchor = targetAnchor;
        this.waypoints = waypoints;
    }

}
