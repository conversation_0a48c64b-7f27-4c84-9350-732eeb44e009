package com.jettech.jettong.alm.workinghours.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 工时填报审批记录实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报审批记录实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursConfirm
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("working_hours_confirm")
@ApiModel(value = "WorkingHoursConfirm", description = "工时填报审批记录")
@AllArgsConstructor
public class WorkingHoursConfirm extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();


    @ApiModelProperty(value = "工时信息表主键/工时分摊表主键")
    @NotNull(message = "请填写外键，工时信息表主键")
    @TableField(value = "`working_hours_id`")
    private Long workingHoursId;

    /**
     * 确认工时类型
     */
    @ApiModelProperty(value = "确认工时类型")
    @NotEmpty(message = "请填写确认工时类型")
    @Size(max = 20, message = "确认工时类型长度不能超过20")
    @TableField(value = "`type`", condition = LIKE)
    private WorkingHoursType type;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "请填写是否有效")
    @TableField(value = "`valid`")
    private Boolean valid;


    @Builder
    public WorkingHoursConfirm(Long id, Long createdBy, LocalDateTime createTime, 
                    Long workingHoursId, WorkingHoursType type, Boolean valid)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.workingHoursId = workingHoursId;
        this.type = type;
        this.valid = valid;
    }

}
