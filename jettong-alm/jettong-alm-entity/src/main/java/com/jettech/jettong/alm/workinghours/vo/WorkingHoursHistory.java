package com.jettech.jettong.alm.workinghours.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户填报历史
 * @projectName jettong
 * @package com.jettech.jettong.alm.workinghours.vo
 * @className WorkingHoursHistory
 * @date 2023/7/12 18:16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class WorkingHoursHistory
{
    @ApiModelProperty("用户每天总工时")
    private List<WorkingHoursDay> hoursDays;

    @ApiModelProperty("用户每天填报信息")
    private List<WorkingHoursInfoVO> userHistory;

    public WorkingHoursHistory(List<WorkingHoursDay> hoursDays,
            List<WorkingHoursInfoVO> userHistory)
    {
        this.hoursDays = hoursDays;
        this.userHistory = userHistory;
    }
}
