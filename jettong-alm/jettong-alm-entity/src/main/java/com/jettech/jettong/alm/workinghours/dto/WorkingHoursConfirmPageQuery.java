package com.jettech.jettong.alm.workinghours.dto;

import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 工时填报审批记录分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报审批记录分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.dto
 * @className WorkingHoursConfirm
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkingHoursConfirmPageQuery", description = "工时填报审批记录")
public class WorkingHoursConfirmPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键，工时信息表主键
     */
    @ApiModelProperty(value = "外键，工时信息表主键")
    private Long workingHoursId;
    /**
     * 确认工时类型
     */
    @ApiModelProperty(value = "确认工时类型")
    private WorkingHoursType type;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private Boolean valid;

}
