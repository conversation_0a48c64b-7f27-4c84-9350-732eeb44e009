package com.jettech.jettong.alm.workinghours.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_INFO_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.STATE_CODE_CLASS;


/**
 * 工时填报信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.entity
 * @className WorkingHoursInfo
 * @date 2022-09-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "WorkingHoursInfoVO", description = "工时填报信息")
@AllArgsConstructor
public class WorkingHoursInfoVO implements EchoVO {

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 事项类型
     */
    @ApiModelProperty(value = "事项类型")
    @NotNull(message = "请填写事项类型")
    @TableField(value = "`type`")
    private WorkingHoursType type;

    /**
     * 项目ID
     */
    @ApiModelProperty(value = "项目ID")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 事项ID
     */
    @ApiModelProperty(value = "事项ID")
    private Long bizId;

    /**
     * 事项名称，当时类型为其他事项时使用此字段
     */
    @ApiModelProperty(value = "事项名称，当时类型为其他事项时使用此字段")
    private String name;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    private String stateCode;

    /**
     * 事项描述
     */
    @ApiModelProperty(value = "事项描述")
    private String description;

    /**
     * 工时时间，单位：小时。最小计量0.5
     */
    @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
    private Double duration;

    /**
     * 填报人
     */
    @ApiModelProperty(value = "填报人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long filledBy;

    private List<FilledInfo> filledInfos;

    @Data
    public class FilledInfo{

        private Long id;

        /**
         * 工时时间，单位：小时。最小计量0.5
         */
        @ApiModelProperty(value = "工时时间，单位：小时。最小计量0.5")
        private Double duration;


        /**
         * 填报时间
         */
        @ApiModelProperty(value = "填报时间")
        private LocalDate fillingTime;

        /**
         * 是否已确认工时
         */
        @ApiModelProperty(value = "是否已确认工时")
        private Boolean verified;

        /**
         * 工时是否有效
         */
        @ApiModelProperty(value = "工时是否有效")
        private Boolean valid;
    }

}
