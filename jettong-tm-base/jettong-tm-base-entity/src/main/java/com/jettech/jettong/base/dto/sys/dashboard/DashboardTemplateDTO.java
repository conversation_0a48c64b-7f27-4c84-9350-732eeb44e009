package com.jettech.jettong.base.dto.sys.dashboard;

import com.jettech.jettong.base.entity.sys.dashboard.Dashboard;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 根据模板生成 工作台
 *
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.dto.sys.dashboard
 * @className DashboardFromTemplateDTO
 * @date 2022/4/22 14:53
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@ApiModel(value = "DashboardTemplateDTO", description = "工作台模板信息")
public class DashboardTemplateDTO extends Dashboard {

    private DashboardTemplateParamDTO param;

}
