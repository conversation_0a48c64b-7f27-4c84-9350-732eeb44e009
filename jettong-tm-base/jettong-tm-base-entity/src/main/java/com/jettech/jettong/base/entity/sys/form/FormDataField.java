package com.jettech.jettong.base.entity.sys.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据表的字段实体类
 * <AUTHOR>
 * @version 1.0
 * @description 数据表的字段实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className FormDataField
 * @date 2022-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_form_data_field")
@ApiModel(value = "FormDataField", description = "数据表的字段")
@AllArgsConstructor
public class FormDataField extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 数据表ID，外键
     */
    @ApiModelProperty(value = "数据表ID，外键")
    @NotNull(message = "请填写数据表ID，外键")
    @TableField(value = "`data_id`")
    private Long dataId;

//    /**
//     * 数据表字段标识
//     */
//    @ApiModelProperty(value = "数据表字段标识")
//    @NotEmpty(message = "请填写数据表字段标识")
//    @Size(max = 50, message = "数据表字段标识长度不能超过50")
//    @TableField(value = "`field`", condition = LIKE)
//    private String field;

    /**
     * 数据表字段名称
     */
    @ApiModelProperty(value = "数据表字段名称")
    @NotEmpty(message = "请填写数据表字段名称")
    @Size(max = 50, message = "数据表字段名称长度不能超过50")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 数据表描述
     */
    @ApiModelProperty(value = "数据表字段描述")
    @Size(max = 255, message = "数据表字段描述长度不能超过255")
    @TableField(value = "`desc`")
    private String desc;

    /**
     * 是否为主字段
     */
    @ApiModelProperty(value = "是否为主字段")
    @NotNull(message = "请填写是否为主字段")
    @TableField(value = "`primary`")
    private Boolean primary;

    @ApiModelProperty(value = "是否作为显示字段")
    @NotNull(message = "请填写是否作为显示字段")
    @TableField(value = "`display_field`")
    private Boolean displayField;

    /**
     * 数据表配置
     */
    @ApiModelProperty(value = "数据表配置")
    @NotEmpty(message = "请填写数据表配置")
    @Size(max = 500, message = "数据表配置长度不能超过500")
    @TableField(value = "`config`", condition = LIKE)
    private String config;


    @Builder
    public FormDataField(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy, 
                    Long dataId, String name, Boolean primary,Boolean displayField, String config, String desc)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.dataId = dataId;
        this.name = name;
        this.desc = desc;
        this.primary = primary;
        this.displayField = displayField;
        this.config = config;
    }

}
