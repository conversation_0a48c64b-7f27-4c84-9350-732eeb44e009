package com.jettech.jettong.base.entity.sys.engine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 动态节点参数表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 动态节点参数表实体类
 * @projectName jettong
 * @package com.jettech.jettong.va-base.entity
 * @className EngineJenkinsCloudExtended
 * @date 2023-01-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_engine_jenkins_cloud_extended")
@ApiModel(value = "EngineJenkinsCloudExtended", description = "动态节点参数表")
@AllArgsConstructor
public class EngineJenkinsCloudExtended
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * y
     * 云节点id
     */
    @ApiModelProperty(value = "云节点id")
    @NotNull(message = "请填写云节点id")
    @TableId(value = "`jenkins_cloud_id`", type = IdType.INPUT)
    private Long jenkinsCloudId;

    /**
     * 属性key
     */
    @ApiModelProperty(value = "属性key")
    @Size(max = 255, message = "属性key长度不能超过255")
    @TableField(value = "`key`", condition = LIKE)
    private String key;

    /**
     * 属性value
     */
    @ApiModelProperty(value = "属性value")
    @Size(max = 255, message = "属性value长度不能超过255")
    @TableField(value = "`value`", condition = LIKE)
    private String value;


    @Builder
    public EngineJenkinsCloudExtended(
                    Long jenkinsCloudId, String key, String value)
    {
        this.jenkinsCloudId = jenkinsCloudId;
        this.key = key;
        this.value = value;
    }

}
