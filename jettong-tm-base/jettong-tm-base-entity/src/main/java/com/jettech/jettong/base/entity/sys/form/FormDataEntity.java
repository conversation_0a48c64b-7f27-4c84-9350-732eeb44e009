package com.jettech.jettong.base.entity.sys.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据表表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 数据表表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className FormData
 * @date 2022-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_form_data")
@ApiModel(value = "FormData", description = "数据表表")
@AllArgsConstructor
public class FormDataEntity extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 数据表名称
     */
    @ApiModelProperty(value = "数据表名称")
    @NotEmpty(message = "请填写数据表名称")
    @Size(max = 50, message = "数据表名称长度不能超过50")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 数据表描述
     */
    @ApiModelProperty(value = "数据表描述")
    @Size(max = 255, message = "数据表描述长度不能超过255")
    @TableField(value = "`desc`")
    private String desc;

    @ApiModelProperty(value = "数据表字段")
    @TableField(exist = false)
    private List<FormDataField> fields;

    @Builder
    public FormDataEntity(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
                    String name, String desc)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.desc = desc;
    }

}
