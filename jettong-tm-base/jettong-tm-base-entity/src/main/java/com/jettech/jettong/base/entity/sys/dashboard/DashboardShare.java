package com.jettech.jettong.base.entity.sys.dashboard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.util.auth.AuthEntity;
import com.jettech.jettong.common.util.auth.enumeration.AuthAction;
import com.jettech.jettong.common.util.auth.enumeration.AuthType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 工作台分享信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 工作台分享信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.sys
 * @className DashboardShare
 * @date 2023-04-27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dashboard_share")
@ApiModel(value = "DashboardShare", description = "工作台分享信息")
@AllArgsConstructor
public class DashboardShare extends AuthEntity implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 仪表板ID
     */
    @ApiModelProperty(value = "仪表板ID")
    @NotNull(message = "请填写仪表板ID")
    @TableField(value = "`dashboard_id`")
    private Long sourceId;

    /**
     * 授权的类型，
     */
    @ApiModelProperty(value = "授权的类型，")
    @NotEmpty(message = "请填写授权的类型，")
    @Size(max = 10, message = "授权的类型，长度不能超过10")
    @TableField(value = "`auth_type`")
    private AuthType authType;

    /**
     * 授权的ID
     */
    @ApiModelProperty(value = "授权的ID")
    @NotEmpty(message = "请填写授权的ID")
    @Size(max = 10, message = "授权的ID长度不能超过10")
    @TableField(value = "`auth_id`", condition = LIKE)
    private Long authId;

    /**
     * 授权的操作，VIEW、EDIT
     */
    @ApiModelProperty(value = "授权的操作，VIEW、EDIT")
    @NotEmpty(message = "请填写授权的操作，VIEW、EDIT")
    @Size(max = 10, message = "授权的操作，VIEW、EDIT长度不能超过10")
    @TableField(value = "`auth_action`")
    private AuthAction authAction;


    @Builder
    public DashboardShare(Long id, Long createdBy, LocalDateTime createTime,
                          Long sourceId, AuthType authType, Long authId, AuthAction authAction)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.sourceId = sourceId;
        this.authType = authType;
        this.authId = authId;
        this.authAction = authAction;
    }
}
