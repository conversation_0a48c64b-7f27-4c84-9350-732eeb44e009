package com.jettech.jettong.base.enumeration.sys.form;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * 自定义表单属性类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单属性类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.form
 * @className CustomFormFieldType
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CustomFormFieldType",
        description = "自定义表单属性类型枚举 #CustomFormFieldType{INPUT:文本框;TEXTAREA:文本域;SELECT:下拉框;NUMBER:数字框;" +
                "WORK_ITEM:工作项-枚举")
public enum CustomFormFieldType implements BaseEnum
{
    /**
     * 小数类型
     */
    FLOAT("小数类型"),
    /**
     * 整数类型
     */
    INT("整数类型"),
    /**
     * 组织机构
     */
    ORG("组织机构"),
    /**
     * 项目用户
     */
    PROJECTUSER("项目用户"),
    /**
     * 文本框
     */
    INPUT("文本框"),
    /**
     * 文本域
     */
    TEXTAREA("文本域"),
    /**
     * 文本编辑器
     */
    EDITOR("文本编辑器"),
    /**
     * 下拉框
     */
    SELECT("下拉框"),
    /**
     * 数字框
     */
    NUMBER("数字框"),
    /**
     * 时间选择
     */
    DATE("时间选择"),
    /**
     * 文本框
     */
    FILE("文件框"),
    /**
     * 图标
     */
    ICON("图标"),
    /**
     * 用户下拉框
     */
    USER("用户下拉框"),
    /**
     * 关联类型
     */
    LINKED("关联类型"),
    /**
     * 引用类型
     */
    QUOTE("引用类型"),
    /**
     * 超链接
     */
    LINK("超链接"),
    /**
     * 自定义
     */
    CUSTOM("自定义类型");

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static CustomFormFieldType match(String val, CustomFormFieldType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static CustomFormFieldType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(CustomFormFieldType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "属性类型", allowableValues = "INPUT,TEXTAREA,SELECT,NUMBER", example = "INPUT")
    public String getCode()
    {
        return this.name();
    }

}
