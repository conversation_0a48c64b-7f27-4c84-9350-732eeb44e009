package com.jettech.jettong.base.entity.sys.engine;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.common.enumeration.EngineInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.*;

/**
 * 引擎信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.engine
 * @className Engine
 * @date 2021-10-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_engine")
@ApiModel(value = "Engine", description = "引擎信息")
@AllArgsConstructor
public class Engine implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @TableId(value = "`id`", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    protected Long id;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`")
    private String description;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类字典")
    @NotEmpty(message = "请选择引擎分类")
    @TableField(value = "`classify`")
    @Excel(name = "引擎分类")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENGINE_CLASSIFY)
    private String classify;

    /**
     * 引擎类型
     */
    @ApiModelProperty(value = "引擎类型字典")
    @NotNull(message = "请选择引擎类型")
    @TableField(value = "`instance`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENGINE_INSTANCE)
    @Excel(name = "引擎类型")
    private EngineInstance instance;

    /**
     * 引擎访问url
     */
    @ApiModelProperty(value = "引擎访问url")
    @Size(max = 500, message = "引擎访问url长度不能超过500")
    @TableField(value = "`engine_url`")
    @Excel(name = "引擎访问url")
    private String engineUrl;

    /**
     * 引擎登录用户
     */
    @ApiModelProperty(value = "引擎登录用户")
    @Size(max = 200, message = "引擎登录用户长度不能超过200")
    @TableField(value = "`engine_login_name`")
    @Excel(name = "引擎登录用户")
    private String engineLoginName;

    /**
     * 引擎密码
     */
    @ApiModelProperty(value = "引擎密码")
    @Size(max = 200, message = "引擎密码长度不能超过200")
    @TableField(value = "`engine_password`")
    private String enginePassword;

    /**
     * 引擎token
     */
    @ApiModelProperty(value = "引擎token")
    @Size(max = 200, message = "引擎token长度不能超过200")
    @TableField(value = "`engine_token`")
    private String engineToken;

    /**
     * jenkins slave 对应的master 引擎id
     */
    @ApiModelProperty(value = "jenkins slave 对应的master 引擎id")
    @TableField(value = "`parent_id`")
    @Excel(name = "jenkins slave 对应的master 引擎id")
    private Long parentId;

    /**
     * 引擎所属服务器id
     */
    @ApiModelProperty(value = "引擎所属服务器id")
    @TableField(value = "`host_id`")
    @Excel(name = "引擎所属服务器id")
    private Long hostId;

    /**
     * 引擎所属服务器IP
     */
    @ApiModelProperty(value = "引擎所属服务器IP")
    @TableField(value = "`engine_ip`")
    @Excel(name = "引擎所属服务器IP")
    private String engineIp;

    /**
     * 巡检策略
     */
    @ApiModelProperty(value = "巡检策略")
    @TableField(value = "`patrol_strategy`")
    @Excel(name = "巡检策略")
    private Integer patrolStrategy;

    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "创建机构")
    private Long orgId;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    protected Long createdBy;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField(value = "`updated_by`", fill = FieldFill.INSERT_UPDATE)
    protected Long updatedBy;

    /**
     * 引擎扩展属性
     */
    @ApiModelProperty(value = "引擎扩展属性")
    @TableField(exist = false)
    private List<EngineExtended> engineExtendeds;

    /**
     * 维护人
     */
    @ApiModelProperty(value = "引擎维护人")
    @TableField(exist = false)
    private List<EngineMaintainer> engineMaintainers;

    /**
     * 是否查询JenkinsSlave引擎
     */
    @ApiModelProperty(value = "是否查询jenkinsSlave引擎")
    @TableField(exist = false)
    private Boolean queryJenkinsSlave;

    @Builder
    public Engine(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String name, Boolean state, String description, String classify, EngineInstance instance,
            String engineUrl, String engineLoginName, String enginePassword, String engineToken, Long parentId,
            Long hostId, String engineIp,
            Integer patrolStrategy, Long orgId, List<EngineExtended> engineExtendeds,
            List<EngineMaintainer> engineMaintainers)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.state = state;
        this.description = description;
        this.classify = classify;
        this.instance = instance;
        this.engineUrl = engineUrl;
        this.engineLoginName = engineLoginName;
        this.enginePassword = enginePassword;
        this.engineToken = engineToken;
        this.parentId = parentId;
        this.hostId = hostId;
        this.engineIp = engineIp;
        this.patrolStrategy = patrolStrategy;
        this.orgId = orgId;
        this.engineExtendeds = engineExtendeds;
        this.engineMaintainers = engineMaintainers;
    }

}
