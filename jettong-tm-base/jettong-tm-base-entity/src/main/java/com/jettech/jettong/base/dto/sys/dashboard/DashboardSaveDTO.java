package com.jettech.jettong.base.dto.sys.dashboard;

import com.jettech.jettong.base.entity.sys.dashboard.DashboardComponent;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 工作台信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto
 * @className DashboardSaveDTO
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DashboardSaveDTO", description = "工作台信息")
public class DashboardSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 是否默认工作台
     */
    @ApiModelProperty(value = "是否默认工作台")
    private Boolean isDefault;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "类型，4没有组件固定页面")
    private String type;
    /**
     * 工作台组件信息
     */
    @ApiModelProperty(value = "工作台组件信息")
    private List<DashboardComponent> dashboardComponents;

}
