package com.jettech.jettong.base.entity.sys.dashboard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;


/**
 * 工作台组件信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台组件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dashboard
 * @className DashboardComponent
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dashboard_component")
@ApiModel(value = "DashboardComponent", description = "工作台组件信息")
@AllArgsConstructor
public class DashboardComponent extends SuperEntity<Long>
{

    private static final long serialVersionUID = 1L;

    /**
     * 工作台id或模板id
     */
    @ApiModelProperty(value = "工作台id或模板id")
    @TableField(value = "`dashboard_id`")
    private Long dashboardId;

    /**
     * 所属用户id
     */
    @ApiModelProperty(value = "所属用户id")
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 内置，内置数据为工作台模板组件
     */
    @ApiModelProperty(value = "内置，内置数据为工作台模板组件")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    /**
     * 组件唯一编号
     */
    @ApiModelProperty(value = "组件唯一编号")
    @Size(max = 100, message = "组件唯一编号长度不能超过100")
    @TableField(value = "`widget_code`")
    private String widgetCode;

    /**
     * 组件x坐标
     */
    @ApiModelProperty(value = "组件x坐标")
    @TableField(value = "`x`")
    private Integer x;

    /**
     * 组件y坐标
     */
    @ApiModelProperty(value = "组件y坐标")
    @TableField(value = "`y`")
    private Integer y;

    /**
     * 组件宽
     */
    @ApiModelProperty(value = "组件宽")
    @TableField(value = "`w`")
    private Integer w;

    /**
     * 组件高
     */
    @ApiModelProperty(value = "组件高")
    @TableField(value = "`h`")
    private Integer h;

    /**
     * 组件查询条件
     */
    @ApiModelProperty(value = "组件查询条件")
    @TableField(value = "`query_params`")
    private String queryParams;


    @Builder
    public DashboardComponent(Long id, Long createdBy, LocalDateTime createTime,
            Long dashboardId, Long userId, Boolean readonly, String widgetCode, Integer x,
            Integer y, Integer w, Integer h, String queryParams)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.dashboardId = dashboardId;
        this.userId = userId;
        this.readonly = readonly;
        this.widgetCode = widgetCode;
        this.x = x;
        this.y = y;
        this.w = w;
        this.h = h;
        this.queryParams = queryParams;
    }

}
