package com.jettech.jettong.base.entity.sys.dashboard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.util.auth.enumeration.AuthAction;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 工作台信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dashboard
 * @className Dashboard
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dashboard")
@ApiModel(value = "Dashboard", description = "工作台信息")
@AllArgsConstructor
public class Dashboard extends Entity<Long> implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 所属用户id
     */
    @ApiModelProperty(value = "所属用户id")
    @TableField(value = "`user_id`")
    @Echo(api = USER_ID_FEIGN_CLASS)
    private Long userId;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(value = "`sort`")
    private Integer sort;

    /**
     * 内置
     */
    @ApiModelProperty(value = "内置，内置数据为工作台模板")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    @ApiModelProperty(value = "类型，4没有组件固定页面")
    @TableField(value = "`type`")
    private String type;
    /**
     * 工作台组件信息
     */
    @ApiModelProperty(value = "工作台组件信息")
    @TableField(exist = false)
    private List<DashboardComponent> dashboardComponents;

    /**
     * 工作台分享信息
     */
    @ApiModelProperty(value = "工作台组件信息")
    @TableField(exist = false)
    private List<AuthAction> authActions;

    /**
     * 非数据库字段，是否默认工作台
     */
    @ApiModelProperty(value = "是否默认工作台")
    @TableField(exist = false)
    private Boolean isDefault;


    @Builder
    public Dashboard(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime,
            Long userId, Boolean isDefault, String name, String description, Integer sort, Boolean readonly,
            List<DashboardComponent> dashboardComponents)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.userId = userId;
        this.isDefault = isDefault;
        this.name = name;
        this.description = description;
        this.sort = sort;
        this.readonly = readonly;
        this.dashboardComponents = dashboardComponents;
    }

}
