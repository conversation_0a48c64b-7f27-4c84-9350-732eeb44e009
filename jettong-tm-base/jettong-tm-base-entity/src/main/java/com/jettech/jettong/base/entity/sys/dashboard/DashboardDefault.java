package com.jettech.jettong.base.entity.sys.dashboard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;


/**
 * 工作台信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.dashboard
 * @className Dashboard
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_dashboard_default")
@ApiModel(value = "Dashboard", description = "用户默认工作台信息")
public class DashboardDefault implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 所属用户id
     */
    @ApiModelProperty(value = "所属用户id")
    @TableField(value = "`user_id`")
    private Long userId;

    /**
     * 工作台id
     */
    @ApiModelProperty(value = "工作台id")
    @TableField(value = "`dashboard_id`")
    private Long dashboardId;

    @Builder
    public DashboardDefault(Long userId, Long dashboardId)
    {
        this.userId = userId;
        this.dashboardId = dashboardId;
    }
}
