package com.jettech.jettong.base.entity.sys.engine;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;

/**
 * 引擎授权机构实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎授权机构实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.engine
 * @className EngineOrg
 * @date 2021-10-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_engine_org")
@ApiModel(value = "EngineOrg", description = "引擎授权机构")
@AllArgsConstructor
public class EngineOrg implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @NotNull(message = "请填写引擎id")
    @TableId(value = "`engine_id`", type = IdType.INPUT)
    private Long engineId;

    /**
     * 机构id #sys_org@Echo(api = ORG_ID_CLASS, beanClass = Org.class)
     */
    @ApiModelProperty(value = "机构id")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 是否创建机构
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;

    @Builder
    public EngineOrg(Long engineId, Long orgId, Boolean isCreate)
    {
        this.engineId = engineId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }
}
