package com.jettech.jettong.base.dto.sys.engine;

import com.jettech.jettong.base.entity.sys.engine.EngineExtended;
import com.jettech.jettong.base.entity.sys.engine.EngineMaintainer;
import com.jettech.jettong.common.enumeration.EngineInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 引擎信息表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎信息表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.engine
 * @className EngineSaveDTO
 * @date 2021-10-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineSaveDTO", description = "引擎信息")
public class EngineSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    private String name;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类字典")
    @NotEmpty(message = "请选择引擎分类")
    private String classify;

    /**
     * 引擎类型
     */
    @ApiModelProperty(value = "引擎类型字典")
    @NotNull(message = "请选择引擎类型")
    private EngineInstance instance;

    /**
     * 引擎访问url
     */
    @ApiModelProperty(value = "引擎访问url")
    @Size(max = 500, message = "引擎访问url长度不能超过500")
    private String engineUrl;

    /**
     * 引擎登录用户
     */
    @ApiModelProperty(value = "引擎登录用户")
    @Size(max = 200, message = "引擎登录用户长度不能超过200")
    private String engineLoginName;

    /**
     * 引擎密码
     */
    @ApiModelProperty(value = "引擎密码")
    @Size(max = 200, message = "引擎密码长度不能超过200")
    private String enginePassword;

    /**
     * 引擎token
     */
    @ApiModelProperty(value = "引擎token")
    @Size(max = 200, message = "引擎token长度不能超过200")
    private String engineToken;

    /**
     * jenkins slave 对应的master 引擎id
     */
    @ApiModelProperty(value = "jenkins slave 对应的master 引擎id")
    private Long parentId;

    /**
     * 引擎所属服务器id
     */
    @ApiModelProperty(value = "引擎所属服务器id")
    private Long hostId;

    /**
     * 引擎扩展属性
     */
    @ApiModelProperty(value = "引擎扩展属性")
    private List<EngineExtended> engineExtendeds;

    /**
     * 引擎维护人
     */
    @ApiModelProperty(value = "引擎维护人")
    private List<EngineMaintainer> engineMaintainers;
}
