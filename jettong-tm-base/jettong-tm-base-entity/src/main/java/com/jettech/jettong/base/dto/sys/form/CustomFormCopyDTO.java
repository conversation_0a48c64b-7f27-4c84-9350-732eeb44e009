package com.jettech.jettong.base.dto.sys.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 自定义表单信息表复制实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单信息表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.form
 * @className CustomFormCopyDTO
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "CustomFormCopyDTO", description = "自定义表单信息表")
public class CustomFormCopyDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 要复制的表单id
     */
    @ApiModelProperty(value = "要复制的表单id")
    @NotNull(message = "请填写要复制的表单id")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @NotEmpty(message = "请填写业务类型")
    @Size(max = 100, message = "业务类型长度不能超过100")
    private String bizType;

}
