package com.jettech.jettong.base.entity.job;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 定时任务和业务关联信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 定时任务和业务关联信息
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.job
 * @className JobBiz
 * @date 2023-08-02
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("jettong_job_biz")
@ApiModel(value = "JobBiz", description = "定时任务和业务关联信息")
public class JobBiz implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @TableField(value = "`biz_type`")
    private String bizType;

    /**
     * 业务id
     */
    @ApiModelProperty(value = "业务id")
    @TableField(value = "`biz_id`")
    private Long bizId;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    @TableField(value = "`job_id`")
    private Integer jobId;

    @Builder
    public JobBiz(String bizType, Long bizId, Integer jobId)
    {
        this.bizType = bizType;
        this.bizId = bizId;
        this.jobId = jobId;
    }
}
