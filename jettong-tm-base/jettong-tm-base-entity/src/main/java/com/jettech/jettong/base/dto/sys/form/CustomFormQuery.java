package com.jettech.jettong.base.dto.sys.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 自定义表单信息表查询实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单信息表查询实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.form
 * @className CustomFormQuery
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "CustomFormQuery", description = "自定义表单信息表")
public class CustomFormQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称，模糊匹配")
    private String name;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private List<String> bizType;

    /**
     * 内置表单
     */
    @ApiModelProperty(value = "内置表单")
    private Boolean readonly;

    /**
     * 是否被使用
     */
    @ApiModelProperty(value = "是否被使用")
    private Boolean isUse;

}
