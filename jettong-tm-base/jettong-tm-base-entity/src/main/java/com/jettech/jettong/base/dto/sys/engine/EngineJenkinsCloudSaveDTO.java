package com.jettech.jettong.base.dto.sys.engine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 动态节点主表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 动态节点主表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.va-base.dto
 * @className EngineJenkinsCloudSaveDTO
 * @date 2023-01-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineJenkinsCloudSaveDTO", description = "动态节点主表")
public class EngineJenkinsCloudSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 255, message = "名称长度不能超过255")
    private String name;
    /**
     * 类型：1-kubernetes；2-pod；3-container
     */
    @ApiModelProperty(value = "类型：1-kubernetes；2-pod；3-container")
    private Integer type;
    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    private Long engineId;
    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    private Long parentId;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 1000, message = "描述长度不能超过1000")
    private String description;
}
