package com.jettech.jettong.base.dto.sys.dashboard;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * 根据模板生成 工作台 所需的参数
 *
 * 后期需增加新的参数时，需修改
 * 当前类增加 对应字段
 * ① DashboardComponent 表字段 QueryParams对应 DashboardComponentQueryParams 序列化实体类
 *    添加对应的字段 {@link DashboardComponentQueryParams}
 * ② 替换模板组件中存的 id 和 name 时，添加对应的字段
 *      {@link com.jettech.jettong.base.service.sys.dashboard.impl.DashboardServiceImpl#replaceParam }
 * ③ 返回模板信息时，根据条件将该字段确认返回给前端
 *      {@link com.jettech.jettong.base.service.sys.dashboard.impl.DashboardServiceImpl#buildParams }
 *
 * <AUTHOR>
 * @version 1.0
 * @description 根据模板生成 工作台 所需的参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.dto.sys.dashboard
 * @className DashboardFromTemplateDTO
 * @date 2022/4/22 14:53
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@ApiModel(value = "DashboardTemplateParamDTO", description = "工作台信息")
public class DashboardTemplateParamDTO {

    @ApiModelProperty(value = "工作台名称")
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "项目")
    private Pair project;
    @ApiModelProperty(value = "产品")
    private Pair product;
    @ApiModelProperty(value = "迭代计划")
    private Pair plan;

    @Data
    public static class Pair {
        @ApiModelProperty(value = "资源ID")
        private String id;
        @ApiModelProperty(value = "资源名称")
        private String name;
    }

}
