package com.jettech.jettong.base.entity.sys.engine;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 引擎维护人信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎维护人信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity
 * @className com.jettech.jettong.base.entity.sys.engine
 * @date 2021-10-28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("sys_engine_maintainer")
@ApiModel(value = "EngineMaintainer", description = "引擎维护人信息")
@AllArgsConstructor
public class EngineMaintainer implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    @NotNull(message = "请填写用户id")
    @TableField(value = "`user_id`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long userId;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @NotNull(message = "请填写引擎id")
    @TableField(value = "`engine_id`")
    private Long engineId;

    @Builder
    public EngineMaintainer(Long userId, Long engineId)
    {
        this.userId = userId;
        this.engineId = engineId;
    }

}
