package com.jettech.jettong.base.entity.sys.form;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自定义表单信息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.form
 * @className CustomForm
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_custom_form")
@ApiModel(value = "CustomForm", description = "自定义表单信息表")
@AllArgsConstructor
public class CustomForm extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty(value = "表单名称")
    @NotEmpty(message = "请填写")
    @Size(max = 100, message = "长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @NotEmpty(message = "请填写业务类型")
    @Size(max = 100, message = "业务类型长度不能超过100")
    @TableField(value = "`biz_type`")
    private String bizType;

    /**
     * 内置表单
     */
    @ApiModelProperty(value = "内置表单")
    @TableField(value = "`readonly`")
    private Boolean readonly;

    /**
     * 是否被使用
     */
    @ApiModelProperty(value = "是否被使用")
    @NotNull(message = "请填写是否被使用")
    @TableField(value = "`is_use`")
    private Boolean isUse;

    /**
     * 非数据库字段，自定义表单属性信息
     */
    @ApiModelProperty(value = "自定义表单属性信息")
    @TableField(exist = false)
    private List<CustomFormField> customFormFields;


    @Builder
    public CustomForm(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String name, String description, String bizType, Boolean readonly, Boolean isUse,
            List<CustomFormField> customFormFields)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.description = description;
        this.bizType = bizType;
        this.readonly = readonly;
        this.isUse = isUse;
        this.customFormFields = customFormFields;
    }

}
