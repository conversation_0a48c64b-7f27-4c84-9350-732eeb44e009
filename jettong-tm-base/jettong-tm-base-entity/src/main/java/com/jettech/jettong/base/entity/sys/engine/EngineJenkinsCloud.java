package com.jettech.jettong.base.entity.sys.engine;

import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import com.jettech.basic.model.EchoVO;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 动态节点主表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 动态节点主表实体类
 * @projectName jettong
 * @package com.jettech.jettong.va-base.entity
 * @className EngineJenkinsCloud
 * @date 2023-01-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_engine_jenkins_cloud")
@ApiModel(value = "EngineJenkinsCloud", description = "动态节点主表")
@AllArgsConstructor
public class EngineJenkinsCloud extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 255, message = "名称长度不能超过255")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 类型：1-kubernetes；2-pod；3-container
     */
    @ApiModelProperty(value = "类型：1-kubernetes；2-pod；3-container")
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @TableField(value = "`engine_id`")
    private Long engineId;

    /**
     * 父id
     */
    @ApiModelProperty(value = "父id")
    @TableField(value = "`parent_id`")
    private Long parentId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @TableField(value = "`org_id`")
    private Long orgId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 1000, message = "描述长度不能超过1000")
    @TableField(value = "`description`")
    private String description;


    @Builder
    public EngineJenkinsCloud(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
                    String name, Integer type, Long engineId, Long parentId, Long orgId, String description)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.type = type;
        this.engineId = engineId;
        this.parentId = parentId;
        this.orgId = orgId;
        this.description = description;
    }

}
