package com.jettech.jettong.base.entity.sys.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 数据表的行信息 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 数据表的行信息 实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className FormDataField
 * @date 2022-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_form_data_row")
@ApiModel(value = "FormDataRow", description = "数据表的行信息")
@AllArgsConstructor
public class FormDataRow extends SuperEntity<Long> implements EchoVO, Comparable<FormDataRow>
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 数据表ID，外键
     */
    @ApiModelProperty(value = "数据表ID，外键")
    @NotNull(message = "请填写数据表ID，外键")
    @TableField(value = "`data_id`")
    private Long dataId;

    /**
     * 行排序
     */
    @ApiModelProperty(value = "行排序")
    @NotNull(message = "请填写 行排序")
    @TableField(value = "`sort`")
    private Integer sort;

    @TableField(exist = false)
    private Map<Long, String> columns;

    public static int compare(FormDataRow row1, FormDataRow row2) {
        if (row1 == null || row2 == null) {
            return 0;
        }
        if (row1.getSort() == null || row2.getSort() == null) {
            return 0;
        }
        return row1.getSort() - row2.getSort();
    }

    @Override
    public int compareTo(FormDataRow o) {
        return FormDataRow.compare(this, o);
    }

    @Builder
    public FormDataRow(Long id, LocalDateTime createTime, Long createdBy, Long dataId, Integer sort) {
        super(id, createTime, createdBy);
        this.dataId = dataId;
        this.sort = sort;
    }

}
