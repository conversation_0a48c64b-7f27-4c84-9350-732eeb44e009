package com.jettech.jettong.base.dto.sys.engine;

import com.jettech.jettong.common.enumeration.EngineInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 引擎信息表对外查询接口实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎信息表对外查询接口实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.engine
 * @className EngineQuery
 * @date 2021-10-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineQuery", description = "引擎信息")
public class EngineQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    private String name;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    private Boolean state;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类")
    private String classify;

    /**
     * 引擎类型
     */
    @ApiModelProperty(value = "引擎类型")
    private EngineInstance instance;

    /**
     * 引擎所属服务器id
     */
    @ApiModelProperty(value = "引擎所属服务器id")
    private Long hostId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

    /**
     * JenkinsMasterId
     */
    @ApiModelProperty(value = "JenkinsMasterId")
    private Long parentId;

    /**
     * 是否查询JenkinsSlave引擎
     */
    @ApiModelProperty(value = "是否查询jenkinsSlave引擎")
    private Boolean queryJenkinsSlave;

}
