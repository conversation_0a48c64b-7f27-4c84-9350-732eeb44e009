package com.jettech.jettong.base.entity.sys.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.enumeration.sys.form.CustomFormFieldType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 自定义表单字段配置信息表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单字段配置信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.entity.sys.form
 * @className CustomFormField
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_custom_form_field")
@ApiModel(value = "CustomFormField", description = "自定义表单字段配置信息表")
@AllArgsConstructor
public class CustomFormField extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 自定义表单信息表ID #sys_custom_form.id
     */
    @ApiModelProperty(value = "自定义表单信息表ID #sys_custom_form.id")
    @NotNull(message = "请填写自定义表单信息表ID #sys_custom_form.id")
    @TableField(value = "`custom_form_id`")
    private Long customFormId;

    /**
     * 对应具体业务表的字段名
     */
    @ApiModelProperty(value = "对应具体业务表的字段名")
    @NotEmpty(message = "请填写对应具体业务表的字段名")
    @Size(max = 100, message = "对应具体业务表的字段名长度不能超过100")
    @TableField(value = "`key`")
    private String key;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`")
    private String description;

    /**
     * 字段枚举类型
     */
    @ApiModelProperty(value = "字段枚举类型")
    @NotEmpty(message = "请填写字段枚举类型")
    @Size(max = 50, message = "字段枚举类型长度不能超过50")
    @TableField(value = "`type`")
    private CustomFormFieldType type;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @NotNull(message = "请填写是否启用")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 是否为内置字段
     */
    @ApiModelProperty(value = "是否为内置字段")
    @NotNull(message = "请填写是否为内置字段")
    @TableField(value = "`is_built`")
    private Boolean isBuilt;

    /**
     * 是否为基本字段
     */
    @ApiModelProperty(value = "是否为基本字段")
    @NotNull(message = "请填写是否为基本字段")
    @TableField(value = "`is_basic`")
    private Boolean isBasic;

    /**
     * 是否必填字段
     */
    @ApiModelProperty(value = "是否必填字段")
    @NotNull(message = "请填写是否必填字段")
    @TableField(value = "`is_required`")
    private Boolean isRequired;

    /**
     * 是否支持导入
     */
    @ApiModelProperty(value = "是否支持导入")
    @NotNull(message = "请填写是否支持导入")
    @TableField(value = "`is_import`")
    private Boolean isImport;

    /**
     * 是否支持导出
     */
    @ApiModelProperty(value = "是否支持导出")
    @TableField(value = "`is_export`")
    private Boolean isExport;

    /**
     * 是否支持筛选
     */
    @ApiModelProperty(value = "是否支持筛选")
    @TableField(value = "`is_search`")
    private Boolean isSearch;

    /**
     * 字段配置信息，json格式
     */
    @ApiModelProperty(value = "字段配置信息，json格式")
    @Size(max = 65535, message = "字段配置信息，json格式长度不能超过65535")
    @TableField(value = "`config`")
    private String config;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "请填写排序")
    @TableField(value = "`sort`")
    private Integer sort;

    /**
     * 非数据库字段，是否显示
     */
    @ApiModelProperty(value = "非数据库字段，是否显示")
    @TableField(exist = false)
    private Boolean isShow;

    /**
     * 非数据库字段，能否修改
     */
    @ApiModelProperty(value = "非数据库字段，能否修改")
    @TableField(exist = false)
    private Boolean isUpdate;

    @Builder
    public CustomFormField(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updateBy,
            Long customFormId, String key, String name, String description, CustomFormFieldType type,
            Boolean state, Boolean isBuilt, Boolean isBasic, Boolean isRequired, Boolean isImport, Boolean isExport,
            Boolean isSearch, String config, Integer sort, Boolean isShow, Boolean isUpdate)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updateBy;
        this.customFormId = customFormId;
        this.key = key;
        this.name = name;
        this.description = description;
        this.type = type;
        this.state = state;
        this.isBuilt = isBuilt;
        this.isBasic = isBasic;
        this.isRequired = isRequired;
        this.isImport = isImport;
        this.isExport = isExport;
        this.isSearch = isSearch;
        this.config = config;
        this.sort = sort;
        this.isShow = isShow;
        this.isUpdate = isUpdate;
    }

    public boolean valueEquals(CustomFormField that) {
        if (this == that) return true;
        return key.equals(that.key) && name.equals(that.name) && type == that.type && Objects.equals(config,
                that.config);
    }

    public int valueHashCode() {
        return Objects.hash(key, name, type, config);
    }
}
