package com.jettech.jettong.base.dto.sys.engine;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.sys.engine.EngineExtended;
import com.jettech.jettong.base.entity.sys.engine.EngineMaintainer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 引擎信息表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 引擎信息表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.engine
 * @className EngineUpdateDTO
 * @date 2021-10-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineUpdateDTO", description = "引擎信息")
public class EngineUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * jenkinsMaster引擎ID
     */
    @ApiModelProperty(value = "jenkinsMaster引擎ID")
    private Long parentId;

    /**
     * 引擎名称
     */
    @ApiModelProperty(value = "引擎名称")
    @Size(max = 100, message = "引擎名称长度不能超过100")
    private String name;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    private Boolean state;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 引擎访问url
     */
    @ApiModelProperty(value = "引擎访问url")
    @Size(max = 500, message = "引擎访问url长度不能超过500")
    private String engineUrl;

    /**
     * 引擎登录用户
     */
    @ApiModelProperty(value = "引擎登录用户")
    @Size(max = 200, message = "引擎登录用户长度不能超过200")
    private String engineLoginName;

    /**
     * 引擎密码
     */
    @ApiModelProperty(value = "引擎密码")
    @Size(max = 200, message = "引擎密码长度不能超过200")
    private String enginePassword;

    /**
     * 引擎token
     */
    @ApiModelProperty(value = "引擎token")
    @Size(max = 200, message = "引擎token长度不能超过200")
    private String engineToken;

    /**
     * 引擎所属服务器id
     */
    @ApiModelProperty(value = "引擎所属服务器id")
    private Long hostId;

    /**
     * 引擎扩展属性
     */
    @ApiModelProperty(value = "引擎扩展属性")
    private List<EngineExtended> engineExtendeds;

    /**
     * 引擎维护人
     */
    @ApiModelProperty(value = "引擎维护人")
    private List<EngineMaintainer> engineMaintainers;
}
