package com.jettech.jettong.base.dto.sys.dashboard;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.base.entity.sys.dashboard.DashboardShare;
import com.jettech.jettong.common.util.auth.enumeration.AuthAction;
import com.jettech.jettong.common.util.auth.enumeration.AuthType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 工作台分享信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台分享信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity.sys
 * @className DashboardShare
 * @date 2023-04-27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "DashboardShareDTO", description = "工作台分享信息")
public class DashboardShareDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 仪表板ID
     */
    @ApiModelProperty(value = "仪表板ID")
    @NotNull(message = "请填写仪表板ID")
    private Long dashboardId;

    /**
     * 授权的类型，
     */
    @ApiModelProperty(value = "授权的类型，")
    @NotEmpty(message = "请填写授权的类型，")
    private AuthType authType;

    /**
     * 授权的ID
     */
    @ApiModelProperty(value = "授权的ID")
    private List<Long> authIds;

    /**
     * 授权的操作，VIEW、EDIT
     */
    @ApiModelProperty(value = "授权的操作，VIEW、EDIT")
    @NotEmpty(message = "请填写授权的操作，VIEW、EDIT")
    private AuthAction authAction;

    public DashboardShareDTO() {
    }

    public DashboardShareDTO(Long dashboardId, AuthType authType, List<Long> authIds, AuthAction authAction) {
        this.dashboardId = dashboardId;
        this.authType = authType;
        this.authIds = authIds;
        this.authAction = authAction;
    }

    /**
     * 将 DashboardShareDTO 转化为 DashboardShare 集合
     */
    public static List<DashboardShare> toDashboardShareList(List<DashboardShareDTO> shareDTOList) {
        // authIds 为空时，返回空列表
        if (CollUtil.isEmpty(shareDTOList)) {
            return new ArrayList<>();
        }

        return shareDTOList.stream().map(dto -> {
                    if (CollUtil.isEmpty(dto.authIds)) {
                        return new ArrayList<DashboardShare>();
                    }
                    return dto.authIds.stream().map(authId -> {
                        return DashboardShare.builder()
                                .sourceId(dto.dashboardId)
                                .authType(dto.authType)
                                .authId(authId)
                                .authAction(dto.authAction)
                                .build();
                    }).collect(Collectors.toList());
                }
        ).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 将 DashboardShare 集合转化为 DashboardShareDTO
     */
    public static List<DashboardShareDTO> fromDashboardShare(List<DashboardShare> dashboardShares) {
        if (CollUtil.isEmpty(dashboardShares)) {
            return new ArrayList<>();
        }

        // dashboardShares 按 authType DashboardId AuthAction 分组
        Map<AuthType, Map<AuthAction, Map<Long, List<Long>>>> shareMap = dashboardShares.stream()
                .collect(Collectors.groupingBy(DashboardShare::getAuthType,
                        Collectors.groupingBy(DashboardShare::getAuthAction,
                                Collectors.groupingBy(DashboardShare::getSourceId,
                                        Collectors.mapping(DashboardShare::getAuthId, Collectors.toList())))));

        // 将 shareMap 转为 dashboardShareDTO
        List<DashboardShareDTO> dtoList = new ArrayList<>();
        shareMap.forEach((authType, authActionMap) -> {
            authActionMap.forEach((authAction, dashboardIdMap) -> {
                dashboardIdMap.forEach((dashboardId, authIds) -> {
                    DashboardShareDTO dashboardShareDTO = new DashboardShareDTO();
                    dashboardShareDTO.setDashboardId(dashboardId);
                    dashboardShareDTO.setAuthType(authType);
                    dashboardShareDTO.setAuthAction(authAction);
                    dashboardShareDTO.setAuthIds(authIds);
                    dtoList.add(dashboardShareDTO);
                });
            });
        });
        return dtoList;
    }

}
