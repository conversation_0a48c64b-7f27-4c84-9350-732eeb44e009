package com.jettech.jettong.base.dto.sys.dashboard;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 工作台组件查询参数
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工作台组件查询参数
 * @projectName jettong-tm
 * @package com.jettech.jettong.base.dto.sys.dashboard
 * @className DashboardComponentQP
 * @date 2022/4/22 16:22
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@ApiModel(value = "DashboardComponentQueryParams", description = "工作台组件查询参数")
public class DashboardComponentQueryParams {

    private String code;
    private String name;
    private String key;
    private String title;

    private String projectId;
    private String productId;
    private String planId;


}
