package com.jettech.jettong.base.dto.sys.engine;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 动态节点参数表新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 动态节点参数表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.va-base.dto
 * @className EngineJenkinsCloudExtendedSaveDTO
 * @date 2023-01-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineJenkinsCloudExtendedSaveDTO", description = "动态节点参数表")
public class EngineJenkinsCloudExtendedSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 云节点id
     */
    @ApiModelProperty(value = "云节点id")
    @NotNull(message = "请填写云节点id")
    private Long jenkinsCloudId;
    /**
     * 属性key
     */
    @ApiModelProperty(value = "属性key")
    @Size(max = 255, message = "属性key长度不能超过255")
    private String key;
    /**
     * 属性value
     */
    @ApiModelProperty(value = "属性value")
    @Size(max = 255, message = "属性value长度不能超过255")
    private String value;

}
