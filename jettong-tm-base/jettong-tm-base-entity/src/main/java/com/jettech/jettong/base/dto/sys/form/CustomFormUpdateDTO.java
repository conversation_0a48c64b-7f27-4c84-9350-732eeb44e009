package com.jettech.jettong.base.dto.sys.form;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.base.entity.sys.form.CustomFormField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 自定义表单信息表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 自定义表单信息表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.dto.sys.form
 * @className CustomFormUpdateDTO
 * @date 2022-07-18
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "CustomFormUpdateDTO", description = "自定义表单信息表")
public class CustomFormUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 表单名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    private String description;

    /**
     * 自定义表单属性信息
     */
    @ApiModelProperty(value = "自定义表单属性信息,要清空时请传空数组")
    @NotNull(message = "自定义表单字段信息不能为null")
    private List<CustomFormField> customFormFields;

}
