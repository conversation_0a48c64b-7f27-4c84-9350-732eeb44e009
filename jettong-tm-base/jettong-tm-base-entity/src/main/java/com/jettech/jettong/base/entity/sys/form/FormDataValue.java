package com.jettech.jettong.base.entity.sys.form;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据表字段的值实体类
 * <AUTHOR>
 * @version 1.0
 * @description 数据表字段的值实体类
 * @projectName jettong
 * @package com.jettech.jettong.base.base.entity
 * @className FormDataValue
 * @date 2022-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_form_data_value")
@ApiModel(value = "FormDataValue", description = "数据表字段的值")
@AllArgsConstructor
public class FormDataValue extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 数据表ID，外键
     */
    @ApiModelProperty(value = "数据表ID，外键")
    @NotNull(message = "请填写数据表ID，外键")
    @TableField(value = "`data_id`")
    private Long dataId;

    /**
     * 数据表字段ID，外键
     */
    @ApiModelProperty(value = "数据表字段ID，外键")
    @NotNull(message = "请填写数据表字段ID，外键")
    @TableField(value = "`field_id`")
    private Long fieldId;

    /**
     * 数据表字段对应的值
     */
    @ApiModelProperty(value = "数据表字段对应的值")
    @NotEmpty(message = "请填写数据表字段对应的值")
    @Size(max = 200, message = "数据表字段对应的值长度不能超过200")
    @TableField(value = "`value`", condition = LIKE)
    private String value;


    /**
     * 每条记录的ID，用来区分每条数据
     */
    @ApiModelProperty(value = "每条记录的ID，用来区分每条数据")
    @NotNull(message = "请填写 记录的ID")
    @TableField(value = "`row_id`")
    private Long rowId;



    @Builder
    public FormDataValue(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy, 
                    Long dataId, Long fieldId, String value, Long rowId)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.dataId = dataId;
        this.fieldId = fieldId;
        this.value = value;
        this.rowId = rowId;
    }

}
