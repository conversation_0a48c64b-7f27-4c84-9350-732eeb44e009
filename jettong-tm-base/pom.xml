<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
        <groupId>com.jettech.jettong</groupId>
        <artifactId>jettong-tm</artifactId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
	</parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>jettong-tm-base</artifactId>
    <name>${project.artifactId}</name>
    <description>va基础服务</description>
    <packaging>pom</packaging>

    <modules>
        <module>jettong-tm-base-api</module>
        <module>jettong-tm-base-entity</module>
        <module>jettong-tm-base-biz</module>
        <module>jettong-tm-base-job</module>
        <module>jettong-tm-base-controller</module>
        <module>jettong-tm-base-server</module>
    </modules>
</project>
