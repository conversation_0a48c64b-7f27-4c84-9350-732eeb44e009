package com.jettech.jettong.cmdb.host.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.cmdb.host.enumeration.HostCreateType;
import com.jettech.jettong.cmdb.host.enumeration.HostStatus;
import com.jettech.jettong.cmdb.host.enumeration.HostType;
import com.jettech.jettong.common.enumeration.OperatingSystemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;


/**
 * 服务器信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.entity
 * @className Host
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_host")
@ApiModel(value = "Host", description = "服务器信息")
@AllArgsConstructor
public class Host extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务器ip
     */
    @ApiModelProperty(value = "服务器ip")
    @Size(max = 20, message = "服务器ip长度不能超过20")
    @TableField(value = "`ip`")
    private String ip;

    /**
     * 服务器名称
     */
    @ApiModelProperty(value = "主机名称")
    @Size(max = 200, message = "主机名称长度不能超过200")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`")
    private String description;

    /**
     * salt minion key
     */
    @ApiModelProperty(value = "salt minion key")
    @Size(max = 500, message = "salt minion key长度不能超过500")
    @TableField(value = "`minion_key`")
    private String minionKey;

    /**
     * 操作系统
     */
    @ApiModelProperty(value = "操作系统")
    @Size(max = 200, message = "操作系统不能超过200")
    @TableField(value = "`operating_system`")
    private String operatingSystem;

    /**
     * 操作系统版本
     */
    @ApiModelProperty(value = "操作系统版本")
    @Size(max = 200, message = "操作系统版本长度不能超过200")
    @TableField(value = "`operating_system_version`")
    private String operatingSystemVersion;

    /**
     * 操作系统内核
     */
    @ApiModelProperty(value = "操作系统内核")
    @TableField(value = "`operating_system_kernel`")
    private OperatingSystemType operatingSystemKernel;

    /**
     * 系统位数 32/64
     */
    @ApiModelProperty(value = "系统位数 32/64")
    @TableField(value = "`places`")
    private Integer places;

    /**
     * cpu核心数
     */
    @ApiModelProperty(value = "cpu核心数")
    @TableField(value = "`cpu_num`")
    private Integer cpuNum;

    /**
     * cpu频率
     */
    @ApiModelProperty(value = "cpu频率")
    @TableField(value = "`cpu_model`")
    private String cpuModel;

    /**
     * 硬盘容量 Mb
     */
    @ApiModelProperty(value = "硬盘容量 Mb")
    @TableField(value = "`hd_size`")
    private Long hdSize;

    /**
     * 内存大小 Mb
     */
    @ApiModelProperty(value = "内存大小 Mb")
    @TableField(value = "`mem_size`")
    private Long memSize;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 纳管状态
     */
    @ApiModelProperty(value = "纳管状态")
    @TableField(value = "`status`")
    private HostStatus status;

    /**
     * 服务器类型
     */
    @ApiModelProperty(value = "服务器类型")
    @TableField(value = "`type`")
    private HostType type;

    /**
     * 创建方式
     */
    @ApiModelProperty(value = "创建方式")
    @TableField(value = "`create_type`")
    private HostCreateType createType;

    /**
     * 所属salt引擎id
     */
    @ApiModelProperty(value = "所属salt引擎")
    @TableField(value = "`salt_engine_id`")
    private Long saltEngineId;

    /**
     * 巡检策略
     */
    @ApiModelProperty(value = "巡检策略")
    @TableField(value = "`patrol_strategy`")
    private Integer patrolStrategy;

    /**
     * 创建机构
     */
    @ApiModelProperty(value = "创建机构")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 服务器扩展属性
     */
    @ApiModelProperty(value = "服务器扩展信息")
    @TableField(exist = false)
    private List<HostExtend> hostExtends;

    @Builder
    public Host(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String ip, String name, String description, String minionKey, String operatingSystem,
            String operatingSystemVersion,
            OperatingSystemType operatingSystemKernel, Integer places, Integer cpuNum, String cpuModel, Long hdSize,
            Long memSize, Boolean state,
            HostStatus status, HostType type, HostCreateType createType,
            Integer patrolStrategy, Long orgId, List<HostExtend> hostExtends)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.ip = ip;
        this.name = name;
        this.description = description;
        this.minionKey = minionKey;
        this.operatingSystem = operatingSystem;
        this.operatingSystemVersion = operatingSystemVersion;
        this.operatingSystemKernel = operatingSystemKernel;
        this.places = places;
        this.cpuNum = cpuNum;
        this.cpuModel = cpuModel;
        this.hdSize = hdSize;
        this.memSize = memSize;
        this.state = state;
        this.status = status;
        this.type = type;
        this.createType = createType;
        this.patrolStrategy = patrolStrategy;
        this.orgId = orgId;
        this.hostExtends = hostExtends;
    }

}
