package com.jettech.jettong.cmdb.application.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 服务应用tag配置信息实体类
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用tag配置信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.entity
 * @className ApplicationTag
 * @date 2022-08-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_tag")
@ApiModel(value = "ApplicationTag", description = "服务应用tag配置信息")
@AllArgsConstructor
public class ApplicationTag extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    private Long applicationId;

    /**
     * tag_key
     */
    @ApiModelProperty(value = "tag_key")
    @Size(max = 50, message = "tag_key长度不能超过50")
    @TableField(value = "`tag_key`", condition = LIKE)
    private String tagKey;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`", condition = LIKE)
    private String description;

    @ApiModelProperty(value = "最后修改时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最后修改人ID")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long updatedBy;

    @Builder
    public ApplicationTag(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
                    Long applicationId, String tagKey, String description)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.applicationId = applicationId;
        this.tagKey = tagKey;
        this.description = description;
    }

}
