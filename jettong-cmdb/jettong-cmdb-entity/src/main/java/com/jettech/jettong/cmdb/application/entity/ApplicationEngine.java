package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.sys.engine.Engine;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.ENGINE_ID_FEIGN_CLASS;


/**
 * 服务应用引擎信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用所用的引擎信息表实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationEngine
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_engine")
@ApiModel(value = "ApplicationEngine", description = "服务应用引擎信息")
@AllArgsConstructor
public class ApplicationEngine implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "引擎id")
    @TableField(value = "`engine_id`")
    @Echo(api = ENGINE_ID_FEIGN_CLASS, beanClass = Engine.class)
    @Excel(name = "引擎id")
    private Long engineId;

    /**
     * 引擎类型字典key
     */
    @ApiModelProperty(value = "引擎类型字典key")
    @Size(max = 50, message = "引擎类型字典key长度不能超过50")
    @TableField(value = "`engine_classify_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENGINE_CLASSIFY)
    @Excel(name = "引擎类型字典key")
    private String engineClassifyKey;

    /**
     * 引擎类型字典key
     */
    @ApiModelProperty(value = "引擎类型字典key")
    @Size(max = 50, message = "引擎类型字典key长度不能超过50")
    @TableField(value = "`engine_instance_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENGINE_INSTANCE)
    @Excel(name = "引擎类型字典key")
    private String engineInstanceKey;

    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    @Size(max = 50, message = "环境字典key长度不能超过50")
    @TableField(value = "`env_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENVIRONMENT)
    @Excel(name = "环境字典key")
    private String envKey;


    @Builder
    public ApplicationEngine(
            Long applicationId, Long engineId, String engineClassifyKey, String engineInstanceKey, String envKey)
    {
        this.applicationId = applicationId;
        this.engineId = engineId;
        this.engineClassifyKey = engineClassifyKey;
        this.engineInstanceKey = engineInstanceKey;
        this.envKey = envKey;
    }

}
