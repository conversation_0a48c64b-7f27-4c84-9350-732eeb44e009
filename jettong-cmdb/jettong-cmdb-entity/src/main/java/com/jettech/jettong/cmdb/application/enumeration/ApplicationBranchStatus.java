package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 分支状态枚举。UNDER_DEVELOPMENT:开发中;DISCARD:废弃
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分支状态枚举。UNDER_DEVELOPMENT:开发中;DISCARD:废弃
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationBranchStatus
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationBranchStatus",
        description = "分支状态枚举。UNDER_DEVELOPMENT:开发中;DISCARD:废弃")
public enum ApplicationBranchStatus implements BaseEnum
{

    /**
     * UNDER_DEVELOPMENT="开发中"
     */
    UNDER_DEVELOPMENT("开发中"),
    /**
     * DISCARD="废弃"
     */
    DISCARD("废弃"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationBranchStatus match(String val, ApplicationBranchStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationBranchStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationBranchStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "UNDER_DEVELOPMENT,DISCARD", example = "UNDER_DEVELOPMENT")
    public String getCode()
    {
        return this.name();
    }

}
