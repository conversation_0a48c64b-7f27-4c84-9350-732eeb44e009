package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationProgramType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationStatus;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationType;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.*;


/**
 * 服务应用信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className Application
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application")
@ApiModel(value = "Application", description = "服务应用信息")
@AllArgsConstructor
public class Application extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 50, message = "编号长度不能超过50")
    @TableField(value = "`code`", condition = LIKE)
    @Excel(name = "编号")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 200, message = "名称长度不能超过200")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 应用状态
     */
    @ApiModelProperty(value = "应用状态")
    @TableField(value = "`status`")
    @Excel(name = "应用状态",
            replace = {"在建_UNDER_CONSTRUCTION", "已投产_PRODUCTION", "已废弃_OBSOLETE", "_null"})
    private ApplicationStatus status;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    @TableField(value = "`responsible`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @Excel(name = "负责人id")
    private Long responsible;

    /**
     * 应用类型
     */
    @ApiModelProperty(value = "应用类型")
    @TableField(value = "`type`")
    @Excel(name = "应用类型",
            replace = {"构建_CI", "部署_CD", "构建+部署_CI_CD", "_null"})
    private ApplicationType type;

    /**
     * 程序类型
     */
    @ApiModelProperty(value = "程序类型")
    @TableField(value = "`program_type`")
    @Excel(name = "程序类型",
            replace = {"Web端_WEB_APP", "移动端_MOBILE_APP", "其它_OTHER", "_null"})
    private ApplicationProgramType programType;

    /**
     * 仓库地址
     */
    @ApiModelProperty(value = "仓库地址")
    @TableField(value = "`code_rep_path`")
    @Excel(name = "仓库地址")
    private String codeRepPath;

    /**
     * 分支策略字典
     */
    @ApiModelProperty(value = "分支策略字典key")
    @Size(max = 50, message = "分支策略字典长度不能超过50")
    @TableField(value = "`branching_strategy_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.BRANCHING_STRATEGY)
    @Excel(name = "分支策略字典key")
    private String branchingStrategyKey;

    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "创建机构id")
    private Long orgId;

    /**
     * 环境信息
     */
    @ApiModelProperty(value = "环境信息")
    @TableField(exist = false)
    @Excel(name = "环境信息")
    private List<ApplicationEnv> applicationEnvs;

    /**
     * 引擎信息
     */
    @ApiModelProperty(value = "引擎信息")
    @TableField(exist = false)
    private List<ApplicationEngine> applicationEngines;

    /**
     * 是否关联产品
     */
    @ApiModelProperty(value = "是否关联产品")
    @TableField(exist = false)
    private boolean isproduct;

    @Builder
    public Application(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String code, String name, String description, ApplicationStatus status, Long responsible,
            ApplicationType type, ApplicationProgramType programType, String codeRepPath, String branchingStrategyKey,
            Long orgId,
            List<ApplicationEnv> applicationEnvs, List<ApplicationEngine> applicationEngines, boolean isproduct)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.description = description;
        this.status = status;
        this.responsible = responsible;
        this.type = type;
        this.programType = programType;
        this.codeRepPath = codeRepPath;
        this.branchingStrategyKey = branchingStrategyKey;
        this.orgId = orgId;
        this.applicationEnvs = applicationEnvs;
        this.applicationEngines = applicationEngines;
        this.isproduct = isproduct;
    }

}
