package com.jettech.jettong.cmdb.host.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 批量修改巡检策略实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 批量修改巡检策略实体类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.host.dto
 * @className EngineUpdatePatrolStrategyDTO
 * @date 2021/11/1 12:58
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "EngineUpdatePatrolStrategyDTO", description = "批量修改巡检策略实体")
public class HostUpdatePatrolStrategyDTO implements Serializable
{
    @ApiModelProperty(value = "巡检间隔分钟数")
    @NotNull(message = "请输入巡检间隔分钟数")
    private Integer patrolStrategy;

    @ApiModelProperty(value = "服务器id集合")
    @NotEmpty(message = "请选择要设置巡检策略的服务器")
    private List<Long> hostIds;
}
