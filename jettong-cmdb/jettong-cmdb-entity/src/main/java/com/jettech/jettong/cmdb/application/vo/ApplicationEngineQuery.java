package com.jettech.jettong.cmdb.application.vo;

import com.jettech.jettong.common.enumeration.EngineClassify;
import com.jettech.jettong.common.enumeration.EngineInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 服务应用引擎信息查询条件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用引擎信息查询条件
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.application.vo
 * @className ApplicationEngineQuery
 * @date 2021/11/2 9:29
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationEngineQuery", description = "服务应用引擎信息查询条件")
public class ApplicationEngineQuery implements Serializable
{
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "applicationId")
    private Long applicationId;

    /**
     * 引擎分类
     */
    @ApiModelProperty(value = "引擎分类")
    private EngineClassify engineClassify;

    /**
     * 引擎类型
     */
    @ApiModelProperty(value = "引擎类型")
    private EngineInstance engineInstance;

    /**
     * 环境key，支持多个
     */
    @ApiModelProperty(value = "环境key")
    private List<String> envKeys;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;
}
