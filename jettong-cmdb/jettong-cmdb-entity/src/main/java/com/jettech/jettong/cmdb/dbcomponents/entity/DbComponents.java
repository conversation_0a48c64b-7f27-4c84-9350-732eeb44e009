package com.jettech.jettong.cmdb.dbcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.application.entity.ApplicationDb;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;


/**
 * 数据库组件信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.entity
 * @className DbComponents
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_db_components")
@ApiModel(value = "DbComponents", description = "数据库组件信息")
@AllArgsConstructor
public class DbComponents extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 100, message = "名称长度不能超过100")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 数据库组件类型字典key
     */
    @ApiModelProperty(value = "数据库组件类型字典key")
    @Size(max = 50, message = "数据库组件类型字典key长度不能超过50")
    @TableField(value = "`db_components_type_key`", condition = EQUAL)
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.DB_COMPONENTS_TYPE)
    @Excel(name = "数据库组件类型字典key")
    private String dbComponentsTypeKey;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    @TableField(value = "`host_id`")
    @Excel(name = "服务器id")
    private Long hostId;

    /**
     * 冗余字段，服务器ip
     */
    @ApiModelProperty(value = "冗余字段，服务器ip")
    @Size(max = 20, message = "冗余字段，服务器ip长度不能超过20")
    @TableField(value = "`server_ip`", condition = LIKE)
    @Excel(name = "冗余字段，服务器ip")
    private String serverIp;

    /**
     * 服务端口
     */
    @ApiModelProperty(value = "服务端口")
    @TableField(value = "`server_port`")
    @Excel(name = "服务端口")
    private Integer serverPort;

    /**
     * 安装目录
     */
    @ApiModelProperty(value = "安装目录")
    @Size(max = 2000, message = "安装目录长度不能超过2000")
    @TableField(value = "`installation_path`", condition = LIKE)
    @Excel(name = "安装目录")
    private String installationPath;

    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "`org_id`")
    @Excel(name = "创建机构id")
    private Long orgId;

    /**
     * 数据库组件扩展信息
     */
    @ApiModelProperty(value = "数据库组件扩展信息")
    @TableField(exist = false)
    private List<DbComponentsExtend> dbComponentsExtends;

    /**
     * 数据库组件用户配置信息
     */
    @ApiModelProperty(value = "数据库组件用户配置信息")
    @TableField(exist = false)
    private List<DbComponentsUser> dbComponentsUsers;

    /**
     * 数据库组件实例配置信息
     */
    @ApiModelProperty(value = "数据库组件实例配置信息")
    @TableField(exist = false)
    private List<DbComponentsInstance> dbComponentsInstances;

    /**
     * 数据库组件实例用户关系配置信息
     */
    @ApiModelProperty(value = "数据库组件实例用户关系配置信息")
    @TableField(exist = false)
    private List<DbComponentsUserInstance> dbComponentsUserInstances;

    /**
     * 服务应用数据库配置信息
     */
    @ApiModelProperty(value = "服务应用数据库配置信息")
    @TableField(exist = false)
    private List<ApplicationDb> applicationDbs;

    @Builder
    public DbComponents(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String name, String description, String dbComponentsTypeKey, Long hostId, String serverIp,
            Integer serverPort, String installationPath, Long orgId, List<DbComponentsExtend> dbComponentsExtends,
            List<DbComponentsUser> dbComponentsUsers, List<DbComponentsInstance> dbComponentsInstances,
            List<DbComponentsUserInstance> dbComponentsUserInstances, List<ApplicationDb> applicationDbs)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.description = description;
        this.dbComponentsTypeKey = dbComponentsTypeKey;
        this.hostId = hostId;
        this.serverIp = serverIp;
        this.serverPort = serverPort;
        this.installationPath = installationPath;
        this.orgId = orgId;
        this.dbComponentsExtends = dbComponentsExtends;
        this.dbComponentsUsers = dbComponentsUsers;
        this.dbComponentsInstances = dbComponentsInstances;
        this.dbComponentsUserInstances = dbComponentsUserInstances;
        this.applicationDbs = applicationDbs;
    }

}
