package com.jettech.jettong.cmdb.host.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 纳管状态枚举。UNMANAGED:未纳管;MANAGED:已纳管;ASSIGNED:已分配
 *
 * <AUTHOR>
 * @version 1.0
 * @description 纳管状态枚举。UNMANAGED:未纳管;MANAGED:已纳管;ASSIGNED:已分配
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.enumeration
 * @className HostStatus
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HostStatus", description = "纳管状态枚举。UNMANAGED:未纳管;MANAGED:已纳管;UNASSIGNED:未分配;ASSIGNED:已分配")
public enum HostStatus implements BaseEnum
{

    /**
     * UNMANAGED="未纳管"
     */
    UNMANAGED("未纳管"),
    /**
     * UNMANAGED="已纳管"
     */
    MANAGED("已纳管"),
    /**
     * UNASSIGNED="未分配"
     */
    UNASSIGNED("未分配"),
    /**
     * ASSIGNED="已分配"
     */
    ASSIGNED("已分配"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static HostStatus match(String val, HostStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static HostStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(HostStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "纳管状态编码", allowableValues = "UNMANAGED,MANAGED,UNASSIGNED,ASSIGNED", example = "UNMANAGED")
    public String getCode()
    {
        return this.name();
    }

}
