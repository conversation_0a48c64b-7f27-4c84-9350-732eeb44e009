package com.jettech.jettong.cmdb.dbcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 数据库组件信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dtoPageQuery
 * @className DbComponents
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DbComponentsPageQuery", description = "数据库组件信息")
public class DbComponentsPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 数据库组件类型字典key
     */
    @ApiModelProperty(value = "数据库组件类型字典key")
    private String dbComponentsTypeKey;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    private Long hostId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

    /**
     * 冗余字段，服务器ip
     */
    @ApiModelProperty(value = "冗余字段，服务器ip")
    private String serverIp;

    /**
     * 服务端口
     */
    @ApiModelProperty(value = "服务端口")
    private Integer serverPort;

}
