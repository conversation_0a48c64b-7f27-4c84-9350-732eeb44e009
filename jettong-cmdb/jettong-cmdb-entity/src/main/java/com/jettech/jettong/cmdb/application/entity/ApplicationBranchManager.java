package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 分支扫描结果表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分支扫描结果表实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.entity
 * @className ApplicationBranchManager
 * @date 2021-12-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_branch_manager")
@ApiModel(value = "ApplicationBranchManager", description = "分支扫描结果表")
@AllArgsConstructor
public class ApplicationBranchManager extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用ID
     */
    @ApiModelProperty(value = "服务应用ID")
    @NotNull(message = "请填写服务应用ID")
    @TableField(value = "application_id")
    @Excel(name = "服务应用ID")
    private Long applicationId;

    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    @Size(max = 50, message = "分支字典key长度不能超过50")
    @TableField(value = "branch_key", condition = LIKE)
    @Excel(name = "分支字典key")
    private String branchKey;

    /**
     * 流水线ID
     */
    @ApiModelProperty(value = "流水线ID")
    @NotNull(message = "请填写流水线ID")
    @TableField(value = "pipeline_id")
    @Excel(name = "流水线ID")
    private Long pipelineId;

    /**
     * 构建ID
     */
    @ApiModelProperty(value = "构建ID")
    @Size(max = 32, message = "构建ID长度不能超过32")
    @TableField(value = "ci_build_id", condition = LIKE)
    @Excel(name = "构建ID")
    private String ciBuildId;

    /**
     * 增量行覆盖率
     */
    @ApiModelProperty(value = "增量行覆盖率")
    @Size(max = 255, message = "增量行覆盖率长度不能超过255")
    @TableField(value = "increte_line_cover_rate", condition = LIKE)
    @Excel(name = "增量行覆盖率")
    private String increteLineCoverRate;

    /**
     * 全量行覆盖率
     */
    @ApiModelProperty(value = "全量行覆盖率")
    @Size(max = 255, message = "全量行覆盖率长度不能超过255")
    @TableField(value = "all_line_cover_rate", condition = LIKE)
    @Excel(name = "全量行覆盖率")
    private String allLineCoverRate;

    /**
     * bug
     */
    @ApiModelProperty(value = "bug")
    @Size(max = 255, message = "bug长度不能超过255")
    @TableField(value = "bugs", condition = LIKE)
    @Excel(name = "bug")
    private String bugs;

    /**
     * 异味
     */
    @ApiModelProperty(value = "异味")
    @Size(max = 255, message = "异味长度不能超过255")
    @TableField(value = "code_smells", condition = LIKE)
    @Excel(name = "异味")
    private String codeSmells;

    /**
     * 漏洞
     */
    @ApiModelProperty(value = "漏洞")
    @Size(max = 255, message = "漏洞长度不能超过255")
    @TableField(value = "vulnerabilities", condition = LIKE)
    @Excel(name = "漏洞")
    private String vulnerabilities;

    /**
     * 代码覆盖率
     */
    @ApiModelProperty(value = "代码覆盖率")
    @Size(max = 255, message = "代码覆盖率长度不能超过255")
    @TableField(value = "debt", condition = LIKE)
    @Excel(name = "代码覆盖率")
    private String debt;

    /**
     * 单测是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "单测是否通过 1-通过；2不通过")
    @TableField(value = "is_unit_passed")
    @Excel(name = "单测是否通过 1-通过；2不通过")
    private Integer isUnitPassed;

    /**
     * 静态扫描是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "静态扫描是否通过 1-通过；2不通过")
    @TableField(value = "is_static_scan_passed")
    @Excel(name = "静态扫描是否通过 1-通过；2不通过")
    private Integer isStaticScanPassed;

    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @Size(max = 255, message = "执行人长度不能超过255")
    @TableField(value = "executor", condition = LIKE)
    @Excel(name = "执行人")
    private String executor;

    /**
     * 单测通过率
     */
    @ApiModelProperty(value = "单测通过率")
    @Size(max = 255, message = "单测通过率长度不能超过255")
    @TableField(value = "unit_pass_rate", condition = LIKE)
    @Excel(name = "单测通过率")
    private String unitPassRate;

    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "org_id")
    @Excel(name = "创建机构id")
    private Long orgId;


    @Builder
    public ApplicationBranchManager(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime,
            Long updatedBy,
            Long applicationId, String branchKey, Long pipelineId, String ciBuildId, String increteLineCoverRate,
            String allLineCoverRate, String bugs, String codeSmells, String vulnerabilities, String debt,
            Integer isUnitPassed,
            Integer isStaticScanPassed, String executor, String unitPassRate, Long orgId)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.applicationId = applicationId;
        this.branchKey = branchKey;
        this.pipelineId = pipelineId;
        this.ciBuildId = ciBuildId;
        this.increteLineCoverRate = increteLineCoverRate;
        this.allLineCoverRate = allLineCoverRate;
        this.bugs = bugs;
        this.codeSmells = codeSmells;
        this.vulnerabilities = vulnerabilities;
        this.debt = debt;
        this.isUnitPassed = isUnitPassed;
        this.isStaticScanPassed = isStaticScanPassed;
        this.executor = executor;
        this.unitPassRate = unitPassRate;
        this.orgId = orgId;
    }

}
