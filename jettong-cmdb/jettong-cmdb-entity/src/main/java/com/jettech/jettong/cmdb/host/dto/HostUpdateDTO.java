package com.jettech.jettong.cmdb.host.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.cmdb.host.entity.HostExtend;
import com.jettech.jettong.cmdb.host.enumeration.HostType;
import com.jettech.jettong.common.enumeration.OperatingSystemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 服务器信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.dto
 * @className HostUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "HostUpdateDTO", description = "服务器信息")
public class HostUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 服务器ip
     */
    @ApiModelProperty(value = "服务器ip")
    @NotNull(message = "请填写服务器ip")
    @Size(max = 20, message = "服务器ip长度不能超过20")
    private String ip;

    /**
     * 服务器主机名
     */
    @ApiModelProperty(value = "服务器主机名")
    @Size(max = 200, message = "服务器主机名长度不能超过200")
    private String name;
    
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 操作系统
     */
    @ApiModelProperty(value = "操作系统")
    @Size(max = 200, message = "操作系统内核长度不能超过200")
    private String operatingSystem;

    /**
     * 操作系统版本
     */
    @ApiModelProperty(value = "操作系统版本")
    @Size(max = 200, message = "操作系统版本长度不能超过200")
    private String operatingSystemVersion;

    /**
     * 操作系统内核
     */
    @ApiModelProperty(value = "操作系统内核")
    private OperatingSystemType operatingSystemKernel;

    /**
     * 系统位数 32/64
     */
    @ApiModelProperty(value = "系统位数 32/64")
    private Integer places;

    /**
     * cpu核心数
     */
    @ApiModelProperty(value = "cpu核心数")
    private Integer cpuNum;

    /**
     * cpu频率
     */
    @ApiModelProperty(value = "cpu频率")
    private String cpuModel;

    /**
     * 硬盘容量 Mb
     */
    @ApiModelProperty(value = "硬盘容量 Mb")
    private Long hdSize;

    /**
     * 内存大小 Mb
     */
    @ApiModelProperty(value = "内存大小 Mb")
    private Long memSize;

    /**
     * 服务器类型
     */
    @ApiModelProperty(value = "服务器类型")
    private HostType type;

    /**
     * 巡检策略
     */
    @ApiModelProperty(value = "巡检策略")
    private Integer patrolStrategy;

    /**
     * 服务器扩展信息
     */
    @ApiModelProperty(value = "服务器扩展信息")
    private List<HostExtend> hostExtends;

}
