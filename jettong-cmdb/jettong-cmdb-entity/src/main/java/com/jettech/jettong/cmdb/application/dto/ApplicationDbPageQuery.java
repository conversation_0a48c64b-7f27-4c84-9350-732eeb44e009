package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 服务应用数据库配置信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用数据库配置信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationDb
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationDbPageQuery", description = "服务应用数据库配置信息")
public class ApplicationDbPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "请填写服务应用id")
    private Long applicationId;

}
