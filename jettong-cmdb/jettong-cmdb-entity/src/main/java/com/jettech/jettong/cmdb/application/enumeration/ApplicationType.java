package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 服务应用信息实体注释中生成的类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息实体注释中生成的类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationType", description = "应用类型 #ApplicationType{CI:构建;CD:部署;CI_CD:构建+部署-枚举")
public enum ApplicationType implements BaseEnum
{

    /**
     * CI="构建"
     */
    CI("构建"),
    /**
     * CD="部署"
     */
    CD("部署"),
    /**
     * CI_CD="构建+部署"
     */
    CI_CD("构建+部署"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationType match(String val, ApplicationType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "CI,CD,CI_CD", example = "CI")
    public String getCode()
    {
        return this.name();
    }

}
