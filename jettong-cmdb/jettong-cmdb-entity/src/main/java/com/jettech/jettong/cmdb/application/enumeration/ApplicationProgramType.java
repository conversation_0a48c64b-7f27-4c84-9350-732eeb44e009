package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 程序类型枚举.WEB_APP:Web端;MOBILE_APP:移动端;OTHER:其它
 *
 * <AUTHOR>
 * @version 1.0
 * @description 程序类型枚举.WEB_APP:Web端;MOBILE_APP:移动端;OTHER:其它
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationProgramType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationProgramType",
        description = "程序类型枚举.WEB_APP:Web端;MOBILE_APP:移动端;OTHER:其它")
public enum ApplicationProgramType implements BaseEnum
{

    /**
     * WEB_APP="Web端"
     */
    WEB_APP("Web端"),
    /**
     * MOBILE_APP="移动端"
     */
    MOBILE_APP("移动端"),
    /**
     * OTHER="其它"
     */
    OTHER("其它"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationProgramType match(String val, ApplicationProgramType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationProgramType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationProgramType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "WEB_APP,MOBILE_APP,OTHER", example = "WEB_APP")
    public String getCode()
    {
        return this.name();
    }

}
