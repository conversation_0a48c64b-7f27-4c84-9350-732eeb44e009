package com.jettech.jettong.cmdb.host.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.host.dto
 * @className HostSyncDTO
 * @date 2021/10/26 10:52
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "HostSyncDTO", description = "同步服务器对象")
public class HostSyncDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "服务器id")
    private List<Long> hostIds;

}
