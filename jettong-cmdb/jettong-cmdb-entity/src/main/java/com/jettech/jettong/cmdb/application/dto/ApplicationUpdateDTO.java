package com.jettech.jettong.cmdb.application.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.cmdb.application.entity.ApplicationEnv;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationProgramType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationStatus;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 服务应用信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationUpdateDTO", description = "服务应用信息")
public class ApplicationUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 200, message = "名称长度不能超过200")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 应用状态
     */
    @ApiModelProperty(value = "应用状态")
    private ApplicationStatus status;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private Long responsible;

    /**
     * 应用类型
     */
    @ApiModelProperty(value = "应用类型")
    private ApplicationType type;

    /**
     * 程序类型
     */
    @ApiModelProperty(value = "程序类型")
    private ApplicationProgramType programType;

    /**
     * 环境信息
     */
    @ApiModelProperty(value = "环境信息")
    @NotEmpty(message = "请选择环境信息")
    @NotNull(message = "请选择环境信息")
    private List<ApplicationEnv> applicationEnvs;

}
