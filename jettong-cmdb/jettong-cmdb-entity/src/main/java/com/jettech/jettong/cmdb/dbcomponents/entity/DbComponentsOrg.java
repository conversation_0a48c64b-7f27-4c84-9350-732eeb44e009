package com.jettech.jettong.cmdb.dbcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_CLASS;


/**
 * 数据库组件授权信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件授权信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.entity
 * @className DbComponentsOrg
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_db_components_org")
@ApiModel(value = "DbComponentsOrg", description = "数据库组件授权信息")
@AllArgsConstructor
public class DbComponentsOrg implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    @NotNull(message = "请填写数据库组件id")
    @TableId(value = "`db_components_id`", type = IdType.INPUT)
    @Excel(name = "数据库组件id")
    private Long dbComponentsId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @NotNull(message = "请填写机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_CLASS, beanClass = Org.class)
    @Excel(name = "机构id")
    private Long orgId;

    /**
     * 是否创建机构
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;

    @Builder
    public DbComponentsOrg(
            Long dbComponentsId, Long orgId, Boolean isCreate)
    {
        this.dbComponentsId = dbComponentsId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }

}
