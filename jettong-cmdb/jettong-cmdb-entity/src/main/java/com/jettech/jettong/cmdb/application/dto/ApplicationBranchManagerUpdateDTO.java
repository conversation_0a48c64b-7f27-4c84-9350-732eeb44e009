package com.jettech.jettong.cmdb.application.dto;

import com.jettech.basic.base.entity.SuperEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 分支扫描结果表修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分支扫描结果表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dto
 * @className ApplicationBranchManagerUpdateDTO
 * @date 2021-12-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBranchManagerUpdateDTO", description = "分支扫描结果表")
public class ApplicationBranchManagerUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 服务应用ID
     */
    @ApiModelProperty(value = "服务应用ID")
    @NotNull(message = "请填写服务应用ID")
    private Long applicationId;
    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    @Size(max = 50, message = "分支字典key长度不能超过50")
    private String branchKey;
    /**
     * 流水线ID
     */
    @ApiModelProperty(value = "流水线ID")
    @NotNull(message = "请填写流水线ID")
    private Long pipelineId;
    /**
     * 构建ID
     */
    @ApiModelProperty(value = "构建ID")
    @Size(max = 32, message = "构建ID长度不能超过32")
    private String ciBuildId;
    /**
     * 增量行覆盖率
     */
    @ApiModelProperty(value = "增量行覆盖率")
    @Size(max = 255, message = "增量行覆盖率长度不能超过255")
    private String increteLineCoverRate;
    /**
     * 全量行覆盖率
     */
    @ApiModelProperty(value = "全量行覆盖率")
    @Size(max = 255, message = "全量行覆盖率长度不能超过255")
    private String allLineCoverRate;
    /**
     * bug
     */
    @ApiModelProperty(value = "bug")
    @Size(max = 255, message = "bug长度不能超过255")
    private String bugs;
    /**
     * 异味
     */
    @ApiModelProperty(value = "异味")
    @Size(max = 255, message = "异味长度不能超过255")
    private String codeSmells;
    /**
     * 漏洞
     */
    @ApiModelProperty(value = "漏洞")
    @Size(max = 255, message = "漏洞长度不能超过255")
    private String vulnerabilities;
    /**
     * 代码覆盖率
     */
    @ApiModelProperty(value = "代码覆盖率")
    @Size(max = 255, message = "代码覆盖率长度不能超过255")
    private String debt;
    /**
     * 单测是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "单测是否通过 1-通过；2不通过")
    private Integer isUnitPassed;
    /**
     * 静态扫描是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "静态扫描是否通过 1-通过；2不通过")
    private Integer isStaticScanPassed;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    @Size(max = 255, message = "执行人长度不能超过255")
    private String executor;
    /**
     * 单测通过率
     */
    @ApiModelProperty(value = "单测通过率")
    @Size(max = 255, message = "单测通过率长度不能超过255")
    private String unitPassRate;
    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    private Long orgId;
}
