package com.jettech.jettong.cmdb.application.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationBranchStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 服务应用分支信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用分支信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationBranchSaveDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBranchSaveDTO", description = "服务应用分支新增信息")
public class ApplicationBranchSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "id不能为空")
    private Long applicationId;

    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    @Size(max = 50, message = "分支字典key长度不能超过50")
    private String branchKey;

    /**
     * 自定义分支名称
     */
    @ApiModelProperty(value = "分支名称")
    @Size(max = 50, message = "分支名称长度不能超过50")
    private String name;

    /**
     * 自定义分支code
     */
    @ApiModelProperty(value = "分支code")
    @Size(max = 50, message = "分支code长度不能超过50")
    @TableField(value = "`code`")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 分支状态
     */
    @ApiModelProperty(value = "分支状态")
    @TableField(value = "`status`")
    private ApplicationBranchStatus status;

}
