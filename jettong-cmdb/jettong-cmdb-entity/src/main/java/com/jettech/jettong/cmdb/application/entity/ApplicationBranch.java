package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationBranchStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 服务应用分支配置信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用分支配置信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationBranch
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_branch")
@ApiModel(value = "ApplicationBranch", description = "服务应用分支配置信息")
@AllArgsConstructor
public class ApplicationBranch extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    @NotNull(message = "id不能为空")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    @Size(max = 50, message = "分支字典key长度不能超过50")
    @TableField(value = "`branch_key`")
    @Excel(name = "分支字典key")
    private String branchKey;

    /**
     * 自定义分支名称
     */
    @ApiModelProperty(value = "自定义分支名称")
    @Size(max = 50, message = "自定义分支名称长度不能超过50")
    @TableField(value = "`name`")
    @Excel(name = "自定义分支名称")
    private String name;

    /**
     * 自定义分支code
     */
    @ApiModelProperty(value = "自定义分支code")
    @Size(max = 50, message = "自定义分支code长度不能超过50")
    @TableField(value = "`code`")
    @Excel(name = "自定义分支code")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    @TableField(value = "`description`")
    @Excel(name = "描述")
    private String description;

    /**
     * 分支是否锁定
     */
    @ApiModelProperty(value = "分支是否锁定")
    @TableField(value = "`use_status`")
    @Excel(name = "分支是否锁定", replace = {"是_true", "否_false", "_null"})
    private Boolean useStatus;

    /**
     * 码线的仓库地址
     */
    @ApiModelProperty(value = "码线的仓库地址")
    @Size(max = 256, message = "码线的仓库地址长度不能超过256")
    @TableField(value = "`code_warehouse_path`")
    @Excel(name = "码线的仓库地址")
    private String codeWarehousePath;

    /**
     * 码线对应的本地仓库地址
     */
    @ApiModelProperty(value = "码线对应的本地仓库地址")
    @Size(max = 256, message = "码线对应的本地仓库地址长度不能超过256")
    @TableField(value = "`local_code_warehouse_path`")
    @Excel(name = "码线对应的本地仓库地址")
    private String localCodeWarehousePath;

    /**
     * 分支状态
     */
    @ApiModelProperty(value = "分支状态")
    @TableField(value = "`status`")
    @Excel(name = "分支状态",
            replace = {"开发中_UNDER_DEVELOPMENT", "废弃_DISCARD", "_null"})
    private ApplicationBranchStatus status;

    /**
     * 分支加解锁原因
     */
    @ApiModelProperty(value = "分支加解锁原因")
    @Size(max = 256, message = "分支加解锁原因长度不能超过256")
    @TableField(value = "`last_operation`")
    @Excel(name = "分支加解锁原因")
    private String lastOperation;

    /**
     * 最后加解锁时间
     */
    @ApiModelProperty(value = "最后加解锁时间")
    @TableField(value = "`last_operation_time`")
    @Excel(name = "最后加解锁时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime lastOperationTime;

    /**
     * 是否提交及构建:1:是;0:否;
     */
    @ApiModelProperty(value = "是否提交及构建:1:是;0:否;")
    @TableField(value = "`is_submit_build`")
    @Excel(name = "是否提交及构建:1:是;0:否;", replace = {"是_true", "否_false", "_null"})
    private Boolean isSubmitBuild;

    @Builder
    public ApplicationBranch(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime,
            Long updatedBy, Long applicationId, String branchKey, String name, String code, Boolean useStatus,
            String codeWarehousePath, String localCodeWarehousePath,
            ApplicationBranchStatus status, String lastOperation, LocalDateTime lastOperationTime,
            Boolean isSubmitBuild)
    {
        this.id = id;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.applicationId = applicationId;
        this.branchKey = branchKey;
        this.name = name;
        this.code = code;
        this.useStatus = useStatus;
        this.codeWarehousePath = codeWarehousePath;
        this.localCodeWarehousePath = localCodeWarehousePath;
        this.status = status;
        this.lastOperation = lastOperation;
        this.lastOperationTime = lastOperationTime;
        this.isSubmitBuild = isSubmitBuild;
    }
}
