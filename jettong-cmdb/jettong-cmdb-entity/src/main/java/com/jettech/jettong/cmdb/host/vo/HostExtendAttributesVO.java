package com.jettech.jettong.cmdb.host.vo;

import com.jettech.jettong.common.enumeration.ExtendType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 服务器扩展属性信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器扩展属性信息
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.host.vo
 * @className HostExtendAttributesVO
 * @date 2021/10/27 15:55
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "HostExtendAttributesVO", description = "服务器扩展属性信息")
public class HostExtendAttributesVO implements Serializable
{
    /**
     * 扩展属性key
     */
    @ApiModelProperty(value = "扩展属性key")
    private String key;
    /**
     * 扩展属性名称
     */
    @ApiModelProperty(value = "扩展属性名称")
    private String name;
    /**
     * 扩展属性类型
     */
    @ApiModelProperty(value = "扩展属性类型")
    private ExtendType inputType;
    /**
     * 是否启用正则校验
     */
    @ApiModelProperty(value = "是否启用正则校验")
    private Boolean isCheck;
    /**
     * 正则
     */
    @ApiModelProperty(value = "正则")
    private String regexp;
    /**
     * 最大长度
     */
    @ApiModelProperty(value = "最大长度")
    private Integer maxlength;
    /**
     * 未通过正则提示
     */
    @ApiModelProperty(value = "未通过正则提示")
    private String message;
    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean notEmpty;
}
