package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponents;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;


/**
 * 服务应用数据库配置信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用数据库配置信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationDb
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_db")
@ApiModel(value = "ApplicationDb", description = "服务应用数据库配置信息")
@AllArgsConstructor
public class ApplicationDb implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableId(value = "`application_id`", type = IdType.INPUT)
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    @NotEmpty(message = "请填写环境字典key")
    @Size(max = 50, message = "环境字典key长度不能超过50")
    @TableField(value = "`env_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENVIRONMENT)
    @Excel(name = "环境字典key")
    private String envKey;

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    @NotNull(message = "请填写数据库组件id")
    @TableField(value = "`db_components_id`")
    @Echo(api = "dbComponentsServiceImpl", beanClass = DbComponents.class)
    @Excel(name = "数据库组件id")
    private Long dbComponentsId;


    @Builder
    public ApplicationDb(
            Long applicationId, String envKey, Long dbCompentsId)
    {
        this.applicationId = applicationId;
        this.envKey = envKey;
        this.dbComponentsId = dbCompentsId;
    }

}
