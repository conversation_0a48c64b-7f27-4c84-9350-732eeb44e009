package com.jettech.jettong.cmdb.application.dto;

import com.jettech.jettong.cmdb.application.enumeration.ApplicationProgramType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationStatus;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 服务应用信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className Application
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationPageQuery", description = "服务应用信息")
public class ApplicationPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号，模糊")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称,模糊")
    private String name;

    /**
     * 应用状态
     */
    @ApiModelProperty(value = "应用状态")
    private ApplicationStatus status;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private Long responsible;

    /**
     * 应用类型
     */
    @ApiModelProperty(value = "应用类型")
    private ApplicationType type;

    /**
     * 程序类型
     */
    @ApiModelProperty(value = "程序类型")
    private ApplicationProgramType programType;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    private String envKey;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    private Long appComponentsId;

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    private Long dbComponentsId;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    private Long hostId;

}
