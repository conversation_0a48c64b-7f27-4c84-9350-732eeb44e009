package com.jettech.jettong.cmdb.dbcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 数据库组件信息查询实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件信息查询实体类
 * @projectName workspace-jettong-enterprise
 * @package com.jettech.jettong.cmdb.db_components.dto
 * @className DbComponentsQuery
 * @date 2021/12/1 10:47
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DbComponentsQuery", description = "数据库组件信息")
public class DbComponentsQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用ID
     */
    @ApiModelProperty(value = "服务应用ID")
    private Long applicationId;

    /**
     * 环境ID集合
     */
    @ApiModelProperty(value = "环境ID集合")
    private List<String> engKeyList;
}
