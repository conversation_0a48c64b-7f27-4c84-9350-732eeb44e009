package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.appcomponents.entity.AppComponents;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;


/**
 * 服务应用部署配置信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用部署配置信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationDeploy
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_deploy")
@ApiModel(value = "ApplicationDeploy", description = "服务应用部署配置信息")
@AllArgsConstructor
public class ApplicationDeploy implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    @Echo(api = "applicationServiceImpl", beanClass = Application.class)
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @TableField(value = "`app_components_id`")
    @Echo(api = "appComponentsServiceImpl", beanClass = AppComponents.class)
    @Excel(name = "应用组件id")
    private Long appComponentsId;

    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    @Size(max = 50, message = "环境字典key长度不能超过50")
    @TableField(value = "`env_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENVIRONMENT)
    @Excel(name = "环境字典key")
    private String envKey;

    /**
     * 工程/模块id
     */
    @ApiModelProperty(value = "工程/模块id")
    @TableField(value = "`module_id`")
    @Excel(name = "工程/模块id")
    private Long moduleId;

    /**
     * 检查url
     */
    @ApiModelProperty(value = "检查url")
    @Size(max = 600, message = "检查url长度不能超过600")
    @TableField(value = "`check_url`")
    @Excel(name = "检查url")
    private String checkUrl;

    /**
     * 部署版本
     */
    @ApiModelProperty(value = "部署版本")
    @TableField(exist = false)
    @Excel(name = "部署版本")
    private String deployVersion;

    @Builder
    public ApplicationDeploy(
            Long applicationId, Long appComponentsId, String envKey, Long moduleId, String checkUrl,
            String deployVersion)
    {
        this.applicationId = applicationId;
        this.appComponentsId = appComponentsId;
        this.envKey = envKey;
        this.moduleId = moduleId;
        this.checkUrl = checkUrl;
        this.deployVersion = deployVersion;
    }

}
