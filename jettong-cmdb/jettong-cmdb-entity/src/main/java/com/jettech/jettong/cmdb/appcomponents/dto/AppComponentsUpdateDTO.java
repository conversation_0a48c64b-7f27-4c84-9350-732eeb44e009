package com.jettech.jettong.cmdb.appcomponents.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.cmdb.appcomponents.entity.AppComponentsExtend;
import com.jettech.jettong.cmdb.application.entity.ApplicationDeploy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 应用组件信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.dto
 * @className AppComponentsUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsUpdateDTO", description = "应用组件信息")
public class AppComponentsUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 应用组件名称
     */
    @ApiModelProperty(value = "应用组件名称")
    @Size(max = 100, message = "应用组件名称长度不能超过100")
    private String name;

    /**
     * 应用组件描述
     */
    @ApiModelProperty(value = "应用组件描述")
    @Size(max = 200, message = "应用组件描述长度不能超过200")
    private String description;

    /**
     * 脚本存放目录
     */
    @ApiModelProperty(value = "脚本存放目录")
    @Size(max = 1000, message = "脚本存放目录长度不能超过1000")
    private String scriptDir;

    /**
     * 部署目录
     */
    @ApiModelProperty(value = "部署目录")
    @Size(max = 1000, message = "部署目录长度不能超过1000")
    private String deployDir;

    /**
     * 发布目录
     */
    @ApiModelProperty(value = "发布目录")
    @Size(max = 1000, message = "发布目录长度不能超过1000")
    private String publishDir;

    @ApiModelProperty(value = "Python目录")
    @Size(max = 1000, message = "Python目录长度不能超过1000")
    private String pythonDir;

    /**
     * 控制台端口
     */
    @ApiModelProperty(value = "控制台端口")
    private Integer consolePort;

    /**
     * 应用权限用户
     */
    @ApiModelProperty(value = "应用权限用户")
    @Size(max = 100, message = "应用权限用户长度不能超过100")
    private String permissionUser;

    /**
     * 应用组件扩展信息
     */
    @ApiModelProperty(value = "应用组件扩展信息")
    private List<AppComponentsExtend> appComponentsExtends;

    /**
     * 服务应用部署配置信息
     */
    @ApiModelProperty(value = "服务应用部署配置信息")
    private ApplicationDeploy applicationDeploy;

}
