package com.jettech.jettong.cmdb.appcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;


/**
 * 应用组件机构授权信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件机构授权信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.entity
 * @className AppComponentsOrg
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_app_components_org")
@ApiModel(value = "AppComponentsOrg", description = "应用组件机构授权信息")
@AllArgsConstructor
public class AppComponentsOrg implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @NotNull(message = "请填写应用组件id")
    @TableId(value = "`app_components_id`", type = IdType.INPUT)
    @Excel(name = "应用组件id")
    private Long appComponentsId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @NotNull(message = "请填写机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "机构id")
    private Long orgId;

    /**
     * 是否创建机构
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;

    @Builder
    public AppComponentsOrg(
            Long appComponentsId, Long orgId, Boolean isCreate)
    {
        this.appComponentsId = appComponentsId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }

}
