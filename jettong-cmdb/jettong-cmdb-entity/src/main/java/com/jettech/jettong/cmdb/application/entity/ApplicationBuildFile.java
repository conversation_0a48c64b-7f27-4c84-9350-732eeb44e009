package com.jettech.jettong.cmdb.application.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.application.enumeration.BuildFileScriptState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 构建参数文件信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationBuildFile
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_build_file")
@ApiModel(value = "ApplicationBuildFile", description = "构建参数文件信息")
@AllArgsConstructor
public class ApplicationBuildFile extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    private Long applicationId;

    /**
     * 构建参数文件名称
     */
    @ApiModelProperty(value = "构建参数文件名称")
    @NotEmpty(message = "请填写构建参数文件名称")
    @Size(max = 255, message = "构建参数文件名称长度不能超过255")
    @TableField(value = "`script_name`")
    private String scriptName;

    /**
     * 构建参数文件所在脚本仓库地址
     */
    @ApiModelProperty(value = "构建参数文件所在脚本仓库地址")
    @Size(max = 4000, message = "构建参数文件所在脚本仓库地址长度不能超过4000")
    @TableField(value = "`script_warehouse_path`")
    private String scriptWarehousePath;

    /**
     * 脚本所在脚本仓库版本号
     */
    @ApiModelProperty(value = "脚本所在脚本仓库版本号")
    @Size(max = 20, message = "脚本所在脚本仓库版本号长度不能超过20")
    @TableField(value = "`script_warehouse_version`")
    private String scriptWarehouseVersion;

    /**
     * 脚本状态 #BuildFileScriptState{SEND_NO:未下发;SEND_FAIL:下发失败;SEND_SUCCESS:下发成功;}
     */
    @ApiModelProperty(value = "脚本状态 #BuildFileScriptState{SEND_NO:未下发;SEND_FAIL:下发失败;SEND_SUCCESS:下发成功;}")
    @TableField(value = "`state`")
    private BuildFileScriptState state;

    /**
     * 脚本文件内容
     */
    @ApiModelProperty(value = "脚本文件内容")
    @TableField(exist = false)
    private String content;

    /**
     * 服务应用code
     */
    @ApiModelProperty(value = "服务应用code")
    @TableField(exist = false)
    private String applicationCode;

    @Builder
    public ApplicationBuildFile(Long id, Long createdBy, LocalDateTime createTime, Long updatedBy,
            LocalDateTime updateTime,
            Long applicationId, String scriptName, String scriptWarehousePath, String scriptWarehouseVersion,
            BuildFileScriptState state, String content)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
        this.applicationId = applicationId;
        this.scriptName = scriptName;
        this.scriptWarehousePath = scriptWarehousePath;
        this.scriptWarehouseVersion = scriptWarehouseVersion;
        this.state = state;
        this.content = content;
    }

}
