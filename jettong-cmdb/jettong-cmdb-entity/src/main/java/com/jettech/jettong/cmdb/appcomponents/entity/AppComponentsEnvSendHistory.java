package com.jettech.jettong.cmdb.appcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 应用组件参数文件下发历史信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件参数文件下发历史信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.entity
 * @className AppComponentsEnvSendHistory
 * @date 2021-10-25
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_app_components_env_send_history")
@ApiModel(value = "AppComponentsEnvSendHistory", description = "应用组件参数文件下发历史信息")
public class AppComponentsEnvSendHistory implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @NotNull(message = "请填写应用组件id")
    @TableField(value = "`app_components_id`")
    @Echo(api = "appComponentsServiceImpl", beanClass = AppComponents.class)
    @Excel(name = "应用组件id")
    private Long appComponentsId;

    /**
     * 是否下发成功
     */
    @ApiModelProperty(value = "是否下发成功")
    @TableField(value = "`is_success`")
    @Excel(name = "是否下发成功", replace = {"是_true", "否_false", "_null"})
    private Boolean isSuccess;

    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    @TableField(value = "`send_time`")
    @Excel(name = "下发时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime sendTime;

    /**
     * 下发人id
     */
    @ApiModelProperty(value = "下发人id")
    @TableField(value = "`user_id`")
    @Excel(name = "下发人id")
    private Long userId;

    /**
     * 下发人名称
     */
    @ApiModelProperty(value = "下发人名称")
    @TableField(value = "`send_user_name`")
    @Excel(name = "下发人名称")
    private String sendUserName;

    /**
     * 消息
     */
    @ApiModelProperty(value = "消息")
    @TableField(exist = false)
    private String message;

    @Builder
    public AppComponentsEnvSendHistory(Long appComponentsId, Boolean isSuccess, LocalDateTime sendTime, Long userId,
            String sendUserName, String message)
    {
        this.appComponentsId = appComponentsId;
        this.isSuccess = isSuccess;
        this.sendTime = sendTime;
        this.userId = userId;
        this.sendUserName = sendUserName;
        this.message = message;
    }

}
