package com.jettech.jettong.cmdb.host.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 服务器类型枚举。VIRTUAL_MACHINE:虚拟机;PHYSICAL_MACHINE:物理机;UNKNOWN:未知
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器类型枚举。VIRTUAL_MACHINE:虚拟机;PHYSICAL_MACHINE:物理机;UNKNOWN:未知
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.enumeration
 * @className HostType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HostType", description = "服务器类型枚举。VIRTUAL_MACHINE:虚拟机;PHYSICAL_MACHINE:物理机;UNKNOWN:未知")
public enum HostType implements BaseEnum
{

    /**
     * VIRTUAL_MACHINE="虚拟机"
     */
    VIRTUAL_MACHINE("虚拟机"),
    /**
     * PHYSICAL_MACHINE="物理机"
     */
    PHYSICAL_MACHINE("物理机"),
    /**
     * UNKNOWN="未知"
     */
    UNKNOWN("未知"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static HostType match(String val, HostType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static HostType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(HostType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "服务器类型编码", allowableValues = "VIRTUAL_MACHINE,PHYSICAL_MACHINE,UNKNOWN",
            example = "VIRTUAL_MACHINE")
    public String getCode()
    {
        return this.name();
    }

}
