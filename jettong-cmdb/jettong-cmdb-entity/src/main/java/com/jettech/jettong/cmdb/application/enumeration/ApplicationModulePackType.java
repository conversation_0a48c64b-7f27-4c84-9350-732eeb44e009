package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 打包方式枚举.FULL:全量;INCREMENT:增量
 *
 * <AUTHOR>
 * @version 1.0
 * @description 打包方式枚举.FULL:全量;INCREMENT:增量
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationModulePackType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationModulePackType", description = "打包方式枚举.FULL:全量;INCREMENT:增量")
public enum ApplicationModulePackType implements BaseEnum
{

    /**
     * FULL="全量"
     */
    FULL("全量"),
    /**
     * INCREMENT="增量"
     */
    INCREMENT("增量"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationModulePackType match(String val, ApplicationModulePackType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationModulePackType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationModulePackType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "FULL,INCREMENT", example = "FULL")
    public String getCode()
    {
        return this.name();
    }

}
