package com.jettech.jettong.cmdb.appcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 应用组件部署历史信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件部署历史信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.entity
 * @className AppComponentsDeployHistory
 * @date 2021-12-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_app_components_deploy_history")
@ApiModel(value = "AppComponentsDeployHistory", description = "应用组件部署历史信息")
public class AppComponentsDeployHistory implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，应用组件id
     */
    @ApiModelProperty(value = "外键，应用组件id")
    @NotNull(message = "请填写外键，应用组件id")
    @TableField(value = "app_components_id")
    @Excel(name = "外键，应用组件id")
    private Long appComponentsId;

    /**
     * 部署版本
     */
    @ApiModelProperty(value = "部署版本")
    @Size(max = 50, message = "部署版本长度不能超过50")
    @TableField(value = "version", condition = LIKE)
    @Excel(name = "部署版本")
    private String version;

    /**
     * 部署人ID
     */
    @ApiModelProperty(value = "部署人ID")
    @TableField(value = "deployer_id")
    @Excel(name = "部署人ID")
    private Long deployerId;

    /**
     * 部署人
     */
    @ApiModelProperty(value = "部署人")
    @Size(max = 100, message = "部署人长度不能超过100")
    @TableField(value = "deployer", condition = LIKE)
    @Excel(name = "部署人")
    private String deployer;

    /**
     * 部署时间
     */
    @ApiModelProperty(value = "部署时间")
    @TableField(value = "deploy_time")
    @Excel(name = "部署时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime deployTime;


    @Builder
    public AppComponentsDeployHistory(
            Long appComponentsId, String version, Long deployerId, String deployer, LocalDateTime deployTime)
    {
        this.appComponentsId = appComponentsId;
        this.version = version;
        this.deployerId = deployerId;
        this.deployer = deployer;
        this.deployTime = deployTime;
    }

}
