package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 构建参数文件状态枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件状态枚举
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className BuildFileScriptState
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "BuildFileScriptState",
        description = "构建参数文件状态 #BuildFileScriptState{SEND_NO:未下发;SEND_FAIL:下发失败;SEND_SUCCESS:下发成功;-枚举")
public enum BuildFileScriptState implements BaseEnum
{

    /**
     * SEND_NO="未下发"
     */
    SEND_NO("未下发"),
    /**
     * SEND_FAIL="下发失败"
     */
    SEND_FAIL("下发失败"),
    /**
     * SEND_SUCCESS="下发成功"
     */
    SEND_SUCCESS("下发成功");

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static BuildFileScriptState match(String val, BuildFileScriptState def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static BuildFileScriptState get(String val)
    {
        return match(val, null);
    }

    public boolean eq(BuildFileScriptState val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "SEND_NO,SEND_FAIL,SEND_SUCCESS", example = "SEND_NO")
    public String getCode()
    {
        return this.name();
    }

}
