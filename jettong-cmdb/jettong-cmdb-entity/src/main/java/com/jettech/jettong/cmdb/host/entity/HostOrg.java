package com.jettech.jettong.cmdb.host.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 服务器授权机构信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器授权机构信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.entity
 * @className HostOrg
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_host_org")
@ApiModel(value = "HostOrg", description = "服务器授权机构信息")
public class HostOrg implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    @NotNull(message = "请填写服务器id")
    @TableField(value = "`host_id`")
    @Excel(name = "服务器id")
    private Long hostId;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @NotNull(message = "请填写机构id")
    @TableField(value = "`org_id`")
    @Excel(name = "机构id")
    private Long orgId;

    /**
     * 是否创建机构
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;

    @Builder
    public HostOrg(
            Long hostId, Long orgId, Boolean isCreate)
    {
        this.hostId = hostId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }

}
