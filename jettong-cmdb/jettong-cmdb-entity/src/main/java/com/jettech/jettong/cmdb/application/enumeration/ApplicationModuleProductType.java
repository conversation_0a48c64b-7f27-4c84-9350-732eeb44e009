package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 工程包类型枚举.DIR:目录;JAR:jar;WAR:war;TAR:tar;ZIP:zip;CUSTOM:自定义
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工程包类型枚举.DIR:目录;JAR:jar;WAR:war;TAR:tar;ZIP:zip;CUSTOM:自定义
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationProgramType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationProgramType",
        description = "工程包类型枚举.DIR:目录;JAR:jar;WAR:war;TAR:tar;ZIP:zip;CUSTOM:自定义")
public enum ApplicationModuleProductType implements BaseEnum
{

    /**
     * 工程包类型枚举.DIR:目录;JAR:jar;WAR:war;TAR:tar;ZIP:zip;CUSTOM:自定义
     */
    DIR("目录"),
    JAR("jar"),
    WAR("war"),
    TAR("tar"),
    ZIP("zip"),
    CUSTOM("自定义");

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationModuleProductType match(String val, ApplicationModuleProductType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationModuleProductType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationModuleProductType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "DIR,JAR,WAR,TAR,ZIP,CUSTOM", example = "JAR")
    public String getCode()
    {
        return this.name();
    }

}
