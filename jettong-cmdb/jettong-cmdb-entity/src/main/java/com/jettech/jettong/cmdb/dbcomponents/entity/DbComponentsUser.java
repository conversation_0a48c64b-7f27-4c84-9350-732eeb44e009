package com.jettech.jettong.cmdb.dbcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据库组件用户信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件用户信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.entity
 * @className DbComponentsUser
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_db_components_user")
@ApiModel(value = "DbComponentsUser", description = "数据库组件用户信息")
public class DbComponentsUser implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id")
    @NotNull(message = "数据库组件实例id不能为空")
    @Excel(name = "主键")
    private Long id;

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    @NotNull(message = "请填写数据库组件id")
    @TableField(value = "`db_components_id`")
    @Excel(name = "数据库组件id")
    private Long dbComponentsId;

    /**
     * 登陆数据库用户名
     */
    @ApiModelProperty(value = "登陆数据库用户名")
    @NotEmpty(message = "请填写登陆数据库用户名")
    @Size(max = 50, message = "登陆数据库用户名长度不能超过50")
    @TableField(value = "`user_name`", condition = LIKE)
    @Excel(name = "登陆数据库用户名")
    private String userName;

    /**
     * 登陆数据库密码（加密后的）
     */
    @ApiModelProperty(value = "登陆数据库密码（加密后的）")
    @NotEmpty(message = "请填写登陆数据库密码（加密后的）")
    @Size(max = 100, message = "登陆数据库密码（加密后的）长度不能超过100")
    @TableField(value = "`password`", condition = LIKE)
    @Excel(name = "登陆数据库密码（加密后的）")
    private String password;

    /**
     * 用户描述
     */
    @ApiModelProperty(value = "用户描述")
    @Size(max = 500, message = "用户描述长度不能超过500")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "用户描述")
    private String description;


    @Builder
    public DbComponentsUser(Long id,
            Long dbComponentsId, String userName, String password, String description)
    {
        this.id = id;
        this.dbComponentsId = dbComponentsId;
        this.userName = userName;
        this.password = password;
        this.description = description;
    }

}
