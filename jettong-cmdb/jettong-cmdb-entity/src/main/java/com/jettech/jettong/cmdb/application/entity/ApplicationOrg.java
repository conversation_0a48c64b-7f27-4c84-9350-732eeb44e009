package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;


/**
 * 服务应用授权机构信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用授权机构信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationOrg
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_org")
@ApiModel(value = "ApplicationOrg", description = "服务应用授权机构信息")
@AllArgsConstructor
public class ApplicationOrg implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "请填写服务应用id")
    @TableField(value = "`application_id`")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 授权机构id
     */
    @ApiModelProperty(value = "授权机构id")
    @NotNull(message = "请填写授权机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "授权机构id")
    private Long orgId;

    /**
     * 是否创建机构，默认false
     */
    @ApiModelProperty(value = "是否创建机构")
    @TableField(exist = false)
    private Boolean isCreate = false;


    @Builder
    public ApplicationOrg(
            Long applicationId, Long orgId, Boolean isCreate)
    {
        this.applicationId = applicationId;
        this.orgId = orgId;
        this.isCreate = isCreate;
    }

}
