package com.jettech.jettong.cmdb.application.dto;

import com.jettech.jettong.cmdb.application.entity.ApplicationBuildFileSend;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 构建参数文件下发信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件下发信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationBuildFileSendSaveDTO
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBuildFileSendSaveDTO", description = "构建参数文件下发信息")
public class ApplicationBuildFileSendSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 构建参数文件id集合
     */
    @ApiModelProperty(value = "构建参数文件id集合")
    @NotNull(message = "请填写构建参数文件id集合")
    private List<Long> buildFileIds;

    /**
     * 下发目标机信息
     */
    @ApiModelProperty(value = "下发目标机信息")
    @NotNull(message = "下发目标机信息")
    private List<ApplicationBuildFileSend> applicationBuildFileSends;

}
