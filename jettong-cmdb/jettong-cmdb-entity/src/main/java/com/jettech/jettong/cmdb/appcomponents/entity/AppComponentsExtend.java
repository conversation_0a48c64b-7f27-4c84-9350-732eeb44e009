package com.jettech.jettong.cmdb.appcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.jettong.common.enumeration.ExtendType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;


/**
 * 应用组件扩展信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件扩展信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.entity
 * @className AppComponentsExtend
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_app_components_extend")
@ApiModel(value = "AppComponentsExtend", description = "应用组件扩展信息")
public class AppComponentsExtend implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @NotNull(message = "请填写应用组件id")
    @TableField(value = "`app_components_id`")
    @Excel(name = "应用组件id")
    private Long appComponentsId;

    /**
     * 扩展属性key
     */
    @ApiModelProperty(value = "扩展属性key")
    @NotEmpty(message = "请填写扩展属性key")
    @Size(max = 50, message = "扩展属性key长度不能超过50")
    @TableField(value = "`key`")
    @Excel(name = "扩展属性key")
    private String key;

    /**
     * 参数类型
     */
    @ApiModelProperty(value = "参数类型")
    @NotEmpty(message = "请填写参数类型")
    @Size(max = 20, message = "参数类型长度不能超过20")
    @TableField(value = "`type`")
    @Excel(name = "参数类型", replace = {"密码框_PASSWORD", "文本框_INPUT", "文本域_TEXTAREA", "其它_OTHER", "_null"})
    private ExtendType type;

    /**
     * 扩展属性value
     */
    @ApiModelProperty(value = "扩展属性value")
    @Size(max = 1000, message = "扩展属性value长度不能超过1000")
    @TableField(value = "`value`")
    @Excel(name = "扩展属性value")
    private String value;

    /**
     * 非数据库字段，扩展字段名称
     */
    @ApiModelProperty(value = "非数据库字段，扩展字段名称")
    @TableField(exist = false)
    private String name;

    @Builder
    public AppComponentsExtend(
            Long appComponentsId, String key, ExtendType type, String value, String name)
    {
        this.appComponentsId = appComponentsId;
        this.key = key;
        this.type = type;
        this.value = value;
        this.name = name;
    }

}
