package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 构建参数文件下发历史信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件下发历史信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationBuildFileSendHistory
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBuildFileSendHistoryPageQuery", description = "构建参数文件下发历史信息")
public class ApplicationBuildFileSendHistoryPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 构建参数文件id
     */
    @ApiModelProperty(value = "构建参数文件id")
    private Long buildFileId;
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    private Long applicationId;
    /**
     * 下发引擎id
     */
    @ApiModelProperty(value = "下发引擎id")
    private Long engineId;
    /**
     * 构建参数文件名称
     */
    @ApiModelProperty(value = "构建参数文件名称")
    private String scriptName;
    /**
     * 构建参数文件所在目标机器路径
     */
    @ApiModelProperty(value = "构建参数文件所在目标机器路径")
    private String destPath;
    /**
     * 脚本所在脚本仓库版本号
     */
    @ApiModelProperty(value = "脚本所在脚本仓库版本号")
    private String scriptWarehouseVersion;

}
