package com.jettech.jettong.cmdb.application.dto;

import com.jettech.jettong.cmdb.application.entity.ApplicationEngine;
import com.jettech.jettong.cmdb.application.entity.ApplicationEnv;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationProgramType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationStatus;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 服务应用信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationSaveDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationSaveDTO", description = "服务应用信息")
public class ApplicationSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 50, message = "编号长度不能超过50")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 200, message = "名称长度不能超过200")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 应用状态
     */
    @ApiModelProperty(value = "应用状态 #ApplicationStatus{UNDER_CONSTRUCTION:在建;PRODUCTION:已投产;OBSOLETE:已废弃}")
    private ApplicationStatus status;

    /**
     * 负责人id
     */
    @ApiModelProperty(value = "负责人id")
    private Long responsible;
    /**
     * 应用类型
     */
    @ApiModelProperty(value = "应用类型")
    private ApplicationType type;

    /**
     * 程序类型
     */
    @ApiModelProperty(value = "程序类型")
    private ApplicationProgramType programType;

    /**
     * 分支策略字典
     */
    @ApiModelProperty(value = "分支策略字典")
    @Size(max = 50, message = "分支策略字典长度不能超过50")
    private String branchingStrategyKey;

    /**
     * 引擎信息
     */
    @ApiModelProperty(value = "引擎信息")
    private List<ApplicationEngine> applicationEngines;

    /**
     * 环境信息
     */
    @ApiModelProperty(value = "环境信息")
    @NotEmpty(message = "请选择环境信息")
    @NotNull(message = "请选择环境信息")
    private List<ApplicationEnv> applicationEnvs;

}
