package com.jettech.jettong.cmdb.host.vo;

import com.jettech.jettong.base.entity.sys.engine.Engine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 同步服务器minionKey返回对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 同步服务器minionKey返回对象
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.vo
 * @className SyncMinionKeyResult
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "SyncMinionKeyResult", description = "同步服务器minionKey返回对象")
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SyncMinionKeyResult implements Serializable
{
    @ApiModelProperty(value = "服务器关联引擎信息")
    private Engine engine;

    @ApiModelProperty(value = "服务器管理引擎下minion集合")
    private List<String> minionKeys;

}