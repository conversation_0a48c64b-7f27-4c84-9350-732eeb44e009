package com.jettech.jettong.cmdb.appcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.cmdb.appcomponents.enumeration.AppComponentsSendStatus;
import com.jettech.jettong.cmdb.application.entity.ApplicationDeploy;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;


/**
 * 应用组件信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.entity
 * @className AppComponents
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_app_components")
@ApiModel(value = "AppComponents", description = "应用组件信息")
@AllArgsConstructor
public class AppComponents extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 应用组件名称
     */
    @ApiModelProperty(value = "应用组件名称")
    @Size(max = 100, message = "应用组件名称长度不能超过100")
    @TableField(value = "`name`")
    @Excel(name = "应用组件名称")
    private String name;

    /**
     * 应用组件描述
     */
    @ApiModelProperty(value = "应用组件描述")
    @Size(max = 200, message = "应用组件描述长度不能超过200")
    @TableField(value = "`description`")
    @Excel(name = "应用组件描述")
    private String description;

    /**
     * 应用组件类型字典key
     */
    @ApiModelProperty(value = "应用组件类型字典key")
    @Size(max = 50, message = "应用组件类型字典key长度不能超过50")
    @TableField(value = "`app_components_type_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.APP_COMPONENTS_TYPE)
    @Excel(name = "应用组件类型字典key")
    private String appComponentsTypeKey;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    @TableField(value = "`host_id`")
    @Excel(name = "服务器id")
    private Long hostId;

    /**
     * 冗余字段，服务器ip
     */
    @ApiModelProperty(value = "服务器ip")
    @Size(max = 20, message = "服务器ip长度不能超过20")
    @TableField(value = "`server_ip`")
    @Excel(name = "服务器ip")
    private String serverIp;

    /**
     * 服务端口
     */
    @ApiModelProperty(value = "服务端口")
    @TableField(value = "`server_port`")
    @Excel(name = "服务端口")
    private Integer serverPort;

    /**
     * 脚本存放目录
     */
    @ApiModelProperty(value = "脚本存放目录")
    @Size(max = 1000, message = "脚本存放目录长度不能超过1000")
    @TableField(value = "`script_dir`")
    @Excel(name = "脚本存放目录")
    private String scriptDir;

    /**
     * 部署目录
     */
    @ApiModelProperty(value = "部署目录")
    @Size(max = 1000, message = "部署目录长度不能超过1000")
    @TableField(value = "`deploy_dir`")
    @Excel(name = "部署目录")
    private String deployDir;

    /**
     * 发布目录
     */
    @ApiModelProperty(value = "发布目录")
    @Size(max = 1000, message = "发布目录长度不能超过1000")
    @TableField(value = "`publish_dir`")
    @Excel(name = "发布目录")
    private String publishDir;

    /**
     * Python目录
     */
    @ApiModelProperty(value = "Python目录")
    @Size(max = 1000, message = "Python目录长度不能超过1000")
    @TableField(value = "`python_dir`")
    @Excel(name = "Python目录")
    private String pythonDir;

    /**
     * 控制台端口
     */
    @ApiModelProperty(value = "控制台端口")
    @TableField(value = "`console_port`")
    @Excel(name = "控制台端口")
    private Integer consolePort;

    /**
     * 应用权限用户
     */
    @ApiModelProperty(value = "应用权限用户")
    @Size(max = 100, message = "应用权限用户长度不能超过100")
    @TableField(value = "`permission_user`")
    @Excel(name = "应用权限用户")
    private String permissionUser;

    /**
     * 下发状态
     */
    @ApiModelProperty(value = "下发状态")
    @TableField(value = "`send_status`")
    @Excel(name = "下发状态")
    private AppComponentsSendStatus sendStatus;

    /**
     * 下发状态
     */
    @ApiModelProperty(value = "下发时间")
    @TableField(value = "`send_time`")
    @Excel(name = "下发时间")
    private LocalDateTime sendTime;

    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "创建机构id")
    private Long orgId;

    /**
     * 应用组件扩展属性
     */
    @ApiModelProperty(value = "应用组件扩展信息")
    @TableField(exist = false)
    private List<AppComponentsExtend> appComponentsExtends;

    /**
     * 服务应用部署配置信息
     */
    @ApiModelProperty(value = "服务应用部署配置信息")
    @TableField(exist = false)
    private ApplicationDeploy applicationDeploy;

    @Builder
    public AppComponents(Long id, LocalDateTime createTime, Long createdBy, LocalDateTime updateTime, Long updatedBy,
            String name, String description, String appComponentsTypeKey, Long hostId, String serverIp,
            Integer serverPort, String scriptDir, String deployDir, String publishDir, String pythonDir,
            Integer consolePort, String permissionUser, AppComponentsSendStatus sendStatus, LocalDateTime sendTime,
            Long orgId, List<AppComponentsExtend> appComponentsExtends, ApplicationDeploy applicationDeploy)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.name = name;
        this.description = description;
        this.appComponentsTypeKey = appComponentsTypeKey;
        this.hostId = hostId;
        this.serverIp = serverIp;
        this.serverPort = serverPort;
        this.scriptDir = scriptDir;
        this.deployDir = deployDir;
        this.publishDir = publishDir;
        this.pythonDir = pythonDir;
        this.consolePort = consolePort;
        this.permissionUser = permissionUser;
        this.sendStatus = sendStatus;
        this.sendTime = sendTime;
        this.orgId = orgId;
        this.appComponentsExtends = appComponentsExtends;
        this.applicationDeploy = applicationDeploy;
    }

}
