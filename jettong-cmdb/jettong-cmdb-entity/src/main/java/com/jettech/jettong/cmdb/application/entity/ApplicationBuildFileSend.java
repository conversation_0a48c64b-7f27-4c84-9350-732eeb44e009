package com.jettech.jettong.cmdb.application.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.engine.Engine;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.ENGINE_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 构建参数文件下发信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件下发信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationBuildFileSend
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode
@Accessors(chain = true)
@TableName("cmdb_application_build_file_send")
@ApiModel(value = "ApplicationBuildFileSend", description = "构建参数文件下发信息")
@AllArgsConstructor
public class ApplicationBuildFileSend implements EchoVO, Serializable
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 构建参数文件id
     */
    @ApiModelProperty(value = "构建参数文件id")
    @NotNull(message = "请填写构建参数文件id")
    @TableField(value = "`build_file_id`")
    private Long buildFileId;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    private Long applicationId;

    /**
     * 下发引擎id
     */
    @ApiModelProperty(value = "下发引擎id")
    @TableField(value = "`engine_id`")
    @Echo(api = ENGINE_ID_FEIGN_CLASS, beanClass = Engine.class)
    private Long engineId;

    /**
     * 下发服务器IP
     */
    @ApiModelProperty(value = "下发服务器IP")
    @TableField(value = "`host_ip`")
    private String hostIp;

    /**
     * 构建参数文件名称
     */
    @ApiModelProperty(value = "构建参数文件名称")
    @NotEmpty(message = "请填写构建参数文件名称")
    @Size(max = 255, message = "构建参数文件名称长度不能超过255")
    @TableField(value = "`script_name`")
    private String scriptName;

    /**
     * 构建参数文件所在目标机器路径
     */
    @ApiModelProperty(value = "构建参数文件所在目标机器路径")
    @Size(max = 4000, message = "构建参数文件所在目标机器路径长度不能超过4000")
    @TableField(value = "`dest_path`")
    private String destPath;

    /**
     * 脚本所在脚本仓库版本号
     */
    @ApiModelProperty(value = "脚本所在脚本仓库版本号")
    @Size(max = 20, message = "脚本所在脚本仓库版本号长度不能超过20")
    @TableField(value = "`script_warehouse_version`")
    private String scriptWarehouseVersion;

    @ApiModelProperty(value = "创建时间/下发时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人ID/下发人id")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long createdBy;

    @ApiModelProperty(value = "控制用户")
    @TableField(exist = false)
    private String consoleUser;

    @Builder
    public ApplicationBuildFileSend(Long createdBy, LocalDateTime createTime,
            Long buildFileId, Long applicationId, Long engineId, String hostIp, String scriptName, String destPath,
            String scriptWarehouseVersion, String consoleUser)
    {
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.buildFileId = buildFileId;
        this.applicationId = applicationId;
        this.engineId = engineId;
        this.scriptName = scriptName;
        this.hostIp = hostIp;
        this.destPath = destPath;
        this.scriptWarehouseVersion = scriptWarehouseVersion;
        this.consoleUser = consoleUser;
    }

}
