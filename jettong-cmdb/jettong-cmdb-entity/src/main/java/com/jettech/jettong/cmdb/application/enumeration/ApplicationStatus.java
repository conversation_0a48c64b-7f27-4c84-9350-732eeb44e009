package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 服务应用信息实体注释中生成的类型枚举
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用信息实体注释中生成的类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationStatus
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationStatus",
        description = "应用状态 #ApplicationStatus{UNDER_CONSTRUCTION:在建;PRODUCTION:已投产;OBSOLETE:已废弃-枚举")
public enum ApplicationStatus implements BaseEnum
{

    /**
     * UNDER_CONSTRUCTION="在建"
     */
    UNDER_CONSTRUCTION("在建"),
    /**
     * PRODUCTION="已投产"
     */
    PRODUCTION("已投产"),
    /**
     * OBSOLETE="已废弃"
     */
    OBSOLETE("已废弃"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationStatus match(String val, ApplicationStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "UNDER_CONSTRUCTION,PRODUCTION,OBSOLETE",
            example = "UNDER_CONSTRUCTION")
    public String getCode()
    {
        return this.name();
    }

}
