package com.jettech.jettong.cmdb.appcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 应用组件信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dtoPageQuery
 * @className AppComponents
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsPageQuery", description = "应用组件信息")
public class AppComponentsPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 应用组件名称
     */
    @ApiModelProperty(value = "应用组件名称")
    private String name;

    /**
     * 应用组件类型字典key
     */
    @ApiModelProperty(value = "应用组件类型字典key")
    private String appComponentsTypeKey;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    private Long applicationId;

    /**
     * 服务器id
     */
    @ApiModelProperty(value = "服务器id")
    private Long hostId;

    /**
     * 服务器IP
     */
    @ApiModelProperty(value = "服务器IP")
    private String serverIp;

    /**
     * 服务端口
     */
    @ApiModelProperty(value = "服务端口")
    private Integer serverPort;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

}
