package com.jettech.jettong.cmdb.appcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 应用组件部署历史信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件部署历史信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dto
 * @className AppComponentsDeployHistorySaveDTO
 * @date 2021-12-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsDeployHistorySaveDTO", description = "应用组件部署历史信息")
public class AppComponentsDeployHistorySaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键，应用组件id
     */
    @ApiModelProperty(value = "外键，应用组件id")
    @NotNull(message = "请填写外键，应用组件id")
    private Long appComponentsId;
    /**
     * 部署版本
     */
    @ApiModelProperty(value = "部署版本")
    @Size(max = 50, message = "部署版本长度不能超过50")
    private String version;
    /**
     * 部署人ID
     */
    @ApiModelProperty(value = "部署人ID")
    private Long deployerId;
    /**
     * 部署人
     */
    @ApiModelProperty(value = "部署人")
    @Size(max = 100, message = "部署人长度不能超过100")
    private String deployer;
    /**
     * 部署时间
     */
    @ApiModelProperty(value = "部署时间")
    private LocalDateTime deployTime;

}
