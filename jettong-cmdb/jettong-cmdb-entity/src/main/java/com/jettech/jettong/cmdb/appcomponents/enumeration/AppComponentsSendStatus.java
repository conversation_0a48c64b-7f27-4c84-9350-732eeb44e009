package com.jettech.jettong.cmdb.appcomponents.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 应用组件文件下发状态枚举 NOT_SEND:未下发;SEND_SUCCESS:下发成功;SEND_FAIL:下发失败
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件文件下发状态枚举 NOT_SEND:未下发;SEND_SUCCESS:下发成功;SEND_FAIL:下发失败
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.enumeration
 * @className HostComponentType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HostComponentType", description = "应用组件文件下发状态枚举 NOT_SEND:未下发;SEND_SUCCESS:下发成功;SEND_FAIL:下发失败")
public enum AppComponentsSendStatus implements BaseEnum
{

    /**
     * NOT_SEND="未下发"
     */
    NOT_SEND("未下发"),
    /**
     * SEND_SUCCESS="下发成功"
     */
    SEND_SUCCESS("下发成功"),
    /**
     * SEND_FAIL="下发失败"
     */
    SEND_FAIL("下发失败"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static AppComponentsSendStatus match(String val, AppComponentsSendStatus def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static AppComponentsSendStatus get(String val)
    {
        return match(val, null);
    }

    public boolean eq(AppComponentsSendStatus val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "应用组件下发状态编码", allowableValues = "NOT_SEND,SEND_SUCCESS,SEND_FAIL", example = "NOT_SEND")
    public String getCode()
    {
        return this.name();
    }

}
