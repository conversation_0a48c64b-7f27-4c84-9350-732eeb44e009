package com.jettech.jettong.cmdb.appcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 应用组件参数文件下发历史信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件参数文件下发历史信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.app_components.dto
 * @className AppComponentsEnvSendHistory
 * @date 2021-10-25
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsEnvSendHistoryPageQuery", description = "应用组件参数文件下发历史信息")
public class AppComponentsEnvSendHistoryPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @NotNull(message = "请填写应用组件id")
    private Long appComponentsId;

}
