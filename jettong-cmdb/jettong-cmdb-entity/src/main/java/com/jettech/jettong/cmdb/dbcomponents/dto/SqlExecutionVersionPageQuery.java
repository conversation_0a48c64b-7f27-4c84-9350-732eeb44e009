package com.jettech.jettong.cmdb.dbcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * SQL执行版本分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description SQL执行版本分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dtoPageQuery
 * @className SqlExecutionVersion
 * @date 2022-06-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "SqlExecutionVersionPageQuery", description = "SQL执行版本")
public class SqlExecutionVersionPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 外键，数据库组件id
     */
    @ApiModelProperty(value = "外键，数据库组件id")
    private Long dbComponentsId;

    /**
     * 是否成功
     */
    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    private String envKey;

    /**
     * sql类型：DDL、DML
     */
    @ApiModelProperty(value = "sql类型：DDL、DML")
    private String sqlType;


}
