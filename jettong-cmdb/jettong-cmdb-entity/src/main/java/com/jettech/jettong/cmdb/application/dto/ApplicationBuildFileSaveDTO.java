package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 构建参数文件信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 构建参数文件信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationBuildFileSaveDTO
 * @date 2022-01-05
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBuildFileSaveDTO", description = "构建参数文件信息")
public class ApplicationBuildFileSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "请输入服务应用id")
    private Long applicationId;

    /**
     * 构建参数文件上传路径
     */
    @ApiModelProperty(value = "构建参数文件上传路径")
    @NotNull(message = "构建参数文件上传路径")
    private String bulidFilePath;

    /**
     * 源文件名称
     */
    @ApiModelProperty(value = "源文件名称")
    @NotNull(message = "源文件名称")
    private String fileName;

}
