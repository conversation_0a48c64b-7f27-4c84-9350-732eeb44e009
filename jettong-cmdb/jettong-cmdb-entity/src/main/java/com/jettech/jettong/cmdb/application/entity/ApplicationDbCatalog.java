package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 服务应用db目录配置信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用db目录配置信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationDbCatalog
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_db_catalog")
@ApiModel(value = "ApplicationDbCatalog", description = "服务应用db目录配置信息")
public class ApplicationDbCatalog implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * db脚本目录
     */
    @ApiModelProperty(value = "db脚本目录")
    @Size(max = 255, message = "db脚本目录长度不能超过255")
    @TableField(value = "`sql_path`", condition = LIKE)
    @Excel(name = "db脚本目录")
    private String sqlPath;

    /**
     * include源目录
     */
    @ApiModelProperty(value = "include源目录")
    @Size(max = 255, message = "include源目录长度不能超过255")
    @TableField(value = "`include_path`", condition = LIKE)
    @Excel(name = "include源目录")
    private String includePath;

    /**
     * exclude源目录
     */
    @ApiModelProperty(value = "exclude源目录")
    @Size(max = 255, message = "exclude源目录长度不能超过255")
    @TableField(value = "`exclude_path`", condition = LIKE)
    @Excel(name = "exclude源目录")
    private String excludePath;

    /**
     * 工程/模块id
     */
    @ApiModelProperty(value = "工程/模块id")
    @TableField(value = "`module_id`")
    @Excel(name = "工程/模块id")
    @Echo(api = "applicationModuleServiceImpl", beanClass = ApplicationModule.class)
    private Long moduleId;

    @Builder
    public ApplicationDbCatalog(
            Long applicationId, String sqlPath, String includePath, String excludePath, Long moduleId)
    {
        this.applicationId = applicationId;
        this.sqlPath = sqlPath;
        this.includePath = includePath;
        this.excludePath = excludePath;
        this.moduleId = moduleId;
    }

}
