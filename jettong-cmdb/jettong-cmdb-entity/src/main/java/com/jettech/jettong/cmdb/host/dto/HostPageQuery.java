package com.jettech.jettong.cmdb.host.dto;

import com.jettech.jettong.cmdb.host.enumeration.HostCreateType;
import com.jettech.jettong.cmdb.host.enumeration.HostStatus;
import com.jettech.jettong.cmdb.host.enumeration.HostType;
import com.jettech.jettong.common.enumeration.OperatingSystemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 服务器信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.dto
 * @className Host
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "HostPageQuery", description = "服务器信息")
public class HostPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务器ip
     */
    @ApiModelProperty(value = "服务器ip")
    private String ip;

    /**
     * 服务器主机名
     */
    @ApiModelProperty(value = "服务器主机名")
    private String name;

    /**
     * 系统内核
     */
    @ApiModelProperty(value = "系统内核")
    private OperatingSystemType operatingSystemKernel;

    /**
     * 是否正常
     */
    @ApiModelProperty(value = "是否正常")
    private Boolean state;

    /**
     * 纳管状态
     */
    @ApiModelProperty(value = "状态")
    private HostStatus status;

    /**
     * 服务器类型
     */
    @ApiModelProperty(value = "服务器类型")
    private HostType type;

    /**
     * 创建方式
     */
    @ApiModelProperty(value = "创建方式")
    private HostCreateType createType;

    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    private Long orgId;

}
