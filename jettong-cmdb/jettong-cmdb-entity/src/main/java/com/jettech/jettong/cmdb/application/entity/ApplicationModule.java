package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationModulePackType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationModuleProductType;
import com.jettech.jettong.cmdb.application.enumeration.ApplicationModuleType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


/**
 * 服务应用工程/模块信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用工程/模块信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationModule
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_module")
@ApiModel(value = "ApplicationModule", description = "服务应用工程/模块信息")
@AllArgsConstructor
public class ApplicationModule extends SuperEntity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 库名称
     */
    @ApiModelProperty(value = "库名称")
    @Size(max = 50, message = "库名称长度不能超过50")
    @TableField(value = "`name`")
    @Excel(name = "库名称")
    private String name;

    /**
     * 库类型
     */
    @ApiModelProperty(value = "库类型")
    @TableField(value = "`type`")
    @Excel(name = "库类型",
            replace = {"单库单应用_ONLY_ONE", "单库多应用_MANY", "_null"})
    private ApplicationModuleType type;

    /**
     * 打包方式
     */
    @ApiModelProperty(value = "打包方式")
    @TableField(value = "`pack_type`")
    @Excel(name = "打包方式",
            replace = {"全量_FULL", "增量_INCREMENT", "_null"})
    private ApplicationModulePackType packType;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    @Size(max = 100, message = "工程名称长度不能超过100")
    @TableField(value = "`module_name`")
    @Excel(name = "工程名称")
    private String moduleName;

    /**
     * 工程相对库路径地址
     */
    @ApiModelProperty(value = "工程相对库路径地址")
    @Size(max = 200, message = "工程相对库路径地址长度不能超过200")
    @TableField(value = "`module_path`")
    @Excel(name = "工程相对库路径地址")
    private String modulePath;

    /**
     * 工程包名称
     */
    @ApiModelProperty(value = "工程包名称")
    @Size(max = 256, message = "工程包名称长度不能超过256")
    @TableField(value = "`product_name`")
    @Excel(name = "工程包名称")
    private String productName;

    /**
     * 工程包类型
     */
    @ApiModelProperty(value = "工程包类型")
    @TableField(value = "`product_type`")
    @Excel(name = "工程包类型", replace = {"全量_FULL", "增量_INCREMENT", "_null"})
    private ApplicationModuleProductType productType;

    /**
     * 工程描述
     */
    @ApiModelProperty(value = "工程描述")
    @Size(max = 256, message = "工程描述长度不能超过256")
    @TableField(value = "`description`")
    @Excel(name = "工程描述")
    private String description;


    @Builder
    public ApplicationModule(Long id, Long createdBy, LocalDateTime createTime,
            Long applicationId, String name, ApplicationModuleType type, ApplicationModulePackType packType,
            String moduleName,
            String modulePath, String productName, ApplicationModuleProductType productType, String description)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.applicationId = applicationId;
        this.name = name;
        this.type = type;
        this.packType = packType;
        this.moduleName = moduleName;
        this.modulePath = modulePath;
        this.productName = productName;
        this.productType = productType;
        this.description = description;
    }

}
