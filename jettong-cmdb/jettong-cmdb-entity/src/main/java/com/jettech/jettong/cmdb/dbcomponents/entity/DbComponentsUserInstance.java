package com.jettech.jettong.cmdb.dbcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 数据库组件用户实例信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件用户实例信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.entity
 * @className DbComponentsUserInstance
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_db_components_user_instance")
@ApiModel(value = "DbComponentsUserInstance", description = "数据库组件用户实例信息")
public class DbComponentsUserInstance implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    @NotNull(message = "请填写数据库组件id")
    @TableField(value = "`db_components_id`")
    @Excel(name = "数据库组件id")
    private Long dbComponentsId;

    /**
     * 数据库用户id
     */
    @ApiModelProperty(value = "数据库用户id")
    @NotNull(message = "请填写数据库用户id")
    @TableField(value = "`db_components_user_id`")
    @Excel(name = "数据库用户id")
    private Long dbComponentsUserId;

    /**
     * 数据库实例id
     */
    @ApiModelProperty(value = "数据库实例id")
    @NotNull(message = "请填写数据库实例id")
    @TableField(value = "`db_components_instance_id`")
    @Excel(name = "数据库实例id")
    private Long dbComponentsInstanceId;


    @Builder
    public DbComponentsUserInstance(
            Long dbComponentsId, Long dbComponentsUserId, Long dbComponentsInstanceId)
    {
        this.dbComponentsId = dbComponentsId;
        this.dbComponentsUserId = dbComponentsUserId;
        this.dbComponentsInstanceId = dbComponentsInstanceId;
    }

}
