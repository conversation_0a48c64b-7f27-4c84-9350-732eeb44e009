package com.jettech.jettong.cmdb.dbcomponents.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 数据库实例信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库实例信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.entity
 * @className DbComponentsInstance
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_db_components_instance")
@ApiModel(value = "DbComponentsInstance", description = "数据库实例信息")
public class DbComponentsInstance implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id")
    @NotNull(message = "数据库组件实例id不能为空")
    @Excel(name = "主键")
    private Long id;

    /**
     * 数据库组件id
     */
    @ApiModelProperty(value = "数据库组件id")
    @TableField(value = "`db_components_id`")
    @Excel(name = "数据库组件id")
    private Long dbComponentsId;

    /**
     * 实例名
     */
    @ApiModelProperty(value = "实例名")
    @Size(max = 50, message = "实例名长度不能超过50")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "实例名")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 500, message = "描述长度不能超过500")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;


    @Builder
    public DbComponentsInstance(Long id,
            Long dbComponentsId, String name, String description)
    {
        this.id = id;
        this.dbComponentsId = dbComponentsId;
        this.name = name;
        this.description = description;
    }

}
