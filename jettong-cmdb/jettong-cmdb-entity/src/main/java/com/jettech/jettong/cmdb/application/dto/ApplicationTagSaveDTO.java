package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 服务应用tag配置信息新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用tag配置信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dto
 * @className ApplicationTagSaveDTO
 * @date 2022-08-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationTagSaveDTO", description = "服务应用tag配置信息")
public class ApplicationTagSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    private Long applicationId;
    /**
     * tag_key
     */
    @ApiModelProperty(value = "tag_key")
    @Size(max = 50, message = "tag_key长度不能超过50")
    private String tagKey;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

}
