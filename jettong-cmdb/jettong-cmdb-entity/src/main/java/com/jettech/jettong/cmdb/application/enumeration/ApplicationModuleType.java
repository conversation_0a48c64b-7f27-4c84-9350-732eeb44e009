package com.jettech.jettong.cmdb.application.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 库类型枚举.ONLY_ONE:单库单应用;MANY:单库多应用
 *
 * <AUTHOR>
 * @version 1.0
 * @description 库类型枚举.ONLY_ONE:单库单应用;MANY:单库多应用
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.enumeration
 * @className ApplicationModuleType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ApplicationModuleType", description = "库类型枚举.ONLY_ONE:单库单应用;MANY:单库多应用")
public enum ApplicationModuleType implements BaseEnum
{

    /**
     * ONLY_ONE="单库单应用"
     */
    ONLY_ONE("单库单应用"),
    /**
     * MANY="单库多应用"
     */
    MANY("单库多应用"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static ApplicationModuleType match(String val, ApplicationModuleType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ApplicationModuleType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ApplicationModuleType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "ONLY_ONE,MANY", example = "ONLY_ONE")
    public String getCode()
    {
        return this.name();
    }

}
