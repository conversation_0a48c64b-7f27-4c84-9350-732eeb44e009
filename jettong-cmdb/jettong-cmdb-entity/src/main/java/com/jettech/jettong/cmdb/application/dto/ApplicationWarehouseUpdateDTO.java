package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 服务应用仓库引擎配置信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用仓库引擎配置信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.dto
 * @className ApplicationWarehouseUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationWarehouseUpdateDTO", description = "服务应用仓库引擎配置信息")
public class ApplicationWarehouseUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "请填写服务应用id")
    private Long applicationId;

    /**
     * 引擎类型Key
     */
    @ApiModelProperty(value = "引擎类型Key")
    @NotNull(message = "请填写引擎类型")
    private String engineInstanceKey;

    /**
     * 引擎id
     */
    @ApiModelProperty(value = "请选择仓库引擎")
    @NotNull(message = "请选择仓库引擎")
    private Long engineId;

    /**
     * 仓库地址
     */
    @ApiModelProperty(value = "仓库地址")
    private String codeRepPath;

    /**
     * 默认创建的分支Key
     */
    @ApiModelProperty(value = "默认创建的分支Key")
    private String branchKey;

    /**
     * 分支策略
     */
    @ApiModelProperty(value = "分支策略")
    private String branchStrategy;

}
