package com.jettech.jettong.cmdb.appcomponents.dto;

import com.jettech.jettong.cmdb.appcomponents.entity.AppComponentsExtend;
import com.jettech.jettong.cmdb.application.entity.ApplicationDeploy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 应用组件信息新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件信息新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.dto
 * @className AppComponentsSaveDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsSaveDTO", description = "应用组件信息")
public class AppComponentsSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 应用组件名称
     */
    @ApiModelProperty(value = "应用组件名称")
    @Size(max = 100, message = "应用组件名称长度不能超过100")
    private String name;

    /**
     * 应用组件描述
     */
    @ApiModelProperty(value = "应用组件描述")
    @Size(max = 200, message = "应用组件描述长度不能超过200")
    private String description;

    /**
     * 应用组件类型字典key
     */
    @ApiModelProperty(value = "应用组件类型字典key")
    @Size(max = 50, message = "应用组件类型字典key长度不能超过50")
    private String appComponentsTypeKey;

    /**
     * 服务器id集合
     */
    @ApiModelProperty(value = "服务器id集合")
    @NotNull(message = "请输入服务器id")
    @NotEmpty(message = "请输入服务器id")
    private List<Long> hostIds;

    /**
     * 服务端口
     */
    @ApiModelProperty(value = "服务端口")
    private Integer serverPort;

    /**
     * 脚本存放目录
     */
    @ApiModelProperty(value = "脚本存放目录")
    @Size(max = 1000, message = "脚本存放目录长度不能超过1000")
    private String scriptDir;

    /**
     * 部署目录
     */
    @ApiModelProperty(value = "部署目录")
    @Size(max = 1000, message = "部署目录长度不能超过1000")
    private String deployDir;

    /**
     * 发布目录
     */
    @ApiModelProperty(value = "发布目录")
    @Size(max = 1000, message = "发布目录长度不能超过1000")
    private String publishDir;

    /**
     * Python目录
     */
    @ApiModelProperty(value = "Python目录")
    @Size(max = 1000, message = "Python目录长度不能超过1000")
    private String pythonDir;

    /**
     * 控制台端口
     */
    @ApiModelProperty(value = "控制台端口")
    private Integer consolePort;

    /**
     * 应用权限用户
     */
    @ApiModelProperty(value = "应用权限用户")
    @Size(max = 100, message = "应用权限用户长度不能超过100")
    private String permissionUser;

    /**
     * 应用组件扩展信息
     */
    @ApiModelProperty(value = "应用组件扩展信息")
    private List<AppComponentsExtend> appComponentsExtends;

    /**
     * 服务应用部署配置信息
     */
    @ApiModelProperty(value = "服务应用部署配置信息")
    private List<ApplicationDeploy> applicationDeploys;
}
