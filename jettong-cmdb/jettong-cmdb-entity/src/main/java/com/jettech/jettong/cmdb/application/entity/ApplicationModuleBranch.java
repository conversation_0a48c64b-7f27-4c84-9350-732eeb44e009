package com.jettech.jettong.cmdb.application.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.Map;


/**
 * 服务应用工程分支信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用工程分支信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.application.entity
 * @className ApplicationModuleBranch
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@TableName("cmdb_application_module_branch")
@ApiModel(value = "ApplicationModuleBranch", description = "服务应用工程分支信息")
@AllArgsConstructor
public class ApplicationModuleBranch implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @NotNull(message = "请填写服务应用id")
    @TableField(value = "`application_id`")
    @Excel(name = "服务应用id")
    private Long applicationId;

    /**
     * 工程/模块id
     */
    @ApiModelProperty(value = "工程/模块id")
    @TableField(value = "`module_id`")
    @Echo(api = "applicationModuleServiceImpl", beanClass = ApplicationModule.class)
    @Excel(name = "工程/模块id")
    private Long moduleId;

    /**
     * 冗余字段，分支策略字典key
     */
    @ApiModelProperty(value = "冗余字段，分支策略字典key")
    @Size(max = 50, message = "冗余字段，分支策略字典key长度不能超过50")
    @TableField(value = "`branching_strategy_key`")
    @Excel(name = "冗余字段，分支策略字典key")
    private String branchingStrategyKey;

    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    @Size(max = 50, message = "分支字典key长度不能超过50")
    @TableField(value = "`branch_key`")
    @Excel(name = "分支字典key")
    private String branchKey;

    /**
     * 工程分支地址
     */
    @ApiModelProperty(value = "工程分支地址")
    @Size(max = 1000, message = "工程分支地址长度不能超过1000")
    @TableField(value = "`path`")
    @Excel(name = "工程分支地址")
    private String path;


    @Builder
    public ApplicationModuleBranch(
            Long applicationId, Long moduleId, String branchingStrategyKey, String branchKey, String path)
    {
        this.applicationId = applicationId;
        this.moduleId = moduleId;
        this.branchingStrategyKey = branchingStrategyKey;
        this.branchKey = branchKey;
        this.path = path;
    }

}
