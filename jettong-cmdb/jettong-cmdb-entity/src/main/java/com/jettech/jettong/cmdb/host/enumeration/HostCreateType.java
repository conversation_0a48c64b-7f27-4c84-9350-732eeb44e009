package com.jettech.jettong.cmdb.host.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 服务器创建方式枚举。SALT:salt;MANUAL_ENTRY:人工录入;PLATFORM_OPEN:平台开通
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器创建方式枚举。SALT:salt;MANUAL_ENTRY:人工录入;PLATFORM_OPEN:平台开通
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.enumeration
 * @className HostCreateType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HostCreateType", description = "服务器创建方式枚举。SALT:salt;MANUAL_ENTRY:人工录入;PLATFORM_OPEN:平台开通")
public enum HostCreateType implements BaseEnum
{

    /**
     * SALT="salt"
     */
    SALT("SaltStack"),
    /**
     * MANUAL_ENTRY="人工录入"
     */
    MANUAL_ENTRY("人工录入"),
    /**
     * PLATFORM_OPEN="平台开通"
     */
    PLATFORM_OPEN("平台开通")
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static HostCreateType match(String val, HostCreateType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static HostCreateType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(HostCreateType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "服务器创建方式编码", allowableValues = "SALT,MANUAL_ENTRY,PLATFORM_OPEN", example = "SALT")
    public String getCode()
    {
        return this.name();
    }

}
