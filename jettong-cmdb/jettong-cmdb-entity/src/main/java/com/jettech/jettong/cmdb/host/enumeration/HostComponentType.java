package com.jettech.jettong.cmdb.host.enumeration;

import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;


/**
 * 组件类型枚举。APP:应用组件;DB:数据库组件
 *
 * <AUTHOR>
 * @version 1.0
 * @description 组件类型枚举。APP:应用组件;DB:数据库组件
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.host.enumeration
 * @className HostComponentType
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "HostComponentType", description = "组件类型枚举。APP:应用组件;DB:数据库组件")
public enum HostComponentType implements BaseEnum
{

    /**
     * APP="应用组件"
     */
    APP("应用组件"),
    /**
     * DB="数据库组件"
     */
    DB("数据库组件"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static HostComponentType match(String val, HostComponentType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static HostComponentType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(HostComponentType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "组件类型编码", allowableValues = "APP,DB", example = "APP")
    public String getCode()
    {
        return this.name();
    }

}
