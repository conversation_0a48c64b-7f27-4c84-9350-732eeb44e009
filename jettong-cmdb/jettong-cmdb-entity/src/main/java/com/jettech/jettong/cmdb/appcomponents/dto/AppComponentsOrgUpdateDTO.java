package com.jettech.jettong.cmdb.appcomponents.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 应用组件机构授权信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 应用组件机构授权信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.dto
 * @className AppComponentsOrgUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "AppComponentsOrgUpdateDTO", description = "应用组件机构授权信息")
public class AppComponentsOrgUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;


    /**
     * 应用组件id
     */
    @ApiModelProperty(value = "应用组件id")
    @NotNull(message = "请填写应用组件id")
    @NotEmpty(message = "请填写应用组件id")
    private List<Long> appComponentsIds;

    /**
     * 授权机构信息
     */
    @ApiModelProperty(value = "授权机构id集合")
    private List<Long> orgIds;

}
