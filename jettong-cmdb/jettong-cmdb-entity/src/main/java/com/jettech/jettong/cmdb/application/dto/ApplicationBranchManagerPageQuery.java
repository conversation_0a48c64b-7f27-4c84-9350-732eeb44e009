package com.jettech.jettong.cmdb.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 分支扫描结果表分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 分支扫描结果表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.dtoPageQuery
 * @className ApplicationBranchManager
 * @date 2021-12-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ApplicationBranchManagerPageQuery", description = "分支扫描结果表")
public class ApplicationBranchManagerPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 服务应用ID
     */
    @ApiModelProperty(value = "服务应用ID")
    private Long applicationId;
    /**
     * 分支字典key
     */
    @ApiModelProperty(value = "分支字典key")
    private String branchKey;
    /**
     * 流水线ID
     */
    @ApiModelProperty(value = "流水线ID")
    private Long pipelineId;
    /**
     * 构建ID
     */
    @ApiModelProperty(value = "构建ID")
    private String ciBuildId;
    /**
     * 增量行覆盖率
     */
    @ApiModelProperty(value = "增量行覆盖率")
    private String increteLineCoverRate;
    /**
     * 全量行覆盖率
     */
    @ApiModelProperty(value = "全量行覆盖率")
    private String allLineCoverRate;
    /**
     * bug
     */
    @ApiModelProperty(value = "bug")
    private String bugs;
    /**
     * 异味
     */
    @ApiModelProperty(value = "异味")
    private String codeSmells;
    /**
     * 漏洞
     */
    @ApiModelProperty(value = "漏洞")
    private String vulnerabilities;
    /**
     * 代码覆盖率
     */
    @ApiModelProperty(value = "代码覆盖率")
    private String debt;
    /**
     * 单测是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "单测是否通过 1-通过；2不通过")
    private Integer isUnitPassed;
    /**
     * 静态扫描是否通过 1-通过；2不通过
     */
    @ApiModelProperty(value = "静态扫描是否通过 1-通过；2不通过")
    private Integer isStaticScanPassed;
    /**
     * 执行人
     */
    @ApiModelProperty(value = "执行人")
    private String executor;
    /**
     * 单测通过率
     */
    @ApiModelProperty(value = "单测通过率")
    private String unitPassRate;
    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    private Long orgId;

}
