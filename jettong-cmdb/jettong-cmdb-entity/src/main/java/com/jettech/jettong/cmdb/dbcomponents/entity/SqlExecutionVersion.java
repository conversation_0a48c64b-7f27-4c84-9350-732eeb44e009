package com.jettech.jettong.cmdb.dbcomponents.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;


/**
 * SQL执行版本实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description SQL执行版本实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.entity
 * @className SqlExecutionVersion
 * @date 2022-06-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sql_execution_version")
@ApiModel(value = "SqlExecutionVersion", description = "SQL执行版本")
@AllArgsConstructor
public class SqlExecutionVersion implements Serializable, EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableId(value = "`id`", type = IdType.INPUT)
    @ApiModelProperty(value = "主键")
    @NotNull(message = "id不能为空", groups = SuperEntity.Update.class)
    protected Long id;
    @ApiModelProperty(value = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;
    @ApiModelProperty(value = "最后修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    protected LocalDateTime updateTime;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 外键，数据库组件id
     */
    @ApiModelProperty(value = "外键，数据库组件id")
    @TableField(value = "`db_components_id`")
    private Long dbComponentsId;
    /**
     * 内部版本号，系统内使用
     */
    @ApiModelProperty(value = "内部版本号，系统内使用")
    @Size(max = 64, message = "内部版本号，系统内使用长度不能超过64")
    @TableField(value = "`version_package`")
    private String versionPackage;
    /**
     * AB显示版本号
     */
    @ApiModelProperty(value = "AB显示版本号")
    @Size(max = 128, message = "AB显示版本号长度不能超过128")
    @TableField(value = "`version_display`")
    private String versionDisplay;
    /**
     * 执行顺序
     */
    @ApiModelProperty(value = "执行顺序")
    @TableField(value = "`install_rank`")
    private Integer installRank;
    /**
     * 执行sql文件路径
     */
    @ApiModelProperty(value = "执行sql文件路径")
    @Size(max = 4000, message = "执行sql文件路径长度不能超过4000")
    @TableField(value = "`sql_path`")
    private String sqlPath;
    /**
     * 执行时间
     */
    @ApiModelProperty(value = "执行时间")
    @TableField(value = "`execution_time`")
    private LocalDateTime executionTime;
    /**
     * 是否成功 0：否、1：是
     */
    @ApiModelProperty(value = "是否成功")
    @TableField(value = "`success`")
    private Boolean success;
    /**
     * 服务应用id
     */
    @ApiModelProperty(value = "服务应用id")
    @TableField(value = "`application_id`")
    private Long applicationId;
    /**
     * 环境字典key
     */
    @ApiModelProperty(value = "环境字典key")
    @Size(max = 50, message = "环境字典key长度不能超过50")
    @TableField(value = "`env_key`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENVIRONMENT)
    private String envKey;
    /**
     * sql类型：DDL、DML
     */
    @ApiModelProperty(value = "sql类型：DDL、DML")
    @Size(max = 32, message = "sql类型：DDL、DML长度不能超过32")
    @TableField(value = "`sql_type`")
    private String sqlType;
    /**
     * sql描述
     */
    @ApiModelProperty(value = "sql描述")
    @Size(max = 4000, message = "sql描述长度不能超过4000")
    @TableField(value = "`description`")
    private String description;
    /**
     * 创建机构id
     */
    @ApiModelProperty(value = "创建机构id")
    @TableField(value = "`org_id`")
    private Long orgId;

    @Builder
    public SqlExecutionVersion(Long id, LocalDateTime createTime, LocalDateTime updateTime,
            Long dbComponentsId, String versionPackage, String versionDisplay, Integer installRank, String sqlPath,
            LocalDateTime executionTime, Boolean success, Long applicationId, String envKey, String sqlType,
            String description, Long orgId)
    {
        this.id = id;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.dbComponentsId = dbComponentsId;
        this.versionPackage = versionPackage;
        this.versionDisplay = versionDisplay;
        this.installRank = installRank;
        this.sqlPath = sqlPath;
        this.executionTime = executionTime;
        this.success = success;
        this.applicationId = applicationId;
        this.envKey = envKey;
        this.sqlType = sqlType;
        this.description = description;
        this.orgId = orgId;
    }

}
