package com.jettech.jettong.cmdb.dbcomponents.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.cmdb.application.entity.ApplicationDb;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponentsExtend;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponentsInstance;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponentsUser;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponentsUserInstance;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 数据库组件信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据库组件信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.cmdb.db_components.dto
 * @className DbComponentsUpdateDTO
 * @date 2021-10-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DbComponentsUpdateDTO", description = "数据库组件信息")
public class DbComponentsUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 100, message = "名称长度不能超过100")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    /**
     * 安装目录
     */
    @ApiModelProperty(value = "安装目录")
    @Size(max = 2000, message = "安装目录长度不能超过2000")
    private String installationPath;

    /**
     * 数据库组件扩展信息
     */
    @ApiModelProperty(value = "数据库组件扩展信息")
    private List<DbComponentsExtend> dbComponentsExtends;

    /**
     * 数据库组件用户配置信息
     */
    @ApiModelProperty(value = "数据库组件用户配置信息")
    private List<DbComponentsUser> dbComponentsUsers;

    /**
     * 数据库组件实例配置信息
     */
    @ApiModelProperty(value = "数据库组件实例配置信息")
    private List<DbComponentsInstance> dbComponentsInstances;

    /**
     * 数据库组件实例用户关系配置信息
     */
    @ApiModelProperty(value = "数据库组件实例用户关系配置信息")
    private List<DbComponentsUserInstance> dbComponentsUserInstances;

    /**
     * 服务应用数据库配置信息
     */
    @ApiModelProperty(value = "服务应用数据库配置信息")
    private List<ApplicationDb> applicationDbs;
}
