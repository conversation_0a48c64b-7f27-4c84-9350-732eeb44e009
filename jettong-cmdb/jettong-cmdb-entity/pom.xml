<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-cmdb</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jettong-cmdb-entity</artifactId>
    <description>资源中心实体模块</description>

    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-base-entity</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.jettech.jettong</groupId>-->
<!--            <artifactId>jettong-product-entity</artifactId>-->
<!--            <version>${jettong-project.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-common</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
        </dependency>
    </dependencies>

</project>