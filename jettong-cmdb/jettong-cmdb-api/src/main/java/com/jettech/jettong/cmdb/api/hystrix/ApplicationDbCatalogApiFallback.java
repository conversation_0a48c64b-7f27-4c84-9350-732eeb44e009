package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.jettong.cmdb.api.ApplicationDbCatalogApi;
import com.jettech.jettong.cmdb.application.entity.ApplicationDbCatalog;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 服务应用DB目录信息对外API熔断器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用DB目录信息对外API熔断器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationDbCatalogApiFallback
 * @date 2021/11/2 10:39
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ApplicationDbCatalogApiFallback implements ApplicationDbCatalogApi
{
    @Override
    public List<ApplicationDbCatalog> findByApplicationIdAndModuleName(Long applicationId, String moduleName)
    {
        return Collections.emptyList();
    }
}
