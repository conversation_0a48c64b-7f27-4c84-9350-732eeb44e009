package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.jettong.cmdb.api.HostApi;
import com.jettech.jettong.cmdb.host.entity.Host;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * 服务应用相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用相关API熔断
 * @projectName jettong-jenkins-starter
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationApiFallback
 * @date 2021/10/27 18:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class HostApiFallback implements HostApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public Host findById(Long id)
    {
        return null;
    }


}
