package com.jettech.jettong.cmdb.api;

import com.jettech.basic.base.R;
import com.jettech.jettong.cmdb.api.hystrix.HostJobApiFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className HostJobApi
 * @date 2021/10/30 18:22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = HostJobApiFallback.class
        , path = "/cmdb/hostJob")
public interface HostJobApi
{

    /**
     * 根据定时任务名称刷新服务器状态
     *
     * @param taskJobName 定时任务名称
     * @return R<Boolean> 刷新结果
     * <AUTHOR>
     * @date 2021/10/30 18:26
     * @update zxy 2021/10/30 18:26
     * @since 1.0
     */
    @PutMapping(value = "/execTaskHostRefreshJob/{taskJobName}")
    R<Boolean> execTaskHostRefreshJob(@PathVariable("taskJobName") String taskJobName);

    /**
     * 同步服务器
     *
     * @return R<Boolean> 同步结果
     * <AUTHOR>
     * @date 2021/10/30 18:27
     * @update zxy 2021/10/30 18:27
     * @since 1.0
     */
    @PutMapping(value = "/execTaskHostSyncJob")
    R<Boolean> execTaskHostSyncJob();
}
