package com.jettech.jettong.cmdb.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.cmdb.api.hystrix.HostApiFallback;
import com.jettech.jettong.cmdb.host.entity.Host;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 服务器相关api
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务器相关api
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className HostApi
 * @date 2021/10/30 16:44
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = HostApiFallback.class
        , path = "/cmdb/host")
public interface HostApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据主键id查询服务器信息
     *
     * @param id 主键id
     * @return R<Host> 服务器信息
     * <AUTHOR>
     * @date 2021/10/30 16:53
     * @update zxy 2021/10/30 16:53
     * @since 1.0
     */
    @GetMapping("/echo/{id}")
    Host findById(@PathVariable("id") Long id);

}
