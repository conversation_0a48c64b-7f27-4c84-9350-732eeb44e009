package com.jettech.jettong.cmdb.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.cmdb.api.hystrix.ApplicationApiFallback;
import com.jettech.jettong.cmdb.application.dto.ApplicationQuery;
import com.jettech.jettong.cmdb.application.entity.Application;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 服务应用相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用相关API
 * @projectName jettong-jenkins-starter
 * @package com.jettech.jettong.cmdb.api
 * @className ApplicationApi
 * @date 2021/10/27 18:27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = ApplicationApiFallback.class
        , path = "/cmdb/application")
public interface ApplicationApi extends LoadService
{
    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return Map<Serializable, Object> 待回显参数
     * <AUTHOR>
     * @date 2021/9/13 10:11
     * @update zxy 2021/9/13 10:11
     * @since 1.0
     */
    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据条件查询服务应用信息
     *
     * @param applicationQuery 查询条件
     * @return List<Application> 服务应用信息
     * <AUTHOR>
     * @date 2021/11/6 11:30
     * @update zxy 2021/11/6 11:30
     * @since 1.0
     */
    @PostMapping("/echo/findByQuery")
    List<Application> findByQuery(@RequestBody ApplicationQuery applicationQuery);

    /**
     * 根据id查询服务应用信息
     *
     * @param id 服务应用id
     * @return Application 服务应用信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectApplicationById")
    Application selectApplicationById(@RequestParam(value = "id") Long id);

}
