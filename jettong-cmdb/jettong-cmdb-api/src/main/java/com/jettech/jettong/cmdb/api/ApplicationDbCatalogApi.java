package com.jettech.jettong.cmdb.api;

import com.jettech.jettong.cmdb.api.hystrix.ApplicationDbCatalogApiFallback;
import com.jettech.jettong.cmdb.application.entity.ApplicationDbCatalog;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * 服务应用DB目录信息对外API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用DB目录信息对外API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className ApplicationDbCatalogApi
 * @date 2021/11/2 10:37
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = ApplicationDbCatalogApiFallback.class
        , path = "/cmdb/applicationDbCatalog")
public interface ApplicationDbCatalogApi
{
    /**
     * 根据服务应用id和工程名称获取工程信息
     *
     * @param applicationId 服务应用id
     * @param moduleName 工程名称
     * @return ApplicationModule 服务应用工程配置信息
     * <AUTHOR>
     * @date 2021/11/2 10:45
     * @update zxy 2021/11/2 10:45
     * @since 1.0
     */
    @GetMapping("/echo/findByApplicationIdAndModuleName/{applicationId}/{moduleName}")
    List<ApplicationDbCatalog> findByApplicationIdAndModuleName(@PathVariable("applicationId") Long applicationId,
            @PathVariable("moduleName") String moduleName);

}
