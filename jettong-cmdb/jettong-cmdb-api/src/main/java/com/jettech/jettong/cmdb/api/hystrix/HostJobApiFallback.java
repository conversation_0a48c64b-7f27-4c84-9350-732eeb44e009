package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.basic.base.R;
import com.jettech.jettong.cmdb.api.HostJobApi;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className HostJobApiFallback
 * @date 2021/10/30 18:23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class HostJobApiFallback implements HostJobApi
{
    @Override
    public R<Boolean> execTaskHostRefreshJob(String taskName)
    {
        return R.timeout();
    }

    @Override
    public R<Boolean> execTaskHostSyncJob()
    {
        return R.timeout();
    }
}
