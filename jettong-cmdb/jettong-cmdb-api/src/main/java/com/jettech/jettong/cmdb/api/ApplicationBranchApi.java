package com.jettech.jettong.cmdb.api;

import com.jettech.basic.base.R;
import com.jettech.jettong.cmdb.api.hystrix.ApplicationBranchApiFallback;
import com.jettech.jettong.cmdb.application.entity.ApplicationBranch;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 服务应用分支信息对外Api
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用分支信息对外Api
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className ApplicationBranchApi
 * @date 2021/11/4 15:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = ApplicationBranchApiFallback.class
        , path = "/cmdb/applicationBranch")
public interface ApplicationBranchApi
{
    /**
     * 根据服务应用id和分支code查询服务应用分支信息
     *
     * @param applicationId 服务应用id
     * @param branchCode 分支code
     * @return ApplicationBranch 服务应用分支信息
     * <AUTHOR>
     * @date 2021/11/4 15:29
     * @update zxy 2021/11/4 15:29
     * @since 1.0
     */
    @GetMapping("/echo/findBranchByApplicationIdAndBranchCode/{applicationId}/{branchCode}")
    ApplicationBranch findBranchByApplicationIdAndBranchCode(@PathVariable("applicationId") Long applicationId,
            @PathVariable("branchCode") String branchCode);

    /**
     * 修改服务应用分支信息
     *
     * @param applicationBranch 服务应用分支信息
     * @return R 修改结果
     * <AUTHOR>
     * @date 2022/5/23 17:55
     * @update zxy 2022/5/23 17:55
     * @since 1.0
     */
    @PutMapping("/applicationBranch/echo/updateSubmitBuild")
    R updateSubmitBuild(@RequestBody ApplicationBranch applicationBranch);
}
