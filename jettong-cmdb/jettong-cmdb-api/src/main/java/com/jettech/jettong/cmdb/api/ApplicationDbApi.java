package com.jettech.jettong.cmdb.api;

import com.jettech.jettong.cmdb.api.hystrix.ApplicationDbApiFallback;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponents;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 服务应用数据库配置对外Api
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用数据库配置对外Api
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className ApplicationDbApi
 * @date 2021/11/4 17:20
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = ApplicationDbApiFallback.class
        , path = "/cmdb/applicationDb")
public interface ApplicationDbApi
{
    /**
     * 根据服务应用id和环境key查询数据库组件信息
     *
     * @param applicationId 服务应用 id
     * @param envKeys 环境key
     * @return List<DbComponents> 数据库组件信息
     * <AUTHOR>
     * @date 2022/5/23 17:55
     * @update zxy 2022/5/23 17:55
     * @since 1.0
     */
    @GetMapping("/echo/findDbComponentsByApplicationIdAndEnvKeys/{applicationId}")
    List<DbComponents> findDbComponentsByApplicationIdAndEnvKeys(
            @PathVariable("applicationId") Long applicationId, @RequestParam("envKeys")
            List<String> envKeys);

}
