package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.basic.base.R;
import com.jettech.jettong.cmdb.api.ApplicationBranchApi;
import com.jettech.jettong.cmdb.application.entity.ApplicationBranch;
import org.springframework.stereotype.Component;

/**
 * 服务应用分支信息对外Api熔断器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用分支信息对外Api熔断器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationBranchApiFallback
 * @date 2021/11/4 15:30
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ApplicationBranchApiFallback implements ApplicationBranchApi
{
    @Override
    public ApplicationBranch findBranchByApplicationIdAndBranchCode(Long applicationId, String branchCode)
    {
        return null;
    }

    @Override
    public R updateSubmitBuild(ApplicationBranch applicationBranch)
    {
        return R.validFail("更新分支提交即构建字段失败");
    }
}
