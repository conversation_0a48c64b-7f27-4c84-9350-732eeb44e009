package com.jettech.jettong.cmdb.api;

import com.jettech.jettong.base.entity.sys.engine.Engine;
import com.jettech.jettong.cmdb.api.hystrix.ApplicationEngineApiFallback;
import com.jettech.jettong.cmdb.application.vo.ApplicationEngineQuery;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 服务应用引擎信息对外API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用引擎信息对外API
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api
 * @className ApplicationEngineApi
 * @date 2021/11/1 21:32
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.cmdb-server:jettong-cmdb-server}", fallback = ApplicationEngineApiFallback.class
        , path = "/cmdb/applicationEngine")
public interface ApplicationEngineApi
{

    /**
     * 根据查询条件查询服务应用引擎信息
     *
     * @param applicationEngineQuery 查询条件
     * @return List<Engine> 引擎信息
     * <AUTHOR>
     * @date 2021/11/2 9:39
     * @update zxy 2021/11/2 9:39
     * @since 1.0
     */
    @PostMapping("/echo/findEngineByQuery")
    List<Engine> findEngineByQueryVo(@RequestBody ApplicationEngineQuery applicationEngineQuery);
}
