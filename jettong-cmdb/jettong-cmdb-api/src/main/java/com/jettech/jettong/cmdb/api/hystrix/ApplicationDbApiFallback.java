package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.jettong.cmdb.api.ApplicationDbApi;
import com.jettech.jettong.cmdb.dbcomponents.entity.DbComponents;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 服务应用数据库配置对外Api熔断器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用数据库配置对外Api熔断器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationDbApiFallback
 * @date 2021/11/4 17:22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ApplicationDbApiFallback implements ApplicationDbApi
{

    @Override
    public List<DbComponents> findDbComponentsByApplicationIdAndEnvKeys(Long applicationId, List<String> envKeys)
    {
        return Collections.emptyList();
    }
}
