package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.jettong.cmdb.api.ApplicationModuleApi;
import com.jettech.jettong.cmdb.application.entity.ApplicationModule;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 服务应用工程信息对外API熔断器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 服务应用工程信息对外API熔断器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationModuleApiFallback
 * @date 2021/11/2 10:40
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ApplicationModuleApiFallback implements ApplicationModuleApi
{
    @Override
    public ApplicationModule findByApplicationIdAndModuleName(Long applicationId, String moduleName)
    {
        return null;
    }

    @Override
    public List<ApplicationModule> findByApplicationId(Long applicationId)
    {
        return Collections.emptyList();
    }
}
