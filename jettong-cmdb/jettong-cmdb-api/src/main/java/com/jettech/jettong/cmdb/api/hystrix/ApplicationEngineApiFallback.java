package com.jettech.jettong.cmdb.api.hystrix;

import com.jettech.jettong.base.entity.sys.engine.Engine;
import com.jettech.jettong.cmdb.api.ApplicationEngineApi;
import com.jettech.jettong.cmdb.application.vo.ApplicationEngineQuery;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.cmdb.api.hystrix
 * @className ApplicationEngineApiFallback
 * @date 2021/11/1 21:33
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ApplicationEngineApiFallback implements ApplicationEngineApi
{
    @Override
    public List<Engine> findEngineByQueryVo(ApplicationEngineQuery applicationEngineQuery)
    {
        return Collections.emptyList();
    }
}
