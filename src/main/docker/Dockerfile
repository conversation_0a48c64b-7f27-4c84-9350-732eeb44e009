FROM harbor.jettong.com/iso/centos:7.5-minimalism-jdk
MAINTAINER <EMAIL>
ENV SERVER_PORT=8762 \
PACKAGENAME=jettong.jar \
NACOS_IP=************* \
NACOS_PORT=8848 \
NACOS_NS=839de5f0-8e15-4d0a-b97e-3da14aed4a84 \
NACOS_USER=nacos \
NACOS_PWD=nacos \
LOG_PATH=/opt/JettongEnterpriseCloud_SIT/log
RUN yum -y install convmv \
 && yum -y install git \
 && yum -y install httpd httpd-devel subversion \
 && yum clean all
COPY jettong*.jar /home/
COPY dockerRun.sh /home/
EXPOSE ${SERVER_PORT}
VOLUME ${LOG_PATH}
ENTRYPOINT cd /home && sh dockerRun.sh