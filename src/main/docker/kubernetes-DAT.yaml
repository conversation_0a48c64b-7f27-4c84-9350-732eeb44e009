# ------------------- 敏捷中心服务 Deployment ------------------- #

apiVersion: apps/v1beta2
kind: Deployment
metadata:
  labels:
    app: autostudio-pipeline-server
  name: autostudio-pipeline-server
  # 集群环境的空间名称
  namespace: autostudio-dat
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: autostudio-pipeline-server
  template:
    metadata:
      labels:
        app: autostudio-pipeline-server
    spec:
      containers:
        - name: autostudio-pipeline-server
          imagePullPolicy: Always
          # 镜像仓库地址
          image: *************/ms-autostudio/autostudio-pipelineserver:dat
          ports:
            - containerPort: 8772
              protocol: TCP
          env:
            # EUREKA_SERVER 服务注册中心的部署的实际IP或者是集群内部IP
            - name: EUREKA_SERVER_IP
              value: autostudio-eureka-server-svc
            # EUREKA_SERVER 服务注册中心的部署的实际端口或内部端口
            - name: EUREKA_SERVER_PORT
              value: "8761"
            # 日志文件产生地址,可以不用修改若修改那么对应的volumeMounts下的配置都需要修改
            - name: LOG_PATH
              value: /home/<USER>
            # 敏捷中心服务的对外暴露端口
            - name: SERVER_PORT
              value: "8772"
            # 敏捷中心服务的对外暴露端口
            - name: UPLOAD_PATH
              value: /home/<USER>/
            # 敏捷中心服务的对外暴露端口
            - name: SERVER_IP
              value: "127.0.0.1"
            # 敏捷中心服务的对外暴露端口
            - name: EUREKA_SECURITY_NAME
              value: admin
            # 敏捷中心服务的对外暴露端口
            - name: EUREKA_SECURITY_PWD
              value: admin1234567
            # 敏捷中心服务的对外暴露端口
            - name: ENV_LEVEL
              value: dat
            # 敏捷中心服务的对外暴露端口
            - name: JDBC_NAME
              value: autochain_MicroService
            # 敏捷中心服务的对外暴露端口
            - name: JDBC_IP
              value: "*************"
            - name: JDBC_PORT
              value: "30771"
            - name: JDBC_DATABASE
              value: autostudio_dat
            # 敏捷中心服务的对外暴露端口
            - name: JDBC_USER
              value: devops
            # 敏捷中心服务的对外暴露端口
            - name: JDBC_PWD
              value: devops_dat
            # 敏捷中心服务的对外暴露端口
            - name: RABBITMQ_HOST
              value: rabbitmq-dat
            # 敏捷中心服务的对外暴露端口
            - name: RABBITMQ_PORT
              value: "5672"
            # 敏捷中心服务的对外暴露端口
            - name: RABBITMQ_USER
              value: user
            # 敏捷中心服务的对外暴露端口
            - name: RABBITMQ_PWD
              value: admin
            # 敏捷中心服务的对外暴露端口
            - name: REDIS_HOST
              value: redis-dat-master
            # 敏捷中心服务的对外暴露端口
            - name: REDIS_PORT
              value: "6379"
            # 敏捷中心服务的对外暴露端口
            - name: SERVERHTTP_URL
              value: "*************"
            # 敏捷中心服务的对外暴露端口
            - name: SERVERHTTP_PORT
              value: "30505"
          volumeMounts:
            # 容器内部日志文件目录地址
            - mountPath: /home/<USER>
              name: microservice-log
            - mountPath: /home/<USER>/
              name: pipeline-data
      # NFS 日志资源文件和用户上传文件的共享目录的服务器和地址
      volumes:
        - name: pipeline-data
          nfs:
            path: /home/<USER>/autostudio/DEV/upload/
            server: *************
        - name: microservice-log
          nfs:
            path: /home/<USER>/autostudio/DEV/logs/
            server: *************

---
# ------------------- 版本管理 Service ------------------- #

apiVersion: v1
kind: Service
metadata:
  labels:
    app: autostudio-pipeline-server
  name: autostudio-pipeline-server-svc
  # 集群环境的空间名称
  namespace: autostudio-dat
spec:
  ports:
    - port: 8772
      protocol: TCP
      targetPort: 8772
      nodePort: 30505
  selector:
    app: autostudio-pipeline-server
  type: NodePort
status:
  loadBalancer: { }