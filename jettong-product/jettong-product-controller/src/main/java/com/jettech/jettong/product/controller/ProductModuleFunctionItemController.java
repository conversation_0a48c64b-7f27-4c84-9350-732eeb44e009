package com.jettech.jettong.product.controller;

import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.common.constant.TaskTypeCodeConstants;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductModuleFunctionItem;
import com.jettech.jettong.product.dto.ProductModuleFunctionItemSaveDTO;
import com.jettech.jettong.product.dto.ProductModuleFunctionItemUpdateDTO;
import com.jettech.jettong.product.dto.ProductModuleFunctionItemPageQuery;
import com.jettech.jettong.product.service.ProductInfoService;
import com.jettech.jettong.product.service.ProductModuleFunctionItemService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.R;
import com.jettech.jettong.product.service.ProductModuleFunctionService;
import com.jettech.jettong.testm.api.TestProductCaseApi;
import com.jettech.jettong.testm.api.TestRequirementFunctionPointsApi;
import com.jettech.jettong.testm.api.TestTaskCaseApi;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.entity.TestProductCase;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.entity.TestTaskCase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jettech.basic.annotation.security.PreAuth;


/**
 * 控制器
 * <AUTHOR>
 * @version 1.0
 * @description 控制器
 * @projectName jettong
 * @package com.jettech.jettong.product.controller
 * @className ProductModuleFunctionItemController
 * @date 2025-08-22
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/productModuleFunctionItem")
@Api(value = "ProductModuleFunctionItem", tags = "")
@PreAuth(replace = "product:productModuleFunctionItem:")
@RequiredArgsConstructor
public class ProductModuleFunctionItemController extends SuperController<ProductModuleFunctionItemService,Long , ProductModuleFunctionItem, ProductModuleFunctionItemPageQuery, ProductModuleFunctionItemSaveDTO, ProductModuleFunctionItemUpdateDTO>
{
    private final ProductModuleFunctionService productModuleFunctionService;
    private final ProductInfoService productInfoService;
    private final TestProductCaseApi testProductCaseApi;
    private final TestTaskCaseApi testTaskCaseApi;
    private final TestRequirementFunctionPointsApi testRequirementFunctionPointsApi;

    @PostMapping("/addProductModuleFunctionItem")
    @ApiOperation(value = "新增工作项与交易关联信息表")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> addProductModuleFunctionItem(
            @RequestBody ProductModuleFunctionItemSaveDTO dto) {

        Long bizId = dto.getBizId();
        String bizType = dto.getBizType();
        if (bizId == null || StringUtils.isBlank(bizType)) {
            return R.fail("业务ID或类型不能为空");
        }
        // 参数校验
        if (dto.getFunctionIds() == null || dto.getFunctionIds().isEmpty()) {
            baseService.remove(Wraps.<ProductModuleFunctionItem>lbQ()
                    .in(ProductModuleFunctionItem::getBizType, bizType)
                    .eq(ProductModuleFunctionItem::getBizId, bizId));
            return R.fail("功能ID列表不能为空");
        }


        // 查询现有关联记录
        List<Long> existingIds = baseService.listObjs(
                Wraps.<ProductModuleFunctionItem>lbQ()
                        .select(ProductModuleFunctionItem::getModuleFunctionId)
                        .eq(ProductModuleFunctionItem::getBizId, bizId)
                        .eq(ProductModuleFunctionItem::getBizType, bizType),
                Object::toString
        ).stream().map(Long::valueOf).collect(Collectors.toList());

        List<Long> newIds = dto.getFunctionIds();
        if(dto.getAddType() != null && "append".equals(dto.getAddType())){


        }else{
            // 计算需删除的旧记录
            List<Long> idsToRemove = existingIds.stream()
                    .filter(id -> !newIds.contains(id))
                    .collect(Collectors.toList());
            if (!idsToRemove.isEmpty()) {
                baseService.remove(Wraps.<ProductModuleFunctionItem>lbQ()
                        .in(ProductModuleFunctionItem::getModuleFunctionId, idsToRemove)
                        .eq(ProductModuleFunctionItem::getBizId, bizId));
            }
        }

        // 计算需新增的记录
        List<ProductModuleFunctionItem> itemsToInsert = newIds.stream()
                .filter(id -> !existingIds.contains(id))
                .map(id -> ProductModuleFunctionItem.builder()
                        .bizType(bizType).bizId(bizId).moduleFunctionId(id).build())
                .collect(Collectors.toList());
        if (!itemsToInsert.isEmpty()) {
            // 分批次插入
            baseService.saveBatch(itemsToInsert, 500);
        }
        return R.success(true);
    }
    @GetMapping("/getProductModuleFunctionsByBizId/{bizId}")
    @ApiOperation(value = "根据业务id获取交易信息")
    public List<ProductModuleFunction> getProductModuleFunctionsByBizId(@PathVariable("bizId")Long bizId){
        List<Long> functionIds =
                baseService.list(Wraps.<ProductModuleFunctionItem>lbQ().eq(ProductModuleFunctionItem::getBizId, bizId))
                        .stream().map(ProductModuleFunctionItem::getModuleFunctionId).collect(Collectors.toList());
        List<ProductModuleFunction> list = new ArrayList<>();
        if(functionIds.size()!=0){
            list = productModuleFunctionService.list(
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getId, functionIds));
            List<TestTaskCase> testTaskCaseList = testTaskCaseApi.queryByTaskId(bizId);
            List<Long> testCaseIds = testTaskCaseList.stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
            List<TestProductCase> productCases = testProductCaseApi.queryProductCaseByIds(testCaseIds);
            List<Long> moduleFunctionIds= productCases.stream().map(TestProductCase::getModuleFunctionId).filter(Objects::nonNull).collect(Collectors.toList());
            for(ProductModuleFunction productModuleFunction:list){
                if(moduleFunctionIds.contains(productModuleFunction.getId())){
                    productModuleFunction.setDisable(true);
                }else{
                    productModuleFunction.setDisable(false);
                }
            }

        }

        return list;
    }

    @GetMapping("/getProductModuleFunctionsTreeByBizId/{bizId}")
    @ApiOperation(value = "根据业务id获取交易树信息")
    public R<List<ProductModuleFunction>> getProductModuleFunctionsTreeByBizId(@PathVariable("bizId")Long bizId){
        List<Long> functionIds =
                baseService.list(Wraps.<ProductModuleFunctionItem>lbQ().eq(ProductModuleFunctionItem::getBizId, bizId))
                        .stream().map(ProductModuleFunctionItem::getModuleFunctionId).collect(Collectors.toList());
        List<ProductModuleFunction> list = new ArrayList<>();
        if(functionIds.size()!=0){
            list = productModuleFunctionService.list(
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getId, functionIds));
        }
        return success(TreeUtil.buildTree(list));
    }

    @GetMapping("/getProductModuleFunctionCaseTree/{bizId}")
    @ApiOperation(value = "根据业务id获取系统交易用例树")
    public R<List<ProductModuleFunction>> getProductModuleFunctionCaseTree(@PathVariable("bizId")Long bizId){
        List<Long> functionIds =
                baseService.list(Wraps.<ProductModuleFunctionItem>lbQ().eq(ProductModuleFunctionItem::getBizId, bizId))
                        .stream().map(ProductModuleFunctionItem::getModuleFunctionId).collect(Collectors.toList());
        List<ProductModuleFunction> resultTree = new ArrayList<>();
        if(functionIds.size()!=0){
            List<ProductModuleFunction> list = productModuleFunctionService.list(
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getId, functionIds));

//            TestProductCasePageQuery data = new TestProductCasePageQuery();
//            data.setTaskId(bizId);
//            data.setFunctionIds(functionIds);
            List<TestTaskCase> testTaskCaseList = testTaskCaseApi.queryByFunctionIds(bizId,functionIds);
//            List<TestProductCase> productCases = testProductCaseApi.queryByFunctionIds(data);
            for(TestTaskCase testTaskCase:testTaskCaseList){
                ProductModuleFunction productModuleFunction = new ProductModuleFunction();
//                TestTaskCase taskCase = testTaskCaseApi.getByTestCaseIdAndTaskId(productCase.getId(),bizId);
                productModuleFunction.setName(testTaskCase.getName());
                productModuleFunction.setId(testTaskCase.getTestcaseId());
                productModuleFunction.setTestTaskCaseId(testTaskCase.getId());
                productModuleFunction.setParentId(testTaskCase.getModuleFunctionId());
                productModuleFunction.setNodeType(3);
                productModuleFunction.setVersion(testTaskCase.getVersion());

                list.add(productModuleFunction);
            }

            // 批量加载产品信息（解决N+1查询）
            List<Long> productIds = list.stream()
                    .map(ProductModuleFunction::getProductId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, ProductInfo> productInfoMap = productInfoService.listByIds(productIds).stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ProductInfo::getId, Function.identity()));
            // 构建树形结构
            List<ProductModuleFunction> tree = TreeUtil.buildTree(list);
            // 按产品分组并组装树节点
            Map<Long, List<ProductModuleFunction>> functionMap = tree.stream()
                    .collect(Collectors.groupingBy(ProductModuleFunction::getProductId));

            functionMap.forEach((productId, funcList) -> {
                ProductInfo productInfo = productInfoMap.get(productId);
                if (productInfo != null) {
                    ProductModuleFunction rootNode = new ProductModuleFunction();
                    rootNode.setId(productInfo.getId());
                    rootNode.setName(productInfo.getName());
                    rootNode.setNodeType(0);
                    funcList.forEach(func -> func.setParentId(productId)); // 设置父ID
                    rootNode.setChildren(funcList);
                    resultTree.add(rootNode);
                }
            });
        }
        return success(resultTree);
    }

    @GetMapping("/uncheckModuleFunction/{bizId}/{moduleFunctionId}")
    @ApiOperation(value = "取消勾选维护范围校验")
    public R<Boolean> uncheckModuleFunction(@PathVariable("bizId")Long bizId,@PathVariable("moduleFunctionId")Long moduleFunctionId){

        ProductModuleFunctionItem productModuleFunctionItem = baseService.getOne(Wraps.<ProductModuleFunctionItem>lbQ()
                .eq(ProductModuleFunctionItem::getModuleFunctionId,moduleFunctionId)
                .eq(ProductModuleFunctionItem::getBizId,bizId));

        //手工执行
        if(TaskTypeCodeConstants.TASK_MANUAL.equals(productModuleFunctionItem.getBizType())){
            List<TestTaskCase> list = testTaskCaseApi.getByFunctionIdAndTaskId(moduleFunctionId,bizId);
            if(list.size()>0){
                return R.validFail("交易已关联执行用例，移除关联失败");
            }
        }else if(TaskTypeCodeConstants.TASK_CASE_DESIGN.equals(productModuleFunctionItem.getBizType())){
            List<TestProductCase> list = testProductCaseApi.getProductCaseByFunctionIdAndTaskId(moduleFunctionId,bizId);
            if(list.size()>0){
                return R.validFail("交易已关联测试用例，移除关联失败");
            }
        }else{
            List<TestRequirementFunctionPoints> list = testRequirementFunctionPointsApi.getPointsByFunctionIdAndTaskId(moduleFunctionId,bizId);
            if(list.size()>0){
                return R.validFail("交易已关联测试点，移除关联失败");
            }
        }


        return success(true);
    }





}
