package com.jettech.jettong.product.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.dto.sys.dictionary.SysDictionaryRelationSaveDTO;
import com.jettech.jettong.product.service.*;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.product.dto.ProductInfoPageQuery;
import com.jettech.jettong.product.dto.ProductInfoSaveDTO;
import com.jettech.jettong.product.dto.ProductInfoUpdateDTO;
import com.jettech.jettong.product.entity.ProductApplication;
import com.jettech.jettong.product.entity.ProductConnect;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.google.common.collect.Maps;
import com.jettech.jettong.product.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType.PRODUCT;
import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 产品/系统实例表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品/系统实例表控制器
 * @projectName jettong
 * @package com.jettech.jettong.product.controller
 * @className ProductInfoController
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/product/productInfo")
@Api(value = "ProductInfo", tags = "产品-系统实例表")
@PreAuth(replace = "product:productInfo:")
@RequiredArgsConstructor
public class ProductInfoController extends
        SuperController<ProductInfoService, Long, ProductInfo, ProductInfoPageQuery, ProductInfoSaveDTO,
                ProductInfoUpdateDTO>
{

    private final EchoService echoService;
    private final ProductApplicationService productApplicationService;
    private final ProductModuleFunctionService productModuleFunctionService;
    private final ProductConnectService productConnectService;
    private final ProductVersionService productVersionService;
    private final PersonalizedTableViewApi tableViewApi;
    private final DictionaryApi dictionaryApi;

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.removeByIdAssociatedData(ids);
        return success();
    }

    @Override
    public R<ProductInfo> get(Long id)
    {
        ProductInfo produpctInfo = baseService.getById(id);
        // 手动注入
        List<ProductConnect> productConnects =
                productConnectService.list(Wraps.<ProductConnect>lbQ().in(ProductConnect::getProductId, id));
        echoService.action(produpctInfo);
        List<Long> productInfos = new ArrayList<>();
        if(productConnects.size() != 0)
        {
            productConnects.forEach(item ->
            {
                productInfos.add(baseService.getById(item.getProductConnectId()).getId());
            });
        }
        Map<String, Object> echoMap = produpctInfo.getEchoMap();
        echoMap.put("productInfo",productInfos);
//        List<ProductProductset> productsetByProductId = productSetInfoApi.getProductsetByProductId(id);
//        List<Long> productsets = new ArrayList<>();
//        if(productsetByProductId.size() != 0)
//        {
//            productsetByProductId.forEach(item ->
//            {
//                productsets.add(item.getProductsetId());
//            });
//        }
//        echoMap.put("productset",productsets);
        echoService.action(productInfos);
        return success(produpctInfo);
    }

    @Override
    public R<ProductInfo> handlerUpdate(ProductInfoUpdateDTO model)
    {
        ProductInfo productInfo = BeanPlusUtil.toBean(model, ProductInfo.class);
        ProductInfo productInfo1 = baseService.getById(model.getId());
        if(!productInfo1.getName().toLowerCase().equals(productInfo.getName().toLowerCase()) && baseService.list(Wraps.<ProductInfo>lbQ().eq(ProductInfo::getName,productInfo.getName())).size() != 0){
            return R.validFail("产品名称【"+productInfo.getName()+"】已经存在");
        }

        productConnectService.remove(Wraps.<ProductConnect>lbQ().in(ProductConnect::getProductId, productInfo.getId()));
        productConnectService.remove(Wraps.<ProductConnect>lbQ().in(ProductConnect::getProductConnectId, productInfo.getId()));
       // productSetInfoApi.deleteByProductId(productInfo.getId());
        baseService.updateAllById(productInfo);
        SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                .setBizId(productInfo.getId())
                .setBizType(PRODUCT.getCode())
                .setDictionaryIds(productInfo.getTestModes());
        dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);

        if(model.getProduct()!=null && model.getProduct().size() != 0){
            model.getProduct().forEach(item ->
            {
                productConnectService.save(ProductConnect.builder().productId(productInfo.getId()).productConnectId(item).build());
                productConnectService.save(ProductConnect.builder().productId(item).productConnectId(productInfo.getId()).build());
            });
        }
//        if (model.getProductset()!=null && model.getProductset().size() != 0){
//            model.getProductset().forEach(item ->
//            {
//                productSetInfoApi.addProductProductSet(ProductProductset.builder().productId(productInfo.getId()).productsetId(item).build());
//            });
//        }
        return R.success(productInfo);
    }

    /**
     * 重写保存逻辑
     *
     * @param data 产品DTO
     * @return 数据
     */
    @Override
    public R<ProductInfo> handlerSave(ProductInfoSaveDTO data)
    {
        if(baseService.list(Wraps.<ProductInfo>lbQ().eq(ProductInfo::getName,data.getName())).size() != 0){
            return R.validFail("产品名称【"+data.getName()+"】已经存在");
        }
        ProductInfo productInfo = BeanUtil.toBean(data, ProductInfo.class);
        ProductInfo product = baseService.saveProductInfo(productInfo);
        SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                .setBizId(productInfo.getId())
                .setBizType(PRODUCT.getCode())
                .setDictionaryIds(productInfo.getTestModes());
        dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);
        if(data.getProduct()!=null && data.getProduct().size() != 0){
            data.getProduct().forEach(item ->
            {
                productConnectService.save(ProductConnect.builder().productId(product.getId()).productConnectId(item).build());
                productConnectService.save(ProductConnect.builder().productId(item).productConnectId(product.getId()).build());
            });
        }
//        if (data.getProductset()!=null && data.getProductset().size() != 0){
//            data.getProductset().forEach(item ->
//            {
//                productSetInfoApi.addProductProductSet(ProductProductset.builder().productId(product.getId()).productsetId(item).build());
//            });
//        }
        return success(productInfo);
    }

    @Override
    public IPage<ProductInfo> query(PageParams<ProductInfoPageQuery> params)
    {
        IPage<ProductInfo> page = params.buildPage(ProductInfo.class);
        ProductInfoPageQuery applicationPage = params.getModel();

        Map<String, Object> map = BeanPlusUtil.beanToMap(applicationPage);

        if (null == applicationPage.getOrgId())
        {
            map.put("orgId", getOrgId());
        }

        baseService.findPage(page, map);

        // 查询产品集信息
//        Set<Long> productIds = page.getRecords().stream().map(ProductInfo::getId).collect(Collectors.toSet());
//        if (!productIds.isEmpty())
//        {
//            Map<Long, List<ProductSetInfo>> productSetMaps = productSetInfoApi.findProductSetByProductIds(productIds);
//
//            page.getRecords().forEach(item ->
//            {
//                if (productSetMaps.containsKey(item.getId()))
//                {
//                    item.getEchoMap().put("productSets", productSetMaps.get(item.getId()));
//                }
//            });
//        }
        
        // 获取产品测试模式关联关系
        if (!page.getRecords().isEmpty()) {
            // 为每个产品单独查询并设置测试模式
            page.getRecords().forEach(product -> {
                List<Dictionary> dictionaries = dictionaryApi.echoDictionaryByBizIdAndType(
                        product.getId(), PRODUCT.getCode());
                if (dictionaries != null && !dictionaries.isEmpty()) {
                    List<Long> testModes = dictionaries.stream()
                            .map(Dictionary::getId)
                            .collect(Collectors.toList());
                    product.setTestModes(testModes);
                }
            });
        }
        
        // 手动注入
        echoService.action(page);

        // 保存最后一次查询的视图
        tableViewApi.saveLastSearch(getUserId(), "product-card", params);

        return page;
    }

    @Override
    public R<List<ProductInfo>> query(ProductInfo productInfo)
    {
        Map<String, Object> map = BeanPlusUtil.beanToMap(productInfo);
        if (null == productInfo.getOrgId())
        {
            map.put("orgId", getOrgId());
        }
        List<ProductInfo> list = baseService.findByParam(map);
        echoService.action(list);
        return success(list);
    }

    /**
     * 获取产品关联的服务应用数量及模块数量、功能点数量
     *
     * @param productId 产品id
     * @return Map<String, Object>
     * <AUTHOR>
     * <AUTHOR>
     * @date 2021/12/02 14:29
     * @update lxr 2021/12/02 14:29
     */
    @ApiOperation(value = "获取产品关联的服务应用数量及模块数量、功能点数量", notes = "获取产品关联的服务应用数量及模块数量、功能点数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品id", required = true, dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)})
    @GetMapping(value = "/getApplicationCountAndModuleFunctionCount")
    @SysLog(value = "获取产品关联的服务应用数量及模块数量、功能点数量", request = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<Map<String, Object>> getApplicationCountAndModuleFunctionCount(
            @RequestParam(value = "productId") Long productId)
    {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
        //服务应用数
        Long applicationCount = productApplicationService.count(Wraps.<ProductApplication>lbQ()
                .eq(ProductApplication::getProductId, productId));
        map.put("applicationCount", applicationCount);
        //产品模块功能数
        Long moduleCount = productModuleFunctionService.count(Wraps.<ProductModuleFunction>lbQ()
                .eq(ProductModuleFunction::getProductId, productId)
                .eq(ProductModuleFunction::getNodeType, 1));
        Long functionCount = productModuleFunctionService.count(Wraps.<ProductModuleFunction>lbQ()
                .eq(ProductModuleFunction::getProductId, productId)
                .eq(ProductModuleFunction::getNodeType, 2));
        map.put("moduleCount", moduleCount);
        map.put("functionCount", functionCount);
        return success(map);
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "code", value = "标识，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY)
    })
    @ApiOperation(value = "下拉框条件查询产品列表无权限", notes = "下拉框条件查询产品列表无权限")
    @PostMapping("/queryListByCondition")
    @SysLog("下拉框条件查询产品列表无权限")
    public R<List<ProductInfo>> queryListByCondition(@ApiIgnore @RequestBody ProductInfo productInfo)
    {
        LbqWrapper<ProductInfo> wrapper = Wraps.lbQ();
        wrapper.like(ProductInfo::getName, productInfo.getName())
                .like(ProductInfo::getCode, productInfo.getCode())
                .orderByDesc(ProductInfo::getUpdateTime);
        List<ProductInfo> list = baseService.list(wrapper);
        return success(list);
    }



    @ApiOperation(value = "根据产品集id查询产品分页信息", notes = "根据产品集id查询产品分页信息")
    @PostMapping("/queryListByProductSetId")
    @SysLog("根据产品集id查询产品信息")
    public IPage<ProductInfo> queryListByProductSetId(@RequestBody PageParams<ProductInfoPageQuery> params)
    {
        IPage<ProductInfo> page = params.buildPage(ProductInfo.class);
        ProductInfoPageQuery productInfoPageQuery = params.getModel();
        Long productSetId = productInfoPageQuery.getProductSetId();
       // List<Long> ids = productSetInfoApi.findProductIdsById(productSetId);
        LbqWrapper<ProductInfo> wrapper = Wraps.lbQ();
//        if(ids.size() != 0){
//            wrapper.in(ProductInfo::getId,ids);
//
//            Map<String, Object> map = BeanPlusUtil.beanToMap(productInfoPageQuery);
//
//            if (null == productInfoPageQuery.getOrgId())
//            {
//                map.put("orgId", getOrgId());
//            }
//            baseService.page(page,wrapper);
//            // 手动注入
//            echoService.action(page);
//        }

        return page;
    }

//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "id", value = "标识，模糊匹配", dataType = DATA_TYPE_LONG,
//                    paramType = PARAM_TYPE_PATH)
//    })
//    @ApiOperation(value = "根据产品集id查询产品下拉框信息", notes = "根据产品集id查询产品下拉框信息")
//    @PostMapping("/queryListByProductSetId/{id}")
//    @SysLog("根据产品集id查询产品下拉框信息")
//    public R<List<ProductTemp>> queryListByProductSet(@PathVariable("id") Long id)
//    {
//        List<Long> productIdsById = productSetInfoApi.findProductIdsById(id);
//        List<ProductInfo> list = baseService.list();
//        List<ProductTemp> productInfos = new ArrayList<>();
//        list.forEach(item ->
//        {
//            if(productIdsById.contains(item.getId())){
//                productInfos.add(new ProductTemp(item.getId(),item.getName(),1));
//            }else {
//                productInfos.add(new ProductTemp(item.getId(),item.getName(),0));
//            }
//        });
//        return success(productInfos);
//    }

    @ApiOperation(value = "根据产品id查询可关联的产品", notes = "根据产品集id查询可关联的产品")
    @PostMapping("/queryListByProductId")
    @SysLog("根据产品集id查询可关联的产品")
    public R<List<ProductInfo>> queryListByProductId(@RequestBody ProductInfo productInfo)
    {
        Long id = productInfo.getId();
        List<ProductInfo> list = baseService.list();
        List<ProductInfo> boys = list.stream().filter(s-> !s.getId().equals(id)).collect(Collectors.toList());
        return success(boys);
    }

    @ApiOperation(value = "下拉框模糊查询五条产品", notes = "下拉框模糊查询五条产品")
    @PostMapping("/queryFiveProduct")
    @SysLog("下拉框模糊查询五条产品")
    public R<List<ProductInfo>> queryFiveProduct(@RequestBody ProductInfo productInfo)
    {
        List<ProductInfo> list = baseService.list(Wraps.<ProductInfo>lbQ().like(ProductInfo::getName,productInfo.getName()));
        return success(list);
    }


    @ApiOperation(value = "产品拓扑图信息", notes = "产品拓扑图信息、功能点数量")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品id", required = true, dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)})
    @GetMapping(value = "/getProductMessage")
    @SysLog(value = "产品拓扑图信息", request = false)
    @PreAuth("hasAnyPermission('{}view')")
    public R<Map<String, Object>> getProductMessage(
            @RequestParam(value = "productId") Long productId)
    {
        List<ProductConnect> products =
                productConnectService.list(Wraps.<ProductConnect>lbQ().eq(ProductConnect::getProductId, productId));

        HashMap<String, Object> data = new HashMap<>();
        ArrayList<Object> nodes = new ArrayList<>();
        data.put("nodes",nodes);
        ArrayList<Object> edges = new ArrayList<>();
        data.put("edges",edges);
        for (ProductConnect item : products)
        {
            HashMap<Object, Object> idname = new HashMap<>();
            idname.put("id", item.getProductConnectId());
            idname.put("name",baseService.getById(item.getProductConnectId()).getName());
            idname.put("icon",baseService.getById(item.getProductConnectId()).getIcon());
            nodes.add(idname);

            HashMap<Object, Object> target = new HashMap<>();
            target.put("source",item.getProductConnectId());
            target.put("target",item.getProductId());
            edges.add(target);
        }
        HashMap<Object, Object> product = new HashMap<>();
        product.put("id", productId);
        product.put("name",baseService.getById(productId).getName());
        product.put("icon",baseService.getById(productId).getIcon());
        nodes.add(product);
        return R.success(data);

    }

    @ApiOperation(value = "产品版本看板", notes = "产品版本看板")
    @GetMapping("/getProductVersionKanBan")
    @SysLog(value = "产品版本看板")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<ProductInfo>> getProductVersionKanBan()
    {
        return success(baseService.getProductVersionKanBan());
    }
}
