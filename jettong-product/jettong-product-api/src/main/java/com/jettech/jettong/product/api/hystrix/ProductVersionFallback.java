package com.jettech.jettong.product.api.hystrix;

import com.jettech.jettong.product.api.ProductVersionApi;
import com.jettech.jettong.product.entity.ProductVersion;
import com.jettech.jettong.product.vo.ProductVersionVO;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品版本相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品版本相关API熔断
 * @package com.jettech.jettong.cmdb.hystrix
 * @className ProductVersionFallback
 * @date 2021/11/12 11:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ProductVersionFallback implements ProductVersionApi
{

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public ProductVersion findById(Long id)
    {
        return null;
    }

    @Override
    public List<ProductVersion> selectProductVersionByProductIds(List<Long> productIds)
    {
        return Collections.emptyList();
    }

    @Override
    public List<ProductVersionVO> findproductVersionAll(){
        return Collections.emptyList();
    }

}
