package com.jettech.jettong.product.api;

import com.jettech.basic.base.R;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductApplicationFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品与服务应用关联相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品与服务应用关联相关API
 * @package com.jettech.jettong.product.api
 * @className ProductApplicationApi
 * @date 2021/11/11 11:27
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", fallback = ProductApplicationFallback.class
        , path = "/product/productApplication")
public interface ProductApplicationApi extends LoadService
{


    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    @DeleteMapping("/removeProductApplicationByIds")
    R<Boolean> removeProductApplicationByIds(@RequestBody List<Long> ids);

    /**
     * 根据产品id获取关联的服务应用id
     *
     * @param productId 产品id
     * @return List<Long>
     */
    @GetMapping("/getApplicationIdListByProductId")
    List<Long> getApplicationIdListByProductId(@RequestParam(value = "productId") Long productId);
}
