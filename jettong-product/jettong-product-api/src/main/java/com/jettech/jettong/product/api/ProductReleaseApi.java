package com.jettech.jettong.product.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductReleaseFallback;
import com.jettech.jettong.product.dto.ProductReleaseSaveDTO;
import com.jettech.jettong.product.entity.ProductRelease;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * 产品相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品相关API
 * @package com.jettech.jettong.product.api
 * @className ProductInfoApi
 * @date 2021/11/12 09:53
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", fallback = ProductReleaseFallback.class
        , path = "/product/productInfo")
public interface ProductReleaseApi extends LoadService
{

    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 供发布管理测使用，发布管理发布成功后需要插入数据
     * @param model
     * @return {@link ProductRelease}
     * @throws
     * <AUTHOR>
     * @date 2022/9/20 16:27
     * @update xmy 2022/9/20 16:27
     * @since 1.0
     */
    @PostMapping("/echo/saveProductRelease")
    ProductRelease saveProductRelease(@RequestBody ProductReleaseSaveDTO model);
}
