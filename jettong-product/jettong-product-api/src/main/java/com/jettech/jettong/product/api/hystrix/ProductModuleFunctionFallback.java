package com.jettech.jettong.product.api.hystrix;

import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductModuleFunctionItem;
import com.jettech.jettong.product.vo.ProductModuleFunctionVO;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品功能模块相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块相关API熔断
 * @package com.jettech.jettong.product.api.hystrix
 * @className ProductModuleFunctionFallback
 * @date 2022/09/13 10:35
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ProductModuleFunctionFallback implements ProductModuleFunctionApi
{

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<ProductModuleFunction> getProductModuleFunctionListByIds(List<Long> ids)
    {
        return Collections.emptyList();
    }

    @Override
    public List<ProductModuleFunctionVO> findProductModuleAll()
    {
        return Collections.emptyList();
    }

    @Override
    public ProductModuleFunction findProductModuleFunctionById(Long id)
    {
        return new ProductModuleFunction();
    }

    @Override
    public void addOrUpdateProductModuleFunction(List<ProductModuleFunction> productModuleFunctions)
    {

    }

    @Override
    public void addProductModuleFunctionItem(List<ProductModuleFunctionItem> productModuleFunctionItems)
    {

    }

    @Override
    public void deleteProductModuleFunctionItemByBizId(List<Long> bizIds)
    {

    }

    @Override
    public String findProductInfoCodeByFunctionId(Long id) {
        return null;
    }

    @Override
    public List<ProductModuleFunction> findByProjectId(Long projectId, Long bizId, Long reqId, Boolean isWHL)
    {
        return Collections.emptyList();
    }
}
