package com.jettech.jettong.product.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductModuleFunctionFallback;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductModuleFunctionItem;
import com.jettech.jettong.product.vo.ProductModuleFunctionVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品功能模块相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块相关API
 * @package com.jettech.jettong.product.api
 * @className ProductModuleFunctionApi
 * @date 2022/09/13 10:35
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", fallback = ProductModuleFunctionFallback.class
        , path = "/product/productModuleFunction")
public interface ProductModuleFunctionApi extends LoadService
{

    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据模块功能id集合查询模块功能信息
     *
     * @param ids 模块功能id
     * @return List<ProductModuleFunction> 模块功能信息
     * <AUTHOR>
     * @date 2022/09/13 15:49
     * @update lxr 2022/09/13 15:49
     * @since 1.0
     */
    @PostMapping("/getProductModuleFunctionListByIds")
    List<ProductModuleFunction> getProductModuleFunctionListByIds(@RequestBody List<Long> ids);

    /**
     * 查询所有产品功能模块
     * @return List<ProductModuleFunctionVO> 产品功能模块信息
     * <AUTHOR>
     * @date 2022/12/24 17:07
     * @update xby 2022/12/24 17:07
     * @since 1.0
     */
    @GetMapping("/echo/findAll")
    List<ProductModuleFunctionVO> findProductModuleAll();

    @GetMapping("/echo/findProductModuleFunctionById/{id}")
    ProductModuleFunction findProductModuleFunctionById(@PathVariable("id") Long id);

    /**
     * 新增或修改
     *
     * @return List<ProductModuleFunction> 产品功能模块信息
     * <AUTHOR>
     * @date 2022/12/24 17:07
     * @update xby 2022/12/24 17:07
     * @since 1.0
     */
    @PostMapping("/echo/addOrUpdateProductModuleFunction")
    void addOrUpdateProductModuleFunction(@RequestBody List<ProductModuleFunction> productModuleFunctions);
    /**
     * 批量新增工作项关联交易信息(新增前会根据业务id删除数据)
     * @return List<ProductModuleFunctionItem> 批量新增工作项关联交易信息
     * <AUTHOR>
     * @date 2025/08/22 17:07
     * @update xby 2022/12/24 17:07
     * @since 1.0
     */
    @PostMapping("/echo/addProductModuleFunctionItem")
    void addProductModuleFunctionItem(@RequestBody List<ProductModuleFunctionItem> productModuleFunctionItems);

    /**
     * 根据bizId和bizType删除
     * @return bizId , bizType
     * <AUTHOR>
     * @date 2025/08/22 17:07
     * @update xby 2022/12/24 17:07
     * @since 1.0
     */
    @DeleteMapping("/echo/deleteProductModuleFunctionItemByBizId")
    void deleteProductModuleFunctionItemByBizId(@RequestBody List<Long> bizIds);


    @GetMapping("/echo/findProductInfoCodeByFunctionId/{id}")
    String findProductInfoCodeByFunctionId(@PathVariable("id") Long id);


    @GetMapping("/findByProjectId/{projectId}")
    List<ProductModuleFunction> findByProjectId(
            @PathVariable Long projectId,
            @RequestParam(value = "bizId", required = false) Long bizId,
            @RequestParam(value = "reqId", required = false) Long reqId,
            @RequestParam(value = "isWHL",required = false)Boolean isWHL);
}
