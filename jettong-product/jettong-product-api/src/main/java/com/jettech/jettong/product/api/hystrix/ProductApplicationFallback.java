package com.jettech.jettong.product.api.hystrix;

import com.jettech.basic.base.R;
import com.jettech.jettong.product.api.ProductApplicationApi;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品与服务应用关联相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品与服务应用关联相关API熔断
 * @package com.jettech.jettong.cmdb.hystrix
 * @className ProductApplicationFallback
 * @date 2021/11/11 11:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ProductApplicationFallback implements ProductApplicationApi
{
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public R<Boolean> removeProductApplicationByIds(List<Long> ids)
    {
        return R.timeout();
    }

    @Override
    public List<Long> getApplicationIdListByProductId(Long productId)
    {

        return Collections.emptyList();
    }
}
