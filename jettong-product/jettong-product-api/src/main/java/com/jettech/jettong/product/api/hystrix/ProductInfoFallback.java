package com.jettech.jettong.product.api.hystrix;

import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品相关API熔断
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品相关API熔断
 * @package com.jettech.jettong.cmdb.hystrix
 * @className ProductApplicationFallback
 * @date 2021/11/12 11:28
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class ProductInfoFallback implements ProductInfoApi
{

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }

    @Override
    public List<ProductInfo> findAll()
    {
        return Collections.emptyList();
    }

    @Override
    public ProductInfo selectProductInfoById(Long id) {
        return null;
    }

    @Override
    public ProductVersion selectProductVersionById(Long id) {
        return null;
    }

    @Override
    public List<ProductInfo> selectProductInfoByIds(List<Long> ids) {
        return null;
    }

    @Override
    public ProductModuleFunction selectProductModuleFunctionById(Long id) {
        return null;
    }

    @Override
    public List<ProductModuleFunction> selectProductModuleFunctionByProjectId(Long projectId)
    {
        return Collections.emptyList();
    }

    @Override
    public List<Long> findByProjectId(Long projectId, Long productModuleFunctionId) {
        return Collections.emptyList();
    }

    @Override
    public List<Long> findByFunctionId(Long productModuleFunctionId)
    {
        return Collections.emptyList();
    }

}
