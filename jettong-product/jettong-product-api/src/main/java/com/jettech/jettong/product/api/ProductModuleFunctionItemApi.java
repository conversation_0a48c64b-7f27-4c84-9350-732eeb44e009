package com.jettech.jettong.product.api;

import com.jettech.basic.echo.core.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductModuleFunctionItemFallback;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品模块功能项相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品模块功能项相关API
 * @package com.jettech.jettong.product.api
 * @className ProductModuleFunctionItemApi
 * @date 2025-09-03
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", 
             fallback = ProductModuleFunctionItemFallback.class,
             path = "/product/productModuleFunctionItem")
public interface ProductModuleFunctionItemApi extends LoadService {

    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据业务id获取交易信息
     * 
     * @param bizId 业务ID（任务ID）
     * @return List<ProductModuleFunction> 交易信息列表
     * <AUTHOR>
     * @date 2025-09-03
     */
    @GetMapping("/getProductModuleFunctionsByBizId/{bizId}")
    List<ProductModuleFunction> getProductModuleFunctionsByBizId(@PathVariable("bizId") Long bizId);
}
