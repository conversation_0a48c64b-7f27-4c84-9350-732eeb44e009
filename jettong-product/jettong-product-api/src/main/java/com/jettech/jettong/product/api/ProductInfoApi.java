package com.jettech.jettong.product.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductInfoFallback;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品相关API
 * @package com.jettech.jettong.product.api
 * @className ProductInfoApi
 * @date 2021/11/12 09:53
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", fallback = ProductInfoFallback.class
        , path = "/product/productInfo")
public interface ProductInfoApi extends LoadService
{

    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 查询所有产品
     * @return List<ProductInfo> 产品信息
     * <AUTHOR>
     * @date 2021/12/28 17:07
     * @update zxy 2021/12/28 17:07
     * @since 1.0
     */
    @GetMapping("/echo/findAll")
    List<ProductInfo> findAll();

    /**
     * 根据id查询产信息
     * @return ProductInfo 产品信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectProductInfoById")
    ProductInfo selectProductInfoById(@RequestParam(value = "id") Long id);

    /**
     * 根据id查询产品版本产信息
     * @return ProductInfo 产品信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectProductVersionById")
    ProductVersion selectProductVersionById(@RequestParam(value = "id") Long id);

    /**
     * 根据id查询产信息
     * @return ProductInfo 产品信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectProductInfoByIds")
    List<ProductInfo> selectProductInfoByIds(@RequestParam(value = "ids") List<Long> ids);

    /**
     * 根据id查询产信息
     * @return ProductInfo 产品信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectProductModuleFunctionById")
    ProductModuleFunction selectProductModuleFunctionById(@RequestParam(value = "id") Long id);
    /**
     * 根据id查询产信息
     * @return ProductInfo 产品信息
     * <AUTHOR>
     * @date 2022/02/08 18:07
     * @update lxr 2022/02/08 18:07
     * @since 1.0
     */
    @GetMapping("/echo/selectProductModuleFunctionByProjectId")
    List<ProductModuleFunction> selectProductModuleFunctionByProjectId(@RequestParam(value = "projectId") Long projectId);
    /**
     * 根据项目ID查询产品模块功能列表
     * @param projectId 项目ID
     * @return List<ProductModuleFunction> 产品模块功能列表
     * <AUTHOR>
     * @date 2025/01/27
     * @since 1.0
     */
    @GetMapping("/echo/findByProjectId")
    List<Long> findByProjectId(@RequestParam(value = "projectId") Long projectId,@RequestParam(value = "productModuleFunctionId") Long productModuleFunctionId);

    /**
     * 根据项目ID查询产品模块功能列表
     * @return List<ProductModuleFunction> 产品模块功能列表
     * <AUTHOR>
     * @date 2025/01/27
     * @since 1.0
     */
    @GetMapping("/echo/findByFunctionId")
    List<Long> findByFunctionId(@RequestParam(value = "functionId") Long functionId);


}
