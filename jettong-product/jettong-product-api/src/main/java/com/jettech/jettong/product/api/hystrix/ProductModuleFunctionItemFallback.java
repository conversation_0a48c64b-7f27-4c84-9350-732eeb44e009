package com.jettech.jettong.product.api.hystrix;

import com.jettech.jettong.product.api.ProductModuleFunctionItemApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品模块功能项API熔断降级处理
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品模块功能项API熔断降级处理
 * @package com.jettech.jettong.product.api.hystrix
 * @className ProductModuleFunctionItemFallback
 * @date 2025-09-03
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Component
public class ProductModuleFunctionItemFallback implements ProductModuleFunctionItemApi {

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        log.error("ProductModuleFunctionItemApi.findByIds 服务调用失败");
        return Collections.emptyMap();
    }

    @Override
    public List<ProductModuleFunction> getProductModuleFunctionsByBizId(Long bizId) {
        log.error("ProductModuleFunctionItemApi.getProductModuleFunctionsByBizId 服务调用失败, bizId: {}", bizId);
        return Collections.emptyList();
    }
}
