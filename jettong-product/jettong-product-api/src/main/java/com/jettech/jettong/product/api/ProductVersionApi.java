package com.jettech.jettong.product.api;

import com.jettech.basic.model.LoadService;
import com.jettech.jettong.product.api.hystrix.ProductVersionFallback;
import com.jettech.jettong.product.entity.ProductVersion;
import com.jettech.jettong.product.vo.ProductVersionVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 产品版本相关API
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品版本相关API
 * @package com.jettech.jettong.product.api
 * @className ProductVersionoApi
 * @date 2021/11/12 09:53
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.product-server:jettong-product-server}", fallback = ProductVersionFallback.class
        , path = "/product/productVersion")
public interface ProductVersionApi extends LoadService
{


    @Override
    @GetMapping("/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    /**
     * 根据产品版本id查询版本信息
     * @param id
     * @return {@link ProductVersion}
     * @throws
     * <AUTHOR>
     * @date 2022/2/16 17:14
     * @update xmy 2022/2/16 17:14
     * @since 1.0
     */
    @GetMapping("/echo/findById")
    ProductVersion findById(@RequestParam(value = "id") Long id);


    /**
     * 根据产品id集合获取关联的产品版本
     * @return ProductVersion 产品版本信息
     * <AUTHOR>
     * @date 2022/08/08 16:07
     * @update lxr 2022/08/08 16:07
     * @since 1.0
     */
    @PostMapping("/echo/selectProductVersionByProductIds")
    List<ProductVersion> selectProductVersionByProductIds(@RequestBody List<Long> productIds);

    /**
     * 查询所有产品版本
     * @return List<ProductVersion> 产品版本信息
     * <AUTHOR>
     * @date 2022/12/24 17:07
     * @update xby 2021/12/24 17:07
     * @since 1.0
     */
    @GetMapping("/echo/findAll")
    List<ProductVersionVO> findproductVersionAll();
}
