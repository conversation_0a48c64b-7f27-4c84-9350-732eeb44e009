package com.jettech.jettong.product.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.ORG_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 产品/系统实例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品/系统实例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.product.entity
 * @className ProductInfo
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("product_info")
@ApiModel(value = "ProductInfo", description = "产品/系统实例表")
@AllArgsConstructor
public class ProductInfo extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @NotEmpty(message = "请填写标识")
    @Size(max = 64, message = "标识长度不能超过64")
    @TableField(value = "`code`", condition = LIKE)
    @Excel(name = "标识")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 类型标识
     */
    @ApiModelProperty(value = "类型标识")
    @TableField(value = "`type_id`")
    @Excel(name = "类型标识")
    @Echo(api = "productTypeServiceImpl", beanClass = ProductType.class)
    private Long typeId;

    /**
     * 负责人ID
     */
    @ApiModelProperty(value = "负责人ID")
    @TableField(value = "`leading_by`")
    @Excel(name = "负责人ID")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    @TableField(value = "`org_id`")
    @Excel(name = "机构ID")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;

    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    @TableField(value = "`icon`")
    @Excel(name = "图标")
    private String icon;

    /**
     * 产品版本
     */
    @ApiModelProperty(value = "产品版本")
    @TableField(exist = false)
    private List<ProductVersion> productVersionList;

    @ApiModelProperty(value = "系统简称")
    @TableField(value = "`short_name`")
    @Excel(name = "系统简称")
    private String shortName;

    @ApiModelProperty(value = "测试方式")
    @Excel(name = "测试方式")
    @TableField(exist = false)
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.PROJECT_TESTMODE)
    private List<Long> testModes;

    @ApiModelProperty(value = "重要级别")
    @TableField(value = "`important_level`")
    @Excel(name = "重要级别")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.IMPORTANT_LEVEL)
    private String importantLevel;

    @ApiModelProperty(value = "开发负责人")
    @TableField(value = "`development_by`")
    @Excel(name = "开发负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long developmentBy;

    @ApiModelProperty(value = "行方开发经理")
    @TableField(value = "`development_manager`")
    @Excel(name = "行方开发经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long developmentManager;

    @ApiModelProperty(value = "SIT测试负责人")
    @TableField(value = "`sit_test_by`")
    @Excel(name = "SIT测试负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long sitTestBy;

    @ApiModelProperty(value = "UAT测试负责人")
    @TableField(value = "`uat_test_by`")
    @Excel(name = "UAT测试负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long uatTestBy;

    @Builder
    public ProductInfo(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String code, String name, String description, Long typeId, Long orgId ,String icon,
            String shortName,List<Long> testModes,String importantLevel,Long developmentBy,
            Long developmentManager,Long sitTestBy,Long uatTestBy)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.description = description;
        this.typeId = typeId;
        this.orgId = orgId;
        this.icon = icon;
        this.shortName = shortName;
        this.testModes = testModes;
        this.importantLevel = importantLevel;
        this.developmentBy = developmentBy;
        this.developmentManager = developmentManager;
        this.sitTestBy = sitTestBy;
        this.uatTestBy = uatTestBy;
    }

}
