package com.jettech.jettong.product.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.product.dto
 * @className ProductModuleFunctionItemSaveDTO
 * @date 2025-08-22
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductModuleFunctionItemSaveDTO", description = "")
public class ProductModuleFunctionItemSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;



    /**
     * 业务id：如任务id
     */
    @ApiModelProperty(value = "业务id：如任务id")
    @NotNull(message = "业务id")
    private Long bizId;

    /**
     * 业务类型：如任务
     */
    @ApiModelProperty(value = "业务类型：如任务")
    @Size(max = 50, message = "业务类型：如任务长度不能超过50")
    @NotNull(message = "请填写业务类型")
    private String bizType;

    /**
     * 交易id集合
     */
    /**
     * 交易id：交易id
     */
    @ApiModelProperty(value = "交易id")
    @NotNull(message = "交易id")
    private List<Long> functionIds;


    @ApiModelProperty(value = "添加方式")
    private String addType;
}
