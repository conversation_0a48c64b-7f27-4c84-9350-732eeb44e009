package com.jettech.jettong.product.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.DICTIONARY_ITEM_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PRODUCT_ID_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PRODUCT_MODULE_ID_CLASS;


/**
 * 产品功能模块功能表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块功能表实体类
 * @projectName jettong
 * @package com.jettech.jettong.product.entity
 * @className ProductModuleFunction
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("product_module_function")
@ApiModel(value = "ProductModuleFunction", description = "产品功能模块功能表")
@AllArgsConstructor
public class ProductModuleFunction extends TreeEntity<ProductModuleFunction, Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    @ApiModelProperty("父ID")
    @TableField("`parent_id`")
    @Echo(api = PRODUCT_MODULE_ID_CLASS,beanClass = ProductModuleFunction.class)
    protected Long parentId;

    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "名称不能为空")
    @Size(max = 255, message = "名称长度不能超过255")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    @TableField(value = "product_version_id")
    @Excel(name = "产品版本id")
    private Long productVersionId;

    /**
     * 父ID
     */
    @ApiModelProperty(value = "父标识")
    @TableField(exist = false)
    @Excel(name = "父标识")
    private String parentCode;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @NotEmpty(message = "请填写标识")
    @Size(max = 64, message = "标识长度不能超过64")
    @TableField(value = "`code`", condition = LIKE)
    @Excel(name = "标识")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    @TableField(value = "`product_id`")
    @Excel(name = "产品")
    @Echo(api = PRODUCT_ID_CLASS,beanClass = ProductInfo.class)
    private Long productId;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品名称")
    @TableField(exist = false)
    private String productName;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "用例版本")
    @TableField(exist = false)
    private String version;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型：1-模块；2-功能")
    @TableField(value = "`node_type`")
    @Excel(name = "节点类型")
    private Integer nodeType;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    @TableField(value = "`org_id`")
    @Excel(name = "机构ID")
    private Long orgId;

    /**
     * 投入
     */
//    @ApiModelProperty(value = "投入")
//    @TableField(value = "`investment`")
//    @Excel(name = "投入")
//    private Integer investment;

    /**
     * 价值
     */
    @ApiModelProperty(value = "价值")
    @TableField(value = "`worth`")
    @Excel(name = "价值")
    private Integer worth;


  /*  @ApiModelProperty(value = "产品经理")
    @Excel(name = "产品经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @TableField(value = "product_manager")
    private Long productManager;*/

    /*@ApiModelProperty(value = "研发经理")
    @Excel(name = "研发经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @TableField(value = "development_manager")
    private Long developmentManager;*/

    /**
     * 变更标识，update-修改，add-添加，delete-删除，为null则没有变更
     */
    @ApiModelProperty(value = "变更标识，update-修改，add-添加，delete-删除，为null则没有变更")
    @TableField(value = "update_state")
    private String updateState;

    /*@ApiModelProperty(value = "占比（投入/价值）")
    @TableField(exist = false)
    private String proportion;*/

    @ApiModelProperty(value = "矩形树图面积大小")
    @TableField(exist = false)
    private Integer value;


    @ApiModelProperty(value = "交易状态 1禁用0启用")
    @TableField(value = "`trade_status`")
    @Excel(name = "交易状态")
    private Integer tradeStatus;

    @ApiModelProperty(value = "交易类型")
    @TableField(value = "`trade_type`")
    @Excel(name = "交易类型")
    private String tradeType;

    @ApiModelProperty(value = "执行方式")
    @TableField(value = "`execution_method`")
    @Excel(name = "执行方式")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS,dictType = DictionaryType.EXECUTION_METHOD)
    private String executionMethod;


    @ApiModelProperty(value = "测试经理")
    @Excel(name = "测试经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @TableField(value = "test_manager")
    private Long testManager;

    @ApiModelProperty(value = "交易树返显禁用")
    @TableField(exist = false)
    private Boolean disable;

    @ApiModelProperty(value = "执行用例id")
    @TableField(exist = false)
    private Long testTaskCaseId;

    @Builder
    public ProductModuleFunction(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime,
            Long updatedBy, Long parentId, String parentCode, String name, Long productVersionId,
            String code, String description, //Long productManager, Long developmentManager,
            Long productId, Integer nodeType, Long orgId, //Integer investment,
            Integer worth, String updateState,
            Integer tradeStatus,String tradeType,String executionMethod,Long testManager)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.parentCode = parentCode;
        this.name = name;
        this.productVersionId = productVersionId;
        this.code = code;
        this.description = description;
        this.productId = productId;
//        this.productManager = productManager;
//        this.developmentManager = developmentManager;
        this.nodeType = nodeType;
        this.orgId = orgId;
//        this.investment = investment;
        this.worth = worth;
        this.updateState = updateState;
        this.tradeStatus = tradeStatus;
        this.tradeType = tradeType;
        this.executionMethod = executionMethod;
        this.testManager = testManager;
    }

}
