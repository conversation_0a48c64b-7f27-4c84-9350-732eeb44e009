package com.jettech.jettong.product.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;


/**
 * 产品版本表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品版本表实体类
 * @projectName jettong
 * @package com.jettech.jettong.product.entity
 * @className ProductVersion
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("product_version")
@ApiModel(value = "ProductVersion", description = "产品版本表")
@AllArgsConstructor
public class ProductVersion extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @Size(max = 64, message = "标识长度不能超过64")
    @TableField(value = "`code`", condition = LIKE)
    @Excel(name = "标识")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "名称")
    private String name;

    /**
     * 版本状态
     */
    @ApiModelProperty(value = "版本状态：1-未发布 2-已发布 3-发布失败")
    @NotNull(message = "请填写版本状态")
    @TableField(value = "`state_id`")
    @Excel(name = "版本状态")
    private Integer stateId;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @TableField(value = "`release_time`")
    @Excel(name = "发布时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime releaseTime;

    /**
     * 计划发布时间
     */
    @ApiModelProperty(value = "计划发布时间")
    @TableField(value = "`plan_rtime`")
    @Excel(name = "计划发布时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planRtime;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_stime`")
    @Excel(name = "计划开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planStime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`", condition = LIKE)
    @Excel(name = "描述")
    private String description;

    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    @NotNull(message = "请填写产品ID")
    @TableField(value = "`product_id`")
    @Excel(name = "产品ID")
    private Long productId;


    @TableField(exist = false)
    private String productName;
    /**
     * 机构id
     */
    @ApiModelProperty(value = "机构id")
    @TableField(value = "`org_id`")
    @Excel(name = "机构id")
    private Long orgId;


    @Builder
    public ProductVersion(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String code, String name, Integer stateId, LocalDateTime releaseTime, LocalDateTime planRtime,
            String description, Long productId, Long orgId, LocalDateTime planStime)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.stateId = stateId;
        this.releaseTime = releaseTime;
        this.planRtime = planRtime;
        this.description = description;
        this.productId = productId;
        this.orgId = orgId;
        this.planStime = planStime;
    }

}
