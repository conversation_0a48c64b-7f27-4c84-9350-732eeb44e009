package com.jettech.jettong.product.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 产品/系统实例表新增实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品/系统实例表新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.product.dto
 * @className ProductInfoSaveDTO
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductInfoSaveDTO", description = "产品/系统实例表")
public class ProductInfoSaveDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    @NotEmpty(message = "请填写标识")
    @Size(max = 64, message = "标识长度不能超过64")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 128, message = "名称长度不能超过128")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 类型标识
     */
    @ApiModelProperty(value = "类型标识")
    @NotNull(message = "请填写类型标识")
    private Long typeId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long orgId;
    /**
     * 产品ID
     */
    @ApiModelProperty(value = "产品ID")
    private List<Long> product;
    /**
     * 产品集ID
     */
    @ApiModelProperty(value = "产品集ID")
    private List<Long> productset;
    /**
     * 图标
     */
    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "系统简称")
    private String shortName;

    @ApiModelProperty(value = "测试方式")
    private List<Long> testModes;

    @ApiModelProperty(value = "重要级别")
    private String importantLevel;

    @ApiModelProperty(value = "开发负责人")
    private Long developmentBy;

    @ApiModelProperty(value = "行方开发经理")
    private Long developmentManager;

    @ApiModelProperty(value = "SIT测试负责人")
    private Long sitTestBy;

    @ApiModelProperty(value = "UAT测试负责人")
    private Long uatTestBy;
}
