<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.product.dao.ProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jettech.jettong.product.entity.ProductInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="org_id" jdbcType="BIGINT" property="orgId"/>
        <result column="leading_by" jdbcType="BIGINT" property="leadingBy"/>
        <result column="icon" jdbcType="LONGVARCHAR" property="icon"/>
        <result column="development_by" jdbcType="BIGINT" property="developmentBy"/>
        <result column="development_manager" jdbcType="BIGINT" property="developmentManager"/>
        <result column="sit_test_by" jdbcType="BIGINT" property="sitTestBy"/>
        <result column="uat_test_by" jdbcType="BIGINT" property="uatTestBy"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="important_level" jdbcType="VARCHAR" property="importantLevel"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        `id`
        ,`created_by`,`create_time`,`update_time`,`updated_by`,
        `code`, `name`, `description`, `type_id`, `org_id`,`leading_by`,`icon`,
        development_by,development_manager,sit_test_by,uat_test_by,short_name,important_level
    </sql>

    <select id="findPage" resultMap="BaseResultMap">
        select
        DISTINCT pi.*
        from `product_info` pi
        left join product_productset pp on pi.id = pp.product_id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="cm.name != null and cm.name != ''">
                and pi.`name` like concat('%', #{cm.name, jdbcType=VARCHAR},'%')
            </if>
            <if test="cm.code != null and cm.code != ''">
                and pi.`code` = #{cm.code, jdbcType=VARCHAR}
            </if>
            <if test="cm.leadingBy != null">
                and pi.`leading_by` = #{cm.leadingBy, jdbcType=VARCHAR}
            </if>
            <if test="cm.orgId != null">
                and (
                (pi.`org_id` in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or pi.`org_id` = #{cm.orgId, jdbcType=BIGINT})
                or exists (
                select
                cao.`product_id`
                from `product_org` cao
                where
                cao.`product_id` = pi.`id`
                and (cao.`org_id` in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or cao.`org_id` = #{cm.orgId, jdbcType=BIGINT})))
            </if>
            <if test="cm.productSetId != null">
                and pp.productset_id = #{cm.productSetId, jdbcType=BIGINT}
            </if>
        </trim>
        ORDER BY pp.productset_id
    </select>

    <select id="findByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `product_info` pi
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="cm.name != null and cm.name != ''">
                and pi.`name` like concat('%', #{cm.name, jdbcType=VARCHAR},'%')
            </if>
            <if test="cm.code != null and cm.code != ''">
                and pi.`code` = #{cm.code, jdbcType=VARCHAR}
            </if>
            <if test="cm.leadingBy != null">
                and pi.`leading_by` = #{cm.leadingBy, jdbcType=VARCHAR}
            </if>
            <if test="cm.orgId != null">
                and (
                (pi.`org_id` in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or pi.`org_id` = #{cm.orgId, jdbcType=BIGINT})
                or exists (
                select
                cao.`product_id`
                from `product_org` cao
                where
                cao.`product_id` = pi.`id`
                and (cao.`org_id` in (select child_id from sys_org_child where parent_id = #{cm.orgId, jdbcType=BIGINT}) or cao.`org_id` = #{cm.orgId, jdbcType=BIGINT})))
            </if>
        </trim>
        ORDER BY update_time DESC
    </select>
</mapper>
