package com.jettech.jettong.product.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.product.entity.*;
import com.jettech.jettong.product.service.*;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.CollHelper;
import com.jettech.jettong.alm.api.ProjectProductApi;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.product.dao.ProductInfoMapper;
import com.jettech.jettong.product.dao.ProductVersionMapper;
import com.jettech.jettong.product.entity.*;
import com.jettech.jettong.product.service.*;
import com.jettech.jettong.testm.api.ProductCaseLibraryConnectApi;
import com.jettech.jettong.testm.entity.ProductCaselibraryConnect;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType.PRODUCT;

/**
 * 产品/系统实例表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品/系统实例表业务层
 * @projectName jettong
 * @package com.jettech.jettong.product.service.impl
 * @className ProductInfoServiceImpl
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class ProductInfoServiceImpl extends SuperServiceImpl<ProductInfoMapper, ProductInfo>
        implements ProductInfoService
{

    private final ProductVersionMapper productVersionMapper;

    private final ProductModuleFunctionService productModuleFunctionService;

    private final ProductApplicationService productApplicationService;

    private final ProductOrgService productOrgService;

    private final UserApi userApi;

    private final ProductConnectService productConnectService;

    private final ProductCaseLibraryConnectApi productCaseLibraryConnectApi;

    private final ProjectProductApi projectProductApi;

    private final DictionaryApi dictionaryApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIdAssociatedData(List<Long> ids)
    {

        if (ids.isEmpty())
        {
            return;
        }
        List<ProductCaselibraryConnect> list = productCaseLibraryConnectApi.selectListByConnectId(ids);
        if (list.size() != 0){
            throw BizException.validFail("删除失败，当前产品被用例库关联");
        }
        ids.forEach(productId ->{

            List<ProjectInfo> projectInfos = projectProductApi.findProject(productId);
            if (CollUtil.isNotEmpty(projectInfos)){
                throw BizException.validFail("删除失败，当前产品被项目关联");
            }
        });


        //删除产品-产品关联信息
        productConnectService.remove(Wraps.<ProductConnect>lbQ().in(ProductConnect::getProductId, ids));
        productConnectService.remove(Wraps.<ProductConnect>lbQ().in(ProductConnect::getProductConnectId, ids));
        //删除产品-产品集关联信息
//        ids.forEach(item->
//        {
//            productSetInfoApi.deleteByProductId(item);
//        });
        //删除产品版本-产品-产品集版本关联信息
       // productSetInfoApi.deleteproductversionproductsetByPorductId(ids);
        //删除产品功能模块功能信息
        productModuleFunctionService.remove(
                Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getProductId, ids));
        //删除产品版本-产品关联信息
        productVersionMapper.delete(Wraps.<ProductVersion>lbQ().in(ProductVersion::getProductId, ids));
        //删除产品与服务应用关联关系
        productApplicationService.remove(Wraps.<ProductApplication>lbQ().in(ProductApplication::getProductId, ids));

        //删除机构关联信息
        productOrgService.remove(Wraps.<ProductOrg>lbQ().in(ProductOrg::getProductId, ids));
        //删除产品用例库中间表
        productCaseLibraryConnectApi.deleteByConnectId(ids);
        //删除字典与平台关联关系信息
        dictionaryApi.deleteDictionaryRelationByBizIdsAndTypes(
                ids,
                Collections.singletonList(PRODUCT.getCode())
        );
        //删除产品信息
        super.removeByIds(ids);
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        LbqWrapper<ProductInfo> wrapper = Wraps.lbQ();
        wrapper.select(ProductInfo.class,  field -> !"icon".equals(field.getProperty()));
        List<ProductInfo> list = super.list(wrapper);
        return CollHelper.uniqueIndex(list, ProductInfo::getId, application -> application);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(ProductInfo productInfo)
    {
        ProductInfo oldProductInfo = super.getById(productInfo.getId());

        if(oldProductInfo.getLeadingBy()!=productInfo.getLeadingBy()){
            if(productInfo.getLeadingBy() !=null ){
                User oldUser = userApi.findUserById(oldProductInfo.getLeadingBy());
                User user = userApi.findUserById(productInfo.getLeadingBy());
                if(user.getOrgId()!= oldUser.getOrgId()){
                    productOrgService.save(ProductOrg.builder().productId(productInfo.getId()).orgId(user.getOrgId()).build());
                }
            }
        }
        return super.updateById(productInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductInfo saveProductInfo(ProductInfo productInfo)
    {
        ArgumentAssert.isFalse(check(productInfo.getCode()), "产品标识【{}】已经存在", productInfo.getCode());
        boolean r = super.save(productInfo);
        //配置负责人权限问题
        if(productInfo.getLeadingBy() !=null )
        {
            User user = userApi.findUserById(productInfo.getLeadingBy());
            if(!user.getOrgId().equals(productInfo.getOrgId())){
                productOrgService.save(ProductOrg.builder().productId(productInfo.getId()).orgId(user.getOrgId()).build());
            }
        }
        return productInfo;
    }

    @Override
    public boolean check(String code)
    {
        //判断当前产品标识是否存在
        return count(Wraps.<ProductInfo>lbQ().eq(ProductInfo::getCode, code)) > 0;
    }

    @Override
    public IPage<ProductInfo> findPage(IPage<ProductInfo> page, Map<String, Object> map)
    {
        return baseMapper.findPage(page, map);
    }

    @Override
    public List<ProductInfo> findByParam(Map<String, Object> map)
    {
        return baseMapper.findByParam(map);
    }

    @Override
    public List<ProductInfo> getProductVersionKanBan() {

        //获取所有产品
        List<ProductInfo>  productInfoList= baseMapper.selectList(null);
        //获取所有版本
        List<ProductVersion> productVersionList= productVersionMapper.selectList(null);
        //根据产品分组产品版本
        Map<Long, List<ProductVersion>> productGroupMap =
                productVersionList.stream().collect(Collectors.groupingBy(ProductVersion::getProductId));
        productInfoList.forEach(item->{
            item.setIcon(null);
            item.setProductVersionList(productGroupMap.get(item.getId()));
        });
        return productInfoList;
    }

}
